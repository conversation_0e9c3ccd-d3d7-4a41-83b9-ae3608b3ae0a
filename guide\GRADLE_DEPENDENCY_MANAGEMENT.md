# Gradle 统一依赖版本管理

本项目使用 Gradle 的版本目录（Version Catalogs）功能来统一管理所有模块的依赖版本。

## 项目结构

```
beautiful-posture/
├── gradle/
│   └── libs.versions.toml          # 版本目录配置文件
├── build.gradle                    # 根项目构建文件
├── settings.gradle                 # 项目设置文件
├── admin-api/                      # 管理后台API模块
├── app-api/                        # 移动端API模块
├── client-api/                     # 客户端API模块
├── core/                           # 核心公共模块
└── service/                        # 业务服务模块
```

## 版本管理配置

### 1. 版本目录文件 (`gradle/libs.versions.toml`)

这个文件定义了所有依赖的版本号和依赖库的引用：

- `[versions]` - 定义版本号变量
- `[libraries]` - 定义具体的依赖库
- `[bundles]` - 定义依赖组合
- `[plugins]` - 定义插件

### 2. 根项目配置 (`build.gradle`)

- 定义全局配置（group、version、repositories）
- 配置子项目的通用设置
- 区分API模块和库模块的插件应用

### 3. 模块分类

- **API模块** (`*-api`): 应用Spring Boot插件，可以独立运行
- **库模块** (`core`, `service`): 使用java-library插件，提供给其他模块使用

## 如何使用

### 添加新依赖

1. 在 `gradle/libs.versions.toml` 中添加版本号：
```toml
[versions]
new-library = "1.0.0"
```

2. 在 `[libraries]` 中定义依赖：
```toml
[libraries]
new-library = { module = "com.example:new-library", version.ref = "new-library" }
```

3. 在模块的 `build.gradle` 中使用：
```gradle
dependencies {
    implementation libs.new.library
}
```

### 更新依赖版本

只需要在 `gradle/libs.versions.toml` 的 `[versions]` 部分修改版本号，所有使用该依赖的模块都会自动更新。

### 使用依赖组合

项目中定义了几个常用的依赖组合：

- `libs.bundles.spring.web` - Spring Web相关依赖
- `libs.bundles.spring.data` - 数据访问相关依赖
- `libs.bundles.spring.security.jwt` - 安全和JWT相关依赖
- `libs.bundles.tools` - 工具类依赖
- `libs.bundles.test` - 测试相关依赖

### 模块间依赖

使用 `project()` 函数引用其他模块：

```gradle
dependencies {
    implementation project(':core')
    implementation project(':service')
}
```

## 常用命令

```bash
# 查看所有项目
./gradlew projects

# 构建所有模块
./gradlew build

# 运行特定模块
./gradlew :admin-api:bootRun

# 查看依赖树
./gradlew :admin-api:dependencies

# 检查依赖更新
./gradlew dependencyUpdates
```

## 优势

1. **统一管理**: 所有依赖版本在一个文件中管理
2. **避免冲突**: 确保所有模块使用相同版本的依赖
3. **易于维护**: 更新版本只需修改一个地方
4. **类型安全**: IDE提供自动补全和类型检查
5. **模块化**: 不同类型的模块使用不同的插件配置

## 注意事项

1. 修改 `libs.versions.toml` 后需要重新同步项目
2. 新增依赖时建议先在版本目录中定义
3. 避免在子模块中硬编码版本号
4. 定期检查和更新依赖版本
