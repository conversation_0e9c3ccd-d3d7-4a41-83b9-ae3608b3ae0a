package com.deepaic.core.auth;

import com.deepaic.core.dto.MemberDTO;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 租户确定策略接口
 * 用于在会员注册时确定应该属于哪个租户
 *
 * <AUTHOR>
 */
public interface TenantDeterminationStrategy {

    /**
     * 根据登录信息确定租户
     *
     * @param loginDTO 登录信息
     * @param request HTTP请求
     * @return 租户编码
     */
    String determineTenant(MemberDTO.MemberLoginDTO loginDTO, HttpServletRequest request);

    /**
     * 策略优先级，数字越小优先级越高
     */
    int getPriority();
}
