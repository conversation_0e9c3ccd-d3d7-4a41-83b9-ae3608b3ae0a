package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 充值储值卡
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_recharge_paid_card")
public class RechargePaidCard extends BaseEntity {

    private Long rechargeId;

    private Long cardId;

    private String cardCode;

    private String cardName;

    private BigDecimal actualPrice;

    private BigDecimal salePrice;

    private BigDecimal projectDiscount;

    private BigDecimal productDiscount;
}
