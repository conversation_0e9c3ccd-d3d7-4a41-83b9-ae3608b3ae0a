package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 会员权益-项目次卡
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_project")
public class MemberProject extends BaseEntity {

    private Long memberId;

    private Long rechargeId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private String projectCategory;

    /**
     * 服务时长
     */
    private String serviceDuration;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Short quantity;

    /**
     * 赠送数量
     */
    private Short giftQuantity;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 1=未用完，2=已用完
     */
    private Short status;
}
