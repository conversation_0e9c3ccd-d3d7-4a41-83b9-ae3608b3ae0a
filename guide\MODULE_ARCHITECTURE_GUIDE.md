# 美姿姿 - 模块架构指南

## 概述

美姿姿项目采用多模块架构，清晰分离不同的业务职责和部署需求。

## 模块结构

所有API相关模块都位于 `api/` 目录下，便于统一管理和部署。

### 1. api/platform-api (SaaS平台管理API)
- **职责**: SaaS平台的管理功能
- **端口**: 8080
- **用户**: 平台管理员
- **功能**:
  - 租户管理
  - 平台用户管理
  - 系统配置
  - 数据统计
  - 平台监控

### 2. api/admin-api (客户系统管理后台API)
- **职责**: 客户租户的管理后台
- **端口**: 8081
- **用户**: 客户管理员
- **功能**:
  - 客户账号管理
  - 组织架构管理
  - 权限管理
  - 业务数据管理
  - 系统设置

### 3. api/app-api (小程序后端API)
- **职责**: 小程序的后端服务
- **端口**: 8082
- **用户**: 小程序用户(会员)
- **功能**:
  - 会员登录认证
  - 业务功能接口
  - 数据查询
  - 消息推送

### 4. api/core (核心模块)
- **职责**: 核心功能和工具类
- **类型**: 库模块
- **功能**:
  - 实体类定义
  - 工具类
  - 配置类
  - 异常处理
  - 常量定义

### 5. api/service (服务层模块)
- **职责**: 业务逻辑处理
- **类型**: 库模块
- **功能**:
  - 业务服务实现
  - 数据访问层
  - 缓存管理
  - 事务处理

## 架构优势

### 1. 清晰的职责分离
- 每个API模块专注于特定的用户群体和功能
- 避免功能混乱和权限交叉

### 2. 独立部署
- 各API模块可以独立部署到不同服务器
- 支持水平扩展和负载均衡

### 3. 安全隔离
- 不同模块使用不同的端口和配置
- 可以设置不同的安全策略

### 4. 维护便利
- 模块化开发，团队可以并行工作
- 问题定位更加精确

## 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   platform-api  │    │    admin-api    │    │     app-api     │
│   (SaaS平台)    │    │   (客户管理)    │    │   (小程序后端)   │
│     :8080       │    │     :8081       │    │     :8082       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   + Redis       │
                    │  (共享基础设施)   │
                    └─────────────────┘
```

## 数据库架构

### 公共表 (public schema)
- `pub_platform_account`: 平台用户账号
- `pub_customer_account`: 客户账号
- `pub_tenant`: 租户信息
- `pub_member_account`: 会员账号映射

### 租户表 (tenant_xxx schema)
- `sys_user`: 租户用户
- `sys_role`: 角色管理
- `sys_menu`: 菜单权限
- `sys_organization`: 组织架构
- `sys_member`: 会员信息
- 其他业务表...

## 认证架构

### 1. 平台认证 (platform-api)
- 使用 `pub_platform_account` 表
- Sa-Token 会话管理
- 平台级权限控制

### 2. 客户认证 (admin-api)
- 使用 `pub_customer_account` 表登录
- 获取租户信息后切换到租户schema
- 使用 `sys_user` 表进行权限验证

### 3. 会员认证 (app-api)
- 使用 `pub_member_account` 表进行跨租户查找
- 确定租户后切换到对应schema
- 使用 `sys_member` 表进行业务操作

## 开发指南

### 1. 新增功能
- 确定功能属于哪个模块
- 在对应的API模块中添加控制器
- 在service模块中实现业务逻辑
- 在core模块中定义实体和工具类

### 2. 跨模块调用
- 避免API模块之间直接调用
- 通过service模块共享业务逻辑
- 使用消息队列进行异步通信

### 3. 数据库操作
- 使用MyBatis-Plus进行数据访问
- 通过拦截器自动切换schema
- 注意公共表和租户表的区别

## 部署说明

### 1. 开发环境
```bash
# 启动所有服务
docker-compose -f docker/docker-compose.full.yml up -d
```

### 2. 生产环境
```bash
# 分别部署各个服务
docker-compose -f docker/docker-compose.platform-api.yml up -d
docker-compose -f docker/docker-compose.admin-api.yml up -d
docker-compose -f docker/docker-compose.app-api.yml up -d
```

### 3. 构建命令
```bash
# Linux/Mac
./docker/build.sh

# Windows
./docker/build.bat
```

## 监控和维护

### 1. 健康检查
- 每个API模块都提供健康检查接口
- 使用Actuator进行监控

### 2. 日志管理
- 各模块独立的日志配置
- 统一的日志格式和级别

### 3. 性能监控
- JVM监控
- 数据库连接池监控
- Redis连接监控

## 注意事项

1. **端口分配**: 确保各模块使用不同端口避免冲突
2. **数据库连接**: 合理配置连接池大小
3. **缓存策略**: 不同模块使用不同的Redis数据库
4. **安全配置**: 各模块独立的安全配置
5. **版本管理**: 保持各模块版本同步更新
