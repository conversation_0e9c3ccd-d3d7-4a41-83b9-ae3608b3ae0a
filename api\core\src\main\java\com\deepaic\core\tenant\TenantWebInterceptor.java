package com.deepaic.core.tenant;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 多租户Web拦截器 - Sa-Token Session版
 * 基于Sa-Token Session自动管理租户上下文
 *
 * 工作流程：
 * 1. 请求开始时检查用户登录状态
 * 2. 如果已登录且Session中没有租户信息，自动查询并设置
 * 3. 如果未登录，无需特殊处理（使用默认值）
 * 4. 请求结束时无需清理（Sa-Token自动管理）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantWebInterceptor implements HandlerInterceptor {

    private final TenantContextService tenantContextService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            // 检查是否为公共API（不需要租户上下文）
            if (isPublicApi(request)) {
                log.debug("公共API请求: {}", request.getRequestURI());
                return true;
            }

            // 如果用户已登录且Session中没有租户信息，自动设置
            if (StpUtil.isLogin() && !SaTokenTenantContext.hasValidTenantContext()) {
                tenantContextService.setTenantContextForUser(StpUtil.getLoginIdAsLong());
            }

            return true;
        } catch (Exception e) {
            log.error("设置租户上下文失败: uri={}", request.getRequestURI(), e);
            return true;
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 使用Sa-Token Session，无需手动清理租户上下文
        // Session会自动管理生命周期
        log.debug("请求完成: uri={}", request.getRequestURI());
    }



    /**
     * 判断是否为公共API
     * 公共API不需要租户上下文，直接使用public schema
     */
    private boolean isPublicApi(HttpServletRequest request) {
        String uri = request.getRequestURI().toLowerCase();

        // 认证相关API
        if (uri.contains("/auth/") ||
            uri.contains("/login") ||
            uri.contains("/register") ||
            uri.contains("/captcha")) {
            return true;
        }

        // 公共资源
        if (uri.contains("/public/") ||
            uri.contains("/static/") ||
            uri.contains("/assets/") ||
            uri.startsWith("/favicon") ||
            uri.startsWith("/robots")) {
            return true;
        }

        // 系统监控
        if (uri.contains("/actuator/") ||
            uri.contains("/health") ||
            uri.contains("/metrics")) {
            return true;
        }

        // API文档
        if (uri.contains("/swagger") ||
            uri.contains("/api-docs") ||
            uri.contains("/doc.html")) {
            return true;
        }

        return false;
    }
}
