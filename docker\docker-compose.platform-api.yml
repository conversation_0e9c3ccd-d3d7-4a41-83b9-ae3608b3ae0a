version: '3.8'

services:
  platform-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile.platform-api
    container_name: beautiful-posture-platform-api
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=beautiful_posture
      - DB_USERNAME=postgres
      - DB_PASSWORD=123456
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
    depends_on:
      - postgres
      - redis
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/platform-api/platform/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:17
    container_name: beautiful-posture-postgres
    environment:
      POSTGRES_DB: beautiful_posture
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts:/docker-entrypoint-initdb.d
    networks:
      - beautiful-posture-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: beautiful-posture-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - beautiful-posture-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  beautiful-posture-network:
    driver: bridge
