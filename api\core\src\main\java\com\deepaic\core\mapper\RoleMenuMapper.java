package com.deepaic.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepaic.core.entity.RoleMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单关联Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface RoleMenuMapper extends BaseMapper<RoleMenu> {

    /**
     * 批量插入角色菜单关联
     */
    int batchInsert(@Param("list") List<RoleMenu> roleMenuList);

    /**
     * 根据角色ID删除角色菜单关联
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据菜单ID删除角色菜单关联
     */
    int deleteByMenuId(@Param("menuId") Long menuId);

    /**
     * 根据角色ID查询菜单ID列表
     */
    List<Long> selectMenuIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据菜单ID查询角色ID列表
     */
    List<Long> selectRoleIdsByMenuId(@Param("menuId") Long menuId);

    /**
     * 检查角色是否拥有指定菜单权限
     */
    Integer checkRoleHasMenu(@Param("roleId") Long roleId, @Param("menuId") Long menuId);
}
