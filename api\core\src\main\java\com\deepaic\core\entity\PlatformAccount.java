package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("pub_platform_account")
public class PlatformAccount extends BaseEntity {

    private String username;

    private String password;

    private String email;

    private String phone;

    private String realName;

    private String avatar;

    private Short permissionLevel;

    private Short status;

    private LocalDateTime lastLoginTime;

    private String lastLoginIp;

    private Integer loginFailCount;

    private LocalDateTime lockTime;

    private LocalDateTime passwordExpireTime;

    private String remark;

    // 账户状态常量
    public static final short STATUS_DISABLED = 0;  // 禁用
    public static final short STATUS_ENABLED = 1;   // 启用
    public static final short STATUS_LOCKED = 2;    // 锁定

    // 账户类型常量
    public static final short TYPE_SUPER_ADMIN = 1;     // 超级管理员
    public static final short TYPE_PLATFORM_ADMIN = 2;  // 平台管理员
    public static final short TYPE_OPERATOR = 3;        // 运营人员
    public static final short TYPE_SUPPORT = 4;         // 技术支持

    /**
     * 检查账户是否启用
     */
    public boolean isEnabled() {
        return Short.valueOf(STATUS_ENABLED).equals(this.status);
    }

    /**
     * 检查账户是否锁定
     */
    public boolean isLocked() {
        return Short.valueOf(STATUS_LOCKED).equals(this.status);
    }

    /**
     * 检查是否为超级管理员
     */
    public boolean isSuperAdmin() {
        return Short.valueOf(TYPE_SUPER_ADMIN).equals(this.permissionLevel);
    }

    /**
     * 检查是否为平台管理员
     */
    public boolean isPlatformAdmin() {
        return Short.valueOf(TYPE_PLATFORM_ADMIN).equals(this.permissionLevel);
    }

    /**
     * 检查密码是否过期
     */
    public boolean isPasswordExpired() {
        return passwordExpireTime != null && passwordExpireTime.isBefore(LocalDateTime.now());
    }

    /**
     * 检查是否可以登录
     */
    public boolean canLogin() {
        return isEnabled() && !isLocked() && !isPasswordExpired();
    }
}
