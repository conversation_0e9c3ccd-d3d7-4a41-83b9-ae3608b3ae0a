package com.deepaic.core.util;

import lombok.*;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * Lombok使用示例工具类
 * 展示各种Lombok注解的使用方法
 * 
 * <AUTHOR> Posture Team
 */
@UtilityClass  // 工具类注解，自动生成私有构造函数，所有方法都是静态的
@Slf4j         // 自动生成日志对象 log
public class LombokExampleUtil {

    /**
     * 使用@SneakyThrows处理受检异常
     */
    @SneakyThrows
    public static void demonstrateSneakyThrows() {
        // 这里可以直接抛出受检异常，不需要try-catch或throws声明
        Thread.sleep(1000);
        log.info("SneakyThrows demonstration completed");
    }

    /**
     * 使用@Cleanup自动关闭资源
     */
    @SneakyThrows
    public static String readFileExample(String filename) {
        @Cleanup java.io.FileInputStream fis = new java.io.FileInputStream(filename);
        // 文件流会自动关闭，无需手动调用close()
        return "File content read successfully";
    }

    /**
     * 演示@NonNull注解的使用
     */
    public static String processNonNullString(@NonNull String input) {
        // 如果input为null，会自动抛出NullPointerException
        return input.toUpperCase();
    }

    /**
     * 演示Optional的使用
     */
    public static Optional<String> findUserById(Long id) {
        log.debug("Finding user by id: {}", id);
        
        if (id == null || id <= 0) {
            return Optional.empty();
        }
        
        // 模拟查找用户
        return Optional.of("User-" + id);
    }

    /**
     * 演示集合处理
     */
    public static void processUserList(@NonNull List<String> usernames) {
        log.info("Processing {} users", usernames.size());
        
        usernames.forEach(username -> {
            log.debug("Processing user: {}", username);
            // 处理用户逻辑
        });
        
        log.info("User processing completed");
    }

    /**
     * 内部类演示 - 使用@RequiredArgsConstructor
     */
    @RequiredArgsConstructor
    @Getter
    public static class ProcessResult {
        private final boolean success;
        private final String message;
        private final long timestamp = System.currentTimeMillis();
        
        public static ProcessResult success(String message) {
            return new ProcessResult(true, message);
        }
        
        public static ProcessResult failure(String message) {
            return new ProcessResult(false, message);
        }
    }

    /**
     * 演示Builder模式的使用
     */
    @Builder
    @Getter
    @ToString
    public static class Configuration {
        @Builder.Default
        private final String environment = "development";
        
        @Builder.Default
        private final int maxConnections = 10;
        
        @Builder.Default
        private final boolean enableLogging = true;
        
        private final String databaseUrl;
        private final String apiKey;
        
        /**
         * 验证配置
         */
        public boolean isValid() {
            return databaseUrl != null && !databaseUrl.isEmpty() &&
                   apiKey != null && !apiKey.isEmpty();
        }
    }

    /**
     * 演示配置的使用
     */
    public static void demonstrateBuilder() {
        Configuration config = Configuration.builder()
                .environment("production")
                .maxConnections(50)
                .databaseUrl("*********************************************")
                .apiKey("your-api-key")
                .build();
        
        log.info("Configuration created: {}", config);
        log.info("Configuration is valid: {}", config.isValid());
    }
}
