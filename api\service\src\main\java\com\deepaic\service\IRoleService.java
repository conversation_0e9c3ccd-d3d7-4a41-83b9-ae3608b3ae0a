package com.deepaic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.deepaic.core.entity.Role;
import com.deepaic.core.dto.RoleDTO;

import java.util.List;

/**
 * 角色服务接口
 *
 * <AUTHOR>
 */
public interface IRoleService extends IService<Role> {

    /**
     * 查询角色列表（带分页）
     */
    IPage<RoleDTO> getRolePage(Page<RoleDTO> page, RoleDTO.RoleQueryDTO query);

    /**
     * 查询所有角色列表
     */
    List<RoleDTO> getRoleList(RoleDTO.RoleQueryDTO query);

    /**
     * 根据用户ID查询角色列表
     */
    List<RoleDTO> getRoleListByUserId(Long userId);

    /**
     * 创建角色
     */
    Long createRole(RoleDTO roleDTO);

    /**
     * 更新角色
     */
    boolean updateRole(Long id, RoleDTO roleDTO);

    /**
     * 删除角色
     */
    boolean deleteRole(Long id);

    /**
     * 批量删除角色
     */
    boolean deleteRoles(List<Long> ids);

    /**
     * 获取角色详情
     */
    RoleDTO getRoleById(Long id);

    /**
     * 检查角色名称是否唯一
     */
    boolean checkRoleNameUnique(String roleName, Long id);

    /**
     * 检查角色编码是否唯一
     */
    boolean checkRoleCodeUnique(String roleCode, Long id);

    /**
     * 分配角色菜单权限
     */
    boolean assignRoleMenus(Long roleId, List<Long> menuIds);

    /**
     * 分配角色数据权限
     */
    boolean assignRoleDataScope(Long roleId, Integer dataScope, List<Long> deptIds);

    /**
     * 分配用户角色
     */
    boolean assignUserRoles(Long userId, List<Long> roleIds);

    /**
     * 取消用户角色
     */
    boolean removeUserRoles(Long userId, List<Long> roleIds);

    /**
     * 根据角色ID查询菜单ID列表
     */
    List<Long> getMenuIdsByRoleId(Long roleId);

    /**
     * 根据角色ID查询部门ID列表
     */
    List<Long> getDeptIdsByRoleId(Long roleId);
}
