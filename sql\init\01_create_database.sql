-- =====================================================
-- 美姿姿健康管理系统 - 数据库创建脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-20
-- 作者: 美姿姿团队
-- 说明: 创建主数据库和基础配置
-- 数据库连接: *****************************************************/mzz
-- =====================================================

-- 设置客户端编码
SET client_encoding = 'UTF8';

-- 创建数据库（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_database WHERE datname = 'mzz') THEN
        -- 注意：CREATE DATABASE 不能在事务块中执行
        -- 这个脚本需要以超级用户身份在postgres数据库中执行
        RAISE NOTICE '请手动执行以下命令创建数据库:';
        RAISE NOTICE 'CREATE DATABASE mzz WITH ENCODING = ''UTF8'' LC_COLLATE = ''zh_CN.UTF-8'' LC_CTYPE = ''zh_CN.UTF-8'' TEMPLATE = template0;';
    ELSE
        RAISE NOTICE '数据库 mzz 已存在';
    END IF;
END $$;

-- 如果是在psql中执行，可以使用以下命令：
-- \c postgres
-- CREATE DATABASE mzz
--     WITH ENCODING = 'UTF8'
--     LC_COLLATE = 'zh_CN.UTF-8'
--     LC_CTYPE = 'zh_CN.UTF-8'
--     TEMPLATE = template0
--     OWNER = postgres;

-- 创建数据库用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_user WHERE usename = 'mzz_user') THEN
        CREATE USER mzz_user WITH PASSWORD 'mzz_user_2025!';
        RAISE NOTICE '用户 mzz_user 创建成功';
    ELSE
        RAISE NOTICE '用户 mzz_user 已存在';
    END IF;
END $$;

-- 授予数据库权限
GRANT CONNECT ON DATABASE mzz TO mzz_user;
GRANT USAGE ON SCHEMA public TO mzz_user;
GRANT CREATE ON SCHEMA public TO mzz_user;

-- 创建只读用户（用于报表和查询）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_user WHERE usename = 'mzz_readonly') THEN
        CREATE USER mzz_readonly WITH PASSWORD 'mzz_readonly_2025!';
        RAISE NOTICE '只读用户 mzz_readonly 创建成功';
    ELSE
        RAISE NOTICE '只读用户 mzz_readonly 已存在';
    END IF;
END $$;

-- 授予只读权限
GRANT CONNECT ON DATABASE mzz TO mzz_readonly;
GRANT USAGE ON SCHEMA public TO mzz_readonly;

-- 设置数据库参数
-- 注意：这些设置需要在mzz数据库中执行
-- ALTER DATABASE mzz SET timezone TO 'Asia/Shanghai';
-- ALTER DATABASE mzz SET log_statement TO 'mod';
-- ALTER DATABASE mzz SET log_min_duration_statement TO 1000;

-- 创建扩展（需要在mzz数据库中执行）
-- \c mzz
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

RAISE NOTICE '==============================================';
RAISE NOTICE '数据库创建脚本执行完成';
RAISE NOTICE '请切换到 mzz 数据库继续执行后续脚本';
RAISE NOTICE '命令: \c mzz';
RAISE NOTICE '==============================================';

-- 记录脚本执行
INSERT INTO public.schema_version (version, description, executed_at)
VALUES ('1.0.0', '创建数据库和用户', NOW())
ON CONFLICT (version) DO NOTHING;
