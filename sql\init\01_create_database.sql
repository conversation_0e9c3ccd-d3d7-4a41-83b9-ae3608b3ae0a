-- =====================================================
-- 美姿姿健康管理系统 - 数据库创建脚本
-- 版本: 1.0.0
-- 创建时间: 2025-06-27
-- 作者: 美姿姿团队
-- 说明: 创建主数据库和基础配置
-- =====================================================

-- 设置客户端编码
SET client_encoding = 'UTF8';

-- 创建数据库（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_database WHERE datname = 'beautiful_posture') THEN
        -- 注意：CREATE DATABASE 不能在事务块中执行
        -- 这个脚本需要以超级用户身份在postgres数据库中执行
        RAISE NOTICE '请手动执行以下命令创建数据库:';
        RAISE NOTICE 'CREATE DATABASE beautiful_posture WITH ENCODING = ''UTF8'' LC_COLLATE = ''zh_CN.UTF-8'' LC_CTYPE = ''zh_CN.UTF-8'' TEMPLATE = template0;';
    ELSE
        RAISE NOTICE '数据库 beautiful_posture 已存在';
    END IF;
END $$;

-- 如果是在psql中执行，可以使用以下命令：
-- \c postgres
-- CREATE DATABASE beautiful_posture 
--     WITH ENCODING = 'UTF8' 
--     LC_COLLATE = 'zh_CN.UTF-8' 
--     LC_CTYPE = 'zh_CN.UTF-8' 
--     TEMPLATE = template0
--     OWNER = postgres;

-- 创建数据库用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_user WHERE usename = 'beautiful_posture_user') THEN
        CREATE USER beautiful_posture_user WITH PASSWORD 'bp_user_2025!';
        RAISE NOTICE '用户 beautiful_posture_user 创建成功';
    ELSE
        RAISE NOTICE '用户 beautiful_posture_user 已存在';
    END IF;
END $$;

-- 授予数据库权限
GRANT CONNECT ON DATABASE beautiful_posture TO beautiful_posture_user;
GRANT USAGE ON SCHEMA public TO beautiful_posture_user;
GRANT CREATE ON SCHEMA public TO beautiful_posture_user;

-- 创建只读用户（用于报表和查询）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_user WHERE usename = 'beautiful_posture_readonly') THEN
        CREATE USER beautiful_posture_readonly WITH PASSWORD 'bp_readonly_2025!';
        RAISE NOTICE '只读用户 beautiful_posture_readonly 创建成功';
    ELSE
        RAISE NOTICE '只读用户 beautiful_posture_readonly 已存在';
    END IF;
END $$;

-- 授予只读权限
GRANT CONNECT ON DATABASE beautiful_posture TO beautiful_posture_readonly;
GRANT USAGE ON SCHEMA public TO beautiful_posture_readonly;

-- 设置数据库参数
-- 注意：这些设置需要在beautiful_posture数据库中执行
-- ALTER DATABASE beautiful_posture SET timezone TO 'Asia/Shanghai';
-- ALTER DATABASE beautiful_posture SET log_statement TO 'mod';
-- ALTER DATABASE beautiful_posture SET log_min_duration_statement TO 1000;

-- 创建扩展（需要在beautiful_posture数据库中执行）
-- \c beautiful_posture
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

RAISE NOTICE '==============================================';
RAISE NOTICE '数据库创建脚本执行完成';
RAISE NOTICE '请切换到 beautiful_posture 数据库继续执行后续脚本';
RAISE NOTICE '命令: \c beautiful_posture';
RAISE NOTICE '==============================================';

-- 记录脚本执行
INSERT INTO public.schema_version (version, description, executed_at) 
VALUES ('1.0.0', '创建数据库和用户', NOW())
ON CONFLICT (version) DO NOTHING;
