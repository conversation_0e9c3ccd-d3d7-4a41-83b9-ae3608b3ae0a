package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@TableName("pub_member_account")
public class PubMemberAccount extends BaseEntity {

    private String tenantCode;

    private Long memberId;

    private Integer wechatOpenId;

    private LocalDateTime lastLoginTime;

    private Object lastLoginIp;

    private Long tenantId;
}
