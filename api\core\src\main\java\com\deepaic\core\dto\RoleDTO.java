package com.deepaic.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色DTO
 *
 * <AUTHOR>
 */
@Data
public class RoleDTO {

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 1, max = 50, message = "角色名称长度必须在1-50个字符之间")
    private String roleName;

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
    private String roleCode;

    /**
     * 角色描述
     */
    private String description;

    /**
     * 数据权限范围
     */
    private Integer dataScope;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 角色状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 菜单ID列表
     */
    private List<Long> menuIds;

    /**
     * 部门ID列表（数据权限）
     */
    private List<Long> deptIds;

    /**
     * 角色查询DTO
     */
    @Data
    public static class RoleQueryDTO {
        /**
         * 角色名称
         */
        private String roleName;

        /**
         * 角色编码
         */
        private String roleCode;

        /**
         * 角色状态
         */
        private Integer status;

        /**
         * 数据权限范围
         */
        private Integer dataScope;
    }
}
