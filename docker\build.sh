#!/bin/bash

# 美姿姿 项目构建脚本
# 用于构建所有模块并生成可部署的JAR文件

set -e  # 遇到错误时退出

echo "=========================================="
echo "美姿姿 项目构建开始"
echo "=========================================="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请安装Java 21或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 21 ]; then
    echo "错误: Java版本过低，需要Java 21或更高版本，当前版本: $JAVA_VERSION"
    exit 1
fi

echo "Java版本检查通过: $JAVA_VERSION"

# 清理之前的构建
echo "清理之前的构建..."
cd ../api
./gradlew clean

# 构建所有模块
echo "开始构建所有模块..."
./gradlew build -x test
cd ../docker

# 检查构建结果
echo "检查构建结果..."

# 检查API模块的JAR文件
API_MODULES=("platform-api" "admin-api" "app-api")
for module in "${API_MODULES[@]}"; do
    if [ "$module" = "platform-api" ]; then
        jar_file="../api/${module}/build/libs/platform-api.jar"
    else
        jar_file="../api/${module}/build/libs/beautiful-posture-${module}.jar"
    fi
    if [ -f "$jar_file" ]; then
        echo "✓ $module 构建成功: $jar_file"
        echo "  文件大小: $(du -h "$jar_file" | cut -f1)"
    else
        echo "✗ $module 构建失败: 未找到 $jar_file"
        exit 1
    fi
done

# 检查库模块的JAR文件
LIB_MODULES=("core" "service")
for module in "${LIB_MODULES[@]}"; do
    jar_file="../api/${module}/build/libs/beautiful-posture-${module}.jar"
    if [ -f "$jar_file" ]; then
        echo "✓ $module 构建成功: $jar_file"
        echo "  文件大小: $(du -h "$jar_file" | cut -f1)"
    else
        echo "✗ $module 构建失败: 未找到 $jar_file"
        exit 1
    fi
done

# 创建部署目录
DEPLOY_DIR="deploy"
echo "创建部署目录: $DEPLOY_DIR"
mkdir -p "$DEPLOY_DIR"

# 复制JAR文件到部署目录
echo "复制JAR文件到部署目录..."
for module in "${API_MODULES[@]}"; do
    if [ "$module" = "platform-api" ]; then
        cp "../api/${module}/build/libs/platform-api.jar" "$DEPLOY_DIR/"
    else
        cp "../api/${module}/build/libs/beautiful-posture-${module}.jar" "$DEPLOY_DIR/"
    fi
done

# 复制配置文件模板
echo "复制配置文件模板..."
if [ -f "application.yml.template" ]; then
    cp "application.yml.template" "$DEPLOY_DIR/application.yml.example"
fi

# 复制启动脚本
echo "创建启动脚本..."
cat > "$DEPLOY_DIR/start-platform-api.sh" << 'EOF'
#!/bin/bash
java -Xms512m -Xmx1024m -XX:+UseG1GC -jar platform-api.jar --server.port=8080
EOF

cat > "$DEPLOY_DIR/start-admin-api.sh" << 'EOF'
#!/bin/bash
java -Xms512m -Xmx1024m -XX:+UseG1GC -jar beautiful-posture-admin-api.jar --server.port=8081
EOF

cat > "$DEPLOY_DIR/start-app-api.sh" << 'EOF'
#!/bin/bash
java -Xms512m -Xmx1024m -XX:+UseG1GC -jar beautiful-posture-app-api.jar --server.port=8082
EOF

# 设置启动脚本权限
chmod +x "$DEPLOY_DIR"/*.sh

# 生成版本信息
echo "生成版本信息..."
cat > "$DEPLOY_DIR/version.txt" << EOF
美姿姿 项目构建信息
构建时间: $(date)
Git提交: $(git rev-parse --short HEAD 2>/dev/null || echo "未知")
Git分支: $(git branch --show-current 2>/dev/null || echo "未知")
构建环境: $(uname -a)
Java版本: $(java -version 2>&1 | head -n 1)
Gradle版本: $(cd ../api && ./gradlew --version | grep "Gradle" | head -n 1)
EOF

echo "=========================================="
echo "构建完成！"
echo "=========================================="
echo "部署文件位置: $DEPLOY_DIR/"
echo "包含以下文件:"
ls -la "$DEPLOY_DIR/"
echo ""
echo "启动命令:"
echo "  SaaS平台API:  cd $DEPLOY_DIR && ./start-platform-api.sh"
echo "  客户管理API:  cd $DEPLOY_DIR && ./start-admin-api.sh"
echo "  小程序API:    cd $DEPLOY_DIR && ./start-app-api.sh"
echo ""
echo "Docker构建命令:"
echo "  docker-compose build"
echo "  docker-compose up -d"
