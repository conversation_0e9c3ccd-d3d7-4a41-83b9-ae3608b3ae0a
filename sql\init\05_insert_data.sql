-- =====================================================
-- 美姿姿健康管理系统 - 初始数据插入脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-20
-- 作者: 美姿姿团队
-- 说明: 插入系统初始化数据
-- 数据库连接: *****************************************************/mzz
-- =====================================================

-- 确保在正确的数据库中执行
\c mzz

-- 设置客户端编码和时区
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- 开始事务
BEGIN;

-- ==============================================
-- 插入平台管理员账户
-- ==============================================

-- 插入超级管理员
INSERT INTO public.pub_platform_account (
    username, password, email, real_name, permission_level, status, remark
) VALUES (
    'admin', 
    '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', -- 密码: admin123
    '<EMAIL>',
    '系统管理员',
    3, -- 超级管理员
    1, -- 启用
    '系统初始化创建的超级管理员账户'
) ON CONFLICT (username) DO NOTHING;

-- 插入平台管理员
INSERT INTO public.pub_platform_account (
    username, password, email, real_name, permission_level, status, remark
) VALUES (
    'platform_admin', 
    '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', -- 密码: admin123
    '<EMAIL>',
    '平台管理员',
    2, -- 高级管理员
    1, -- 启用
    '平台日常管理账户'
) ON CONFLICT (username) DO NOTHING;

-- ==============================================
-- 插入示例租户
-- ==============================================

-- 插入示例租户
INSERT INTO public.pub_tenant (
    tenant_code, tenant_name, schema_name, tenant_type, status,
    contact_name, contact_phone, contact_email, address,
    expire_time, max_users, max_storage, features, settings, remark
) VALUES (
    'demo',
    '示例租户',
    'tenant_demo',
    2, -- 标准版
    1, -- 启用
    '张三',
    '***********',
    '<EMAIL>',
    '北京市朝阳区示例大厦',
    '2025-12-31 23:59:59'::timestamp,
    50, -- 最大用户数
    **********, -- 5GB存储
    '{"modules": ["user_management", "role_management", "member_management"], "features": ["multi_tenant", "api_access"]}',
    '{"theme": "default", "language": "zh_CN", "timezone": "Asia/Shanghai"}',
    '系统示例租户，用于演示和测试'
) ON CONFLICT (tenant_code) DO NOTHING;

-- ==============================================
-- 插入示例租户管理员
-- ==============================================

-- 插入租户管理员
INSERT INTO public.pub_customer_account (
    tenant_code, username, password, email, real_name, account_type, status, remark
) VALUES (
    'demo',
    'demo_admin',
    '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', -- 密码: admin123
    '<EMAIL>',
    '示例管理员',
    1, -- 管理员
    1, -- 启用
    '示例租户的管理员账户'
) ON CONFLICT (tenant_code, username) DO NOTHING;

-- ==============================================
-- 插入租户内部数据（tenant_demo schema）
-- ==============================================

-- 切换到示例租户Schema
SET search_path TO tenant_demo, public;

-- 插入组织机构数据（使用雪花算法ID）
INSERT INTO sys_organization (id, parent_id, ancestors, org_code, org_name, org_type, org_level, leader_name, phone, email, status, description) VALUES
-- 公司层级
(1735123456789020001, 0, '0', 'COMPANY_001', '美姿姿健康科技有限公司', 1, 1, '张总', '***********', '<EMAIL>', 1, '公司总部'),
-- 部门层级
(1735123456789020002, 1735123456789020001, '0,1735123456789020001', 'DEPT_TECH', '技术研发部', 2, 2, '李技术总监', '13800138001', '<EMAIL>', 1, '负责产品技术研发'),
(1735123456789020003, 1735123456789020001, '0,1735123456789020001', 'DEPT_MARKET', '市场营销部', 2, 2, '王市场总监', '13800138002', '<EMAIL>', 1, '负责市场推广和销售'),
(1735123456789020004, 1735123456789020001, '0,1735123456789020001', 'DEPT_HR', '人力资源部', 2, 2, '赵人事经理', '13800138003', '<EMAIL>', 1, '负责人力资源管理'),
-- 小组层级
(1735123456789020005, 1735123456789020002, '0,1735123456789020001,1735123456789020002', 'GROUP_FRONTEND', '前端开发组', 3, 3, '孙前端组长', '13800138004', '<EMAIL>', 1, '负责前端开发'),
(1735123456789020006, 1735123456789020002, '0,1735123456789020001,1735123456789020002', 'GROUP_BACKEND', '后端开发组', 3, 3, '周后端组长', '13800138005', '<EMAIL>', 1, '负责后端开发')
ON CONFLICT (id) DO NOTHING;

-- 插入岗位数据（使用雪花算法ID）
INSERT INTO sys_position (id, position_code, position_name, org_id, position_level, position_category, job_description, requirements, salary_range, status) VALUES
(1735123456789020101, 'POS_CEO', '首席执行官', 1735123456789020001, 1, '高级管理', '负责公司整体战略和运营', '10年以上管理经验', '50-100万', 1),
(1735123456789020102, 'POS_CTO', '技术总监', 1735123456789020002, 2, '技术管理', '负责技术团队管理和技术决策', '8年以上技术管理经验', '30-60万', 1),
(1735123456789020103, 'POS_SENIOR_DEV', '高级开发工程师', 1735123456789020005, 3, '技术开发', '负责核心功能开发', '3年以上开发经验', '15-30万', 1),
(1735123456789020104, 'POS_DEV', '开发工程师', 1735123456789020006, 4, '技术开发', '负责功能模块开发', '1年以上开发经验', '10-20万', 1)
ON CONFLICT (id) DO NOTHING;

-- 注意：使用雪花算法生成ID，无需重置序列

-- 插入角色数据
INSERT INTO sys_role (id, role_name, role_code, role_sort, data_scope, status, remark) VALUES
(1, '超级管理员', 'super_admin', 1, 1, 1, '超级管理员角色，拥有所有权限'),
(2, '管理员', 'admin', 2, 2, 1, '管理员角色，拥有大部分权限'),
(3, '普通用户', 'user', 3, 5, 1, '普通用户角色，基础权限'),
(4, '部门经理', 'dept_manager', 4, 4, 1, '部门经理角色，管理本部门及下级部门'),
(5, '员工', 'employee', 5, 5, 1, '普通员工角色')
ON CONFLICT (id) DO NOTHING;

-- 注意：使用雪花算法生成ID，无需重置序列

-- 插入菜单数据
INSERT INTO sys_menu (id, menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, remark) VALUES
-- 一级菜单
(1, '系统管理', 0, 1, 'system', NULL, 'M', true, 1, NULL, 'system', '系统管理目录'),
(2, '用户管理', 0, 2, 'user', NULL, 'M', true, 1, NULL, 'user', '用户管理目录'),
(3, '会员管理', 0, 3, 'member', NULL, 'M', true, 1, NULL, 'peoples', '会员管理目录'),

-- 系统管理子菜单
(101, '用户管理', 1, 1, 'user', 'system/user/index', 'C', true, 1, 'system:user:list', 'user', '用户管理菜单'),
(102, '角色管理', 1, 2, 'role', 'system/role/index', 'C', true, 1, 'system:role:list', 'peoples', '角色管理菜单'),
(103, '菜单管理', 1, 3, 'menu', 'system/menu/index', 'C', true, 1, 'system:menu:list', 'tree-table', '菜单管理菜单'),
(104, '部门管理', 1, 4, 'dept', 'system/dept/index', 'C', true, 1, 'system:dept:list', 'tree', '部门管理菜单'),

-- 用户管理按钮
(1001, '用户查询', 101, 1, '', '', 'F', true, 1, 'system:user:query', '#', ''),
(1002, '用户新增', 101, 2, '', '', 'F', true, 1, 'system:user:add', '#', ''),
(1003, '用户修改', 101, 3, '', '', 'F', true, 1, 'system:user:edit', '#', ''),
(1004, '用户删除', 101, 4, '', '', 'F', true, 1, 'system:user:remove', '#', ''),
(1005, '用户导出', 101, 5, '', '', 'F', true, 1, 'system:user:export', '#', ''),
(1006, '用户导入', 101, 6, '', '', 'F', true, 1, 'system:user:import', '#', ''),
(1007, '重置密码', 101, 7, '', '', 'F', true, 1, 'system:user:resetPwd', '#', ''),

-- 角色管理按钮
(1008, '角色查询', 102, 1, '', '', 'F', true, 1, 'system:role:query', '#', ''),
(1009, '角色新增', 102, 2, '', '', 'F', true, 1, 'system:role:add', '#', ''),
(1010, '角色修改', 102, 3, '', '', 'F', true, 1, 'system:role:edit', '#', ''),
(1011, '角色删除', 102, 4, '', '', 'F', true, 1, 'system:role:remove', '#', ''),
(1012, '角色导出', 102, 5, '', '', 'F', true, 1, 'system:role:export', '#', ''),

-- 菜单管理按钮
(1013, '菜单查询', 103, 1, '', '', 'F', true, 1, 'system:menu:query', '#', ''),
(1014, '菜单新增', 103, 2, '', '', 'F', true, 1, 'system:menu:add', '#', ''),
(1015, '菜单修改', 103, 3, '', '', 'F', true, 1, 'system:menu:edit', '#', ''),
(1016, '菜单删除', 103, 4, '', '', 'F', true, 1, 'system:menu:remove', '#', ''),

-- 部门管理按钮
(1017, '部门查询', 104, 1, '', '', 'F', true, 1, 'system:dept:query', '#', ''),
(1018, '部门新增', 104, 2, '', '', 'F', true, 1, 'system:dept:add', '#', ''),
(1019, '部门修改', 104, 3, '', '', 'F', true, 1, 'system:dept:edit', '#', ''),
(1020, '部门删除', 104, 4, '', '', 'F', true, 1, 'system:dept:remove', '#', '')
ON CONFLICT (id) DO NOTHING;

-- 注意：使用雪花算法生成ID，无需重置序列

-- 插入用户数据（使用雪花算法ID）
INSERT INTO sys_user (id, user_code, username, password, email, real_name, org_id, position_id, employee_no, entry_date, status, remark) VALUES
(1735123456789012387, 'U001', 'admin', '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', '<EMAIL>', '系统管理员', 1735123456789020001, 1735123456789020101, 'EMP001', '2025-01-01', 1, '系统管理员'),
(1735123456789012388, 'U002', 'tech_manager', '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', '<EMAIL>', '技术经理', 1735123456789020002, 1735123456789020102, 'EMP002', '2025-01-01', 1, '技术部经理'),
(1735123456789012389, 'U003', 'frontend_dev', '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', '<EMAIL>', '前端开发', 1735123456789020005, 1735123456789020103, 'EMP003', '2025-01-01', 1, '前端开发工程师'),
(1735123456789012390, 'U004', 'backend_dev', '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', '<EMAIL>', '后端开发', 1735123456789020006, 1735123456789020104, 'EMP004', '2025-01-01', 1, '后端开发工程师')
ON CONFLICT (id) DO NOTHING;

-- 插入用户组织关联数据（使用雪花算法ID）
INSERT INTO sys_user_organization (id, user_id, org_id, position_id, is_primary, is_leader, join_date, status) VALUES
(1735123456789020201, 1735123456789012387, 1735123456789020001, 1735123456789020101, TRUE, TRUE, '2025-01-01', 1),
(1735123456789020202, 1735123456789012388, 1735123456789020002, 1735123456789020102, TRUE, TRUE, '2025-01-01', 1),
(1735123456789020203, 1735123456789012389, 1735123456789020005, 1735123456789020103, TRUE, FALSE, '2025-01-01', 1),
(1735123456789020204, 1735123456789012390, 1735123456789020006, 1735123456789020104, TRUE, FALSE, '2025-01-01', 1)
ON CONFLICT (id) DO NOTHING;

-- 注意：使用雪花算法生成ID，无需重置序列

-- 插入用户角色关联
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1, 1), -- admin -> 超级管理员
(2, 4), -- tech_manager -> 部门经理
(3, 5), -- frontend_dev -> 员工
(4, 5)  -- backend_dev -> 员工
ON CONFLICT (user_id, role_id) DO NOTHING;

-- 插入角色菜单关联（超级管理员拥有所有权限）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, id FROM sys_menu WHERE status = 1
ON CONFLICT (role_id, menu_id) DO NOTHING;

-- 插入示例会员数据
INSERT INTO sys_member (id, member_no, nickname, real_name, phone, member_level, status, register_source, remark) VALUES
(1, 'M000001', '张小明', '张明', '***********', 1, 1, 'wechat', '微信注册会员'),
(2, 'M000002', '李小红', '李红', '***********', 2, 1, 'phone', '手机号注册会员'),
(3, 'M000003', '王小强', '王强', '***********', 1, 1, 'wechat', '微信注册会员')
ON CONFLICT (id) DO NOTHING;

-- 注意：使用雪花算法生成ID，无需重置序列

-- 插入会员账户关联（使用雪花算法ID）
INSERT INTO public.pub_member_account (id, tenant_code, member_id, login_type, login_identifier, status, remark) VALUES
(1735123456789012394, 'demo', 1735123456789012391, 1, 'wx_openid_001', 1, '张小明的微信登录'),
(1735123456789012395, 'demo', 1735123456789012392, 2, '***********', 1, '李小红的手机号登录'),
(1735123456789012396, 'demo', 1735123456789012393, 1, 'wx_openid_003', 1, '王小强的微信登录')
ON CONFLICT (id) DO NOTHING;

-- 插入数据权限配置（使用雪花算法ID）
INSERT INTO sys_data_permission (id, permission_name, permission_code, permission_type, resource_type, scope_type, scope_value, status, remark) VALUES
-- 组织权限
(1735123456789020301, '全部数据权限', 'DATA_SCOPE_ALL', 1, '*', 1, NULL, 1, '可以查看所有组织的数据'),
(1735123456789020302, '本组织数据权限', 'DATA_SCOPE_CURRENT', 1, '*', 2, NULL, 1, '只能查看本组织的数据'),
(1735123456789020303, '本组织及下级数据权限', 'DATA_SCOPE_CURRENT_AND_SUB', 1, '*', 3, NULL, 1, '可以查看本组织及下级组织的数据'),
(1735123456789020304, '仅本人数据权限', 'DATA_SCOPE_SELF', 1, '*', 5, NULL, 1, '只能查看本人的数据'),
-- 模块权限
(1735123456789020305, '用户管理数据权限', 'DATA_SCOPE_USER', 2, 'sys_user', 3, NULL, 1, '用户管理模块的数据权限'),
(1735123456789020306, '组织管理数据权限', 'DATA_SCOPE_ORG', 2, 'sys_organization', 3, NULL, 1, '组织管理模块的数据权限'),
(1735123456789020307, '会员管理数据权限', 'DATA_SCOPE_MEMBER', 2, 'sys_member', 2, NULL, 1, '会员管理模块的数据权限')
ON CONFLICT (id) DO NOTHING;

-- 配置角色数据权限
INSERT INTO sys_role_data_permission (role_id, data_permission_id) VALUES
-- 超级管理员拥有全部数据权限
(1735123456789012355, 1735123456789020301),
(1735123456789012355, 1735123456789020305),
(1735123456789012355, 1735123456789020306),
(1735123456789012355, 1735123456789020307),
-- 管理员拥有本组织及下级数据权限
(1735123456789012356, 1735123456789020303),
(1735123456789012356, 1735123456789020305),
(1735123456789012356, 1735123456789020306),
-- 部门经理拥有本组织及下级数据权限
(1735123456789012358, 1735123456789020303),
(1735123456789012358, 1735123456789020305),
-- 普通员工只能查看本人数据
(1735123456789012359, 1735123456789020304)
ON CONFLICT (role_id, data_permission_id) DO NOTHING;

-- 插入用户扩展信息（使用雪花算法ID）
INSERT INTO sys_user_profile (id, user_id, education, major, work_experience, skills, emergency_contact, emergency_phone, address) VALUES
(1735123456789020401, 1735123456789012387, '硕士', '计算机科学与技术', '10年互联网行业管理经验', '团队管理,战略规划,产品设计', '张夫人', '***********', '北京市朝阳区'),
(1735123456789020402, 1735123456789012388, '本科', '软件工程', '8年技术开发和管理经验', 'Java,Python,架构设计,团队管理', '李夫人', '***********', '北京市海淀区'),
(1735123456789020403, 1735123456789012389, '本科', '计算机科学与技术', '3年前端开发经验', 'Vue.js,React,TypeScript,UI设计', '孙父亲', '***********', '北京市西城区'),
(1735123456789020404, 1735123456789012390, '本科', '软件工程', '2年后端开发经验', 'Java,Spring Boot,MySQL,Redis', '周母亲', '***********', '北京市东城区')
ON CONFLICT (id) DO NOTHING;

-- 建立登录账户关联
UPDATE public.pub_customer_account SET
    sys_user_id = 1735123456789012387,
    is_primary = TRUE
WHERE tenant_code = 'demo' AND username = 'demo_admin';

-- 更新业务用户表，建立与登录账户的关联
UPDATE sys_user SET
    account_id = (
        SELECT id FROM public.pub_customer_account
        WHERE tenant_code = 'demo' AND username = 'demo_admin'
    )
WHERE username = 'admin';

-- 恢复搜索路径
SET search_path TO public, system_admin, audit, reporting;

-- 提交事务
COMMIT;

-- 记录脚本执行
INSERT INTO public.schema_version (version, description, executed_at) 
VALUES ('1.0.0', '插入系统初始化数据', NOW())
ON CONFLICT (version) DO UPDATE SET 
    executed_at = NOW(),
    description = EXCLUDED.description;

RAISE NOTICE '==============================================';
RAISE NOTICE '初始数据插入脚本执行完成';
RAISE NOTICE '已插入数据:';
RAISE NOTICE '- 平台管理员账户: admin, platform_admin';
RAISE NOTICE '- 示例租户: demo';
RAISE NOTICE '- 租户管理员: demo_admin';
RAISE NOTICE '- 组织机构数据: 6个组织（公司、部门、小组）';
RAISE NOTICE '- 岗位数据: 4个岗位';
RAISE NOTICE '- 角色数据: 5个角色';
RAISE NOTICE '- 菜单数据: 完整的菜单权限体系';
RAISE NOTICE '- 用户数据: 4个示例用户';
RAISE NOTICE '- 用户组织关联: 4个关联记录';
RAISE NOTICE '- 数据权限: 7个权限配置';
RAISE NOTICE '- 用户扩展信息: 4个用户详细信息';
RAISE NOTICE '- 会员数据: 3个示例会员';
RAISE NOTICE '- 账户关联: 登录账户与业务用户关联';
RAISE NOTICE '==============================================';
RAISE NOTICE '默认登录信息:';
RAISE NOTICE '平台管理员 - 用户名: admin, 密码: admin123';
RAISE NOTICE '租户管理员 - 用户名: demo_admin, 密码: admin123';
RAISE NOTICE '==============================================';

-- 显示数据统计
SELECT 
    'pub_platform_account' as table_name,
    COUNT(*) as record_count
FROM public.pub_platform_account
UNION ALL
SELECT 
    'pub_tenant' as table_name,
    COUNT(*) as record_count
FROM public.pub_tenant
UNION ALL
SELECT 
    'pub_customer_account' as table_name,
    COUNT(*) as record_count
FROM public.pub_customer_account
UNION ALL
SELECT 
    'pub_member_account' as table_name,
    COUNT(*) as record_count
FROM public.pub_member_account
ORDER BY table_name;
