package com.deepaic.app;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 小程序API启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {
    "com.deepaic.core",
    "com.deepaic.service",
    "com.deepaic.app"
})
@MapperScan(basePackages = {
    "com.deepaic.core.mapper",
    "com.deepaic.service.mapper"
})
public class AppApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(AppApiApplication.class, args);
    }

}
