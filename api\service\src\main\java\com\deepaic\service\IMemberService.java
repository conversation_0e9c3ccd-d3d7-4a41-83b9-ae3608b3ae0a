package com.deepaic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.deepaic.core.entity.Member;
import com.deepaic.core.dto.MemberDTO;

import java.util.List;

/**
 * 会员服务接口
 *
 * <AUTHOR>
 */
public interface IMemberService extends IService<Member> {

    /**
     * 查询会员列表（带分页）
     */
    IPage<MemberDTO> getMemberPage(Page<MemberDTO> page, MemberDTO.MemberQueryDTO query);

    /**
     * 查询所有会员列表
     */
    List<MemberDTO> getMemberList(MemberDTO.MemberQueryDTO query);

    /**
     * 获取会员详情
     */
    MemberDTO getMemberById(Long id);

    /**
     * 根据会员编号获取会员
     */
    MemberDTO getMemberByNo(String memberNo);

    /**
     * 根据手机号获取会员
     */
    MemberDTO getMemberByPhone(String phone);

    /**
     * 根据微信OpenID获取会员
     */
    MemberDTO getMemberByWxOpenid(String wxOpenid);

    /**
     * 创建会员
     */
    Long createMember(MemberDTO memberDTO);

    /**
     * 更新会员
     */
    boolean updateMember(Long id, MemberDTO memberDTO);

    /**
     * 删除会员
     */
    boolean deleteMember(Long id);

    /**
     * 批量删除会员
     */
    boolean deleteMembers(List<Long> ids);

    /**
     * 启用会员
     */
    boolean enableMember(Long id);

    /**
     * 禁用会员
     */
    boolean disableMember(Long id);

    /**
     * 冻结会员
     */
    boolean freezeMember(Long id);

    /**
     * 检查会员编号是否唯一
     */
    boolean checkMemberNoUnique(String memberNo, Long id);

    /**
     * 检查手机号是否唯一
     */
    boolean checkPhoneUnique(String phone, Long id);

    /**
     * 更新会员积分
     */
    boolean updateMemberPoints(Long id, Integer points);

    /**
     * 更新会员余额
     */
    boolean updateMemberBalance(Long id, Long balance);

    /**
     * 更新会员等级
     */
    boolean updateMemberLevel(Long id, Long levelId);

    /**
     * 获取推荐会员列表
     */
    List<MemberDTO> getReferralMembers(Long referrerId);

    /**
     * 统计推荐会员数量
     */
    Integer countReferralMembers(Long referrerId);

    /**
     * 统计会员数量
     */
    Integer countMembers(MemberDTO.MemberQueryDTO query);
}
