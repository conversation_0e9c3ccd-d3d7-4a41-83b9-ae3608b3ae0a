# Docker 分布式部署指南

本目录包含美姿姿项目的Docker化部署相关文件，支持单机部署和分布式部署两种模式。

## 📁 文件说明

```
docker/
├── Dockerfile.admin-api              # Admin API Dockerfile
├── Dockerfile.app-api                # App API Dockerfile
├── Dockerfile.client-api             # Client API Dockerfile
├── docker-compose.yml                # 基础设施部署配置
├── docker-compose.full.yml           # 完整部署配置（单机模式）
├── docker-compose.infrastructure.yml # 基础设施组件配置
├── docker-compose.admin-api.yml      # Admin API服务配置
├── docker-compose.app-api.yml        # App API服务配置
├── docker-compose.client-api.yml     # Client API服务配置
├── .env.example                      # 环境变量配置示例
├── deploy.sh                         # Linux/Mac部署脚本
├── deploy.bat                        # Windows部署脚本
├── build.sh                          # 构建脚本
├── build.bat                         # Windows构建脚本
├── application.yml.template          # 应用配置模板
└── README.md                         # 本文档
```

## 🏗️ 部署架构

### 单机部署模式
所有服务部署在同一台服务器上，适用于开发环境或小规模部署。

### 分布式部署模式
将基础设施和各个API服务分别部署到不同的服务器上，适用于生产环境。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   基础设施服务器   │    │   API服务器1     │    │   API服务器2     │
│                │    │                │    │                │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ PostgreSQL  │ │    │ │ Admin API   │ │    │ │ App API     │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │                │    │                │
│ │ Redis       │ │    │                │    │ ┌─────────────┐ │
│ └─────────────┘ │    │                │    │ │ Client API  │ │
│ ┌─────────────┐ │    │                │    │ └─────────────┘ │
│ │ Nginx       │ │    │                │    │                │
│ └─────────────┘ │    │                │    │                │
│ ┌─────────────┐ │    │                │    │                │
│ │ Monitoring  │ │    │                │    │                │
│ └─────────────┘ │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速部署

### 环境准备

1. **安装Docker和Docker Compose**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install docker.io docker-compose

   # CentOS/RHEL
   sudo yum install docker docker-compose

   # 启动Docker服务
   sudo systemctl start docker
   sudo systemctl enable docker
   ```

2. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp .env.example .env

   # 编辑环境变量文件
   vim .env
   ```

### 基础设施部署（开发环境）

使用主docker-compose文件部署基础设施组件：

```bash
# Linux/Mac
./deploy.sh infrastructure

# Windows
deploy.bat infrastructure

# 或者直接使用docker-compose
docker-compose up -d
```

**注意**: 主docker-compose.yml现在只包含基础设施组件（PostgreSQL、Redis），API服务需要单独部署。

### 完整部署（单机测试）

如果需要在单机上部署所有服务进行测试：

```bash
# 使用完整配置部署所有服务
./deploy.sh full

# 或者直接使用docker-compose
docker-compose -f docker-compose.full.yml up -d
```

### 分布式部署（生产环境）

#### 1. 部署基础设施服务器

在基础设施服务器上部署数据库、缓存等组件：

```bash
# 部署基础设施
./deploy.sh infrastructure

# 启用监控组件
./deploy.sh infrastructure -p monitoring

# 启用高可用Redis
./deploy.sh infrastructure -p ha

# 启用负载均衡
./deploy.sh infrastructure -p proxy
```

#### 2. 部署API服务器

在不同的API服务器上分别部署各个服务：

```bash
# 服务器1: 部署Admin API
./deploy.sh admin-api

# 服务器2: 部署App API
./deploy.sh app-api

# 服务器3: 部署Client API
./deploy.sh client-api
```

## 📋 部署配置详解

### 基础设施配置 (docker-compose.infrastructure.yml)

包含以下组件：

- **PostgreSQL**: 主数据库
- **Redis**: 缓存和会话存储
- **Nginx**: 负载均衡和反向代理 (可选)
- **Elasticsearch + Kibana**: 日志系统 (可选)
- **Prometheus + Grafana**: 监控系统 (可选)

### API服务配置

每个API服务都有独立的配置文件：

- **Admin API**: 管理后台，端口8080
- **App API**: 移动应用，端口8081
- **Client API**: Web客户端，端口8082

## 🔧 部署脚本使用

### 基本语法

```bash
# Linux/Mac
./deploy.sh [选项] <服务>

# Windows
deploy.bat [选项] <服务>
```

### 服务选项

- `infrastructure`: 基础设施组件
- `admin-api`: 管理后台API
- `app-api`: 移动应用API
- `client-api`: 客户端API
- `all`: 所有服务（单机模式）

### 常用选项

- `-f, --force`: 强制重新构建镜像
- `-p, --profile`: 指定profile（monitoring, ha, proxy等）
- `-e, --env`: 指定环境变量文件
- `--stop`: 停止服务
- `--restart`: 重启服务
- `--logs`: 查看日志
- `--status`: 查看状态

### 使用示例

```bash
# 部署基础设施并启用监控
./deploy.sh infrastructure -p monitoring

# 强制重新构建Admin API
./deploy.sh admin-api -f

# 查看App API日志
./deploy.sh --logs app-api

# 重启Client API
./deploy.sh --restart client-api

# 停止所有服务
./deploy.sh --stop all
```

### 1. 使用构建脚本 (推荐)

#### Linux/Mac
```bash
chmod +x build.sh
./build.sh
```

#### Windows
```cmd
build.bat
```

构建脚本会自动：
- 编译所有模块
- 生成JAR文件
- 创建部署目录
- 复制启动脚本
- 生成版本信息

### 2. Docker Compose部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 3. 单独构建Docker镜像

```bash
# 构建管理后台API镜像
docker build -f Dockerfile.admin-api -t beautiful-posture-admin-api ..

# 构建移动端API镜像
docker build -f Dockerfile.app-api -t beautiful-posture-app-api ..

# 构建客户端API镜像
docker build -f Dockerfile.client-api -t beautiful-posture-client-api ..
```

## 🔧 配置说明

### Docker Compose服务

| 服务名 | 端口 | 说明 |
|--------|------|------|
| postgres | 5432 | PostgreSQL数据库 |
| redis | 6379 | Redis缓存 |
| admin-api | 8080 | 管理后台API |
| app-api | 8081 | 移动端API |
| client-api | 8082 | 客户端API |

### 环境变量

```yaml
# 数据库配置
SPRING_DATASOURCE_URL: *************************************************
SPRING_DATASOURCE_USERNAME: postgres
SPRING_DATASOURCE_PASSWORD: postgres123

# Redis配置
SPRING_REDIS_HOST: redis
SPRING_REDIS_PORT: 6379

# 应用配置
SPRING_PROFILES_ACTIVE: docker
```

### 数据卷

- `postgres_data`: PostgreSQL数据持久化
- `redis_data`: Redis数据持久化

## 🛠️ 自定义配置

### 1. 修改数据库配置

编辑 `docker-compose.yml`:

```yaml
postgres:
  environment:
    POSTGRES_DB: your_database
    POSTGRES_USER: your_username
    POSTGRES_PASSWORD: your_password
```

### 2. 修改应用端口

```yaml
admin-api:
  ports:
    - "9080:8080"  # 修改外部端口
```

### 3. 添加环境变量

```yaml
admin-api:
  environment:
    SPRING_PROFILES_ACTIVE: production
    JAVA_OPTS: "-Xms1g -Xmx2g"
```

### 4. 使用外部配置文件

```yaml
admin-api:
  volumes:
    - ./config/application.yml:/app/application.yml
```

## 📊 监控和日志

### 查看应用日志

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs admin-api

# 实时跟踪日志
docker-compose logs -f app-api
```

### 健康检查

访问以下端点检查服务状态：

- 管理后台API: http://localhost:8080/actuator/health
- 移动端API: http://localhost:8081/actuator/health
- 客户端API: http://localhost:8082/actuator/health

### 性能监控

```bash
# 查看容器资源使用情况
docker stats

# 查看特定容器资源使用
docker stats beautiful-posture-admin-api
```

## 🔍 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :8080

# 修改docker-compose.yml中的端口映射
```

#### 2. 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose ps postgres

# 查看数据库日志
docker-compose logs postgres
```

#### 3. 内存不足
```bash
# 增加JVM内存限制
environment:
  JAVA_OPTS: "-Xms512m -Xmx1024m"
```

#### 4. 构建失败
```bash
# 清理Docker缓存
docker system prune -a

# 重新构建镜像
docker-compose build --no-cache
```

### 调试模式

启用调试模式：

```yaml
admin-api:
  environment:
    JAVA_OPTS: "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
  ports:
    - "5005:5005"  # 调试端口
```

## 🚀 生产环境部署

### 1. 安全配置

```yaml
# 移除默认密码
postgres:
  environment:
    POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password

# 使用secrets管理敏感信息
secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
```

### 2. 资源限制

```yaml
admin-api:
  deploy:
    resources:
      limits:
        cpus: '1.0'
        memory: 1G
      reservations:
        cpus: '0.5'
        memory: 512M
```

### 3. 健康检查

```yaml
admin-api:
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
    interval: 30s
    timeout: 10s
    retries: 3
```

### 4. 日志管理

```yaml
admin-api:
  logging:
    driver: "json-file"
    options:
      max-size: "100m"
      max-file: "3"
```

## 📈 扩展部署

### 水平扩展

```bash
# 扩展API服务实例
docker-compose up -d --scale admin-api=3

# 使用负载均衡器
# 需要配置nginx或其他负载均衡器
```

### 集群部署

```yaml
# 使用Docker Swarm
docker swarm init
docker stack deploy -c docker-compose.yml beautiful-posture
```

---

更多详细信息请参考 [项目文档](../guide/) 📚
