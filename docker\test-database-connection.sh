#!/bin/bash

# 美姿姿 - 数据库连接测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 未运行，请先启动Docker"
        exit 1
    fi
    log_success "Docker 运行正常"
}

# 启动PostgreSQL容器
start_postgres() {
    log_info "启动PostgreSQL容器..."
    
    if docker ps | grep -q "beautiful-posture-postgres"; then
        log_warning "PostgreSQL容器已在运行"
    else
        docker run -d \
            --name beautiful-posture-postgres \
            --network beautiful-posture-network \
            -e POSTGRES_DB=beautiful_posture \
            -e POSTGRES_USER=postgres \
            -e POSTGRES_PASSWORD=postgres123 \
            -p 5432:5432 \
            -v "$(pwd)/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql" \
            postgres:15-alpine
        
        log_info "等待PostgreSQL启动..."
        sleep 10
    fi
    
    # 测试连接
    if docker exec beautiful-posture-postgres pg_isready -U postgres >/dev/null 2>&1; then
        log_success "PostgreSQL 连接正常"
    else
        log_error "PostgreSQL 连接失败"
        exit 1
    fi
}

# 启动Redis容器
start_redis() {
    log_info "启动Redis容器..."
    
    if docker ps | grep -q "beautiful-posture-redis"; then
        log_warning "Redis容器已在运行"
    else
        docker run -d \
            --name beautiful-posture-redis \
            --network beautiful-posture-network \
            -p 6379:6379 \
            redis:7-alpine
        
        log_info "等待Redis启动..."
        sleep 5
    fi
    
    # 测试连接
    if docker exec beautiful-posture-redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis 连接正常"
    else
        log_error "Redis 连接失败"
        exit 1
    fi
}

# 创建网络
create_network() {
    if ! docker network ls | grep -q "beautiful-posture-network"; then
        log_info "创建Docker网络..."
        docker network create beautiful-posture-network --driver bridge
        log_success "网络创建成功"
    else
        log_info "网络已存在"
    fi
}

# 测试数据库表
test_database_tables() {
    log_info "测试数据库表结构..."
    
    # 检查表是否存在
    tables=$(docker exec beautiful-posture-postgres psql -U postgres -d beautiful_posture -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public';")
    
    if echo "$tables" | grep -q "sys_user"; then
        log_success "用户表创建成功"
    else
        log_error "用户表创建失败"
        return 1
    fi
    
    if echo "$tables" | grep -q "sys_role"; then
        log_success "角色表创建成功"
    else
        log_error "角色表创建失败"
        return 1
    fi
    
    # 检查默认数据
    user_count=$(docker exec beautiful-posture-postgres psql -U postgres -d beautiful_posture -t -c "SELECT COUNT(*) FROM sys_user WHERE username = 'admin';")
    if [ "$(echo $user_count | tr -d ' ')" = "1" ]; then
        log_success "默认管理员用户创建成功"
    else
        log_error "默认管理员用户创建失败"
        return 1
    fi
}

# 测试Redis数据库
test_redis_databases() {
    log_info "测试Redis数据库..."
    
    # 测试不同数据库
    for db in 0 1 2; do
        if docker exec beautiful-posture-redis redis-cli -n $db ping | grep -q "PONG"; then
            log_success "Redis数据库 $db 连接正常"
        else
            log_error "Redis数据库 $db 连接失败"
            return 1
        fi
    done
}

# 显示连接信息
show_connection_info() {
    log_info "数据库连接信息："
    echo "PostgreSQL:"
    echo "  Host: localhost"
    echo "  Port: 5432"
    echo "  Database: beautiful_posture"
    echo "  Username: postgres"
    echo "  Password: postgres123"
    echo ""
    echo "Redis:"
    echo "  Host: localhost"
    echo "  Port: 6379"
    echo "  Databases: 0 (Admin API), 1 (App API), 2 (Client API)"
    echo ""
    echo "测试连接："
    echo "  psql -h localhost -p 5432 -U postgres -d beautiful_posture"
    echo "  redis-cli -h localhost -p 6379"
}

# 清理函数
cleanup() {
    if [ "$1" = "clean" ]; then
        log_info "清理测试环境..."
        docker stop beautiful-posture-postgres beautiful-posture-redis >/dev/null 2>&1 || true
        docker rm beautiful-posture-postgres beautiful-posture-redis >/dev/null 2>&1 || true
        docker network rm beautiful-posture-network >/dev/null 2>&1 || true
        log_success "清理完成"
    fi
}

# 主函数
main() {
    log_info "美姿姿数据库连接测试开始"
    
    # 检查参数
    if [ "$1" = "clean" ]; then
        cleanup clean
        exit 0
    fi
    
    check_docker
    create_network
    start_postgres
    start_redis
    
    log_info "等待服务完全启动..."
    sleep 5
    
    test_database_tables
    test_redis_databases
    
    show_connection_info
    
    log_success "所有测试通过！数据库配置正确"
    log_info "使用 '$0 clean' 清理测试环境"
}

# 执行主函数
main "$@"
