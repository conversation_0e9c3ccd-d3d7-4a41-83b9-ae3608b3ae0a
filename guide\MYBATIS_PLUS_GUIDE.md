# MyBatis-Plus 使用指南

美姿姿项目已在core模块中集成了MyBatis-Plus 3.5.12版本，这是支持Spring Boot 3的最新版本。

## 已配置的功能

### 1. 依赖配置
- `mybatis-plus-spring-boot3-starter`: 3.5.12 - 主要依赖
- `mybatis-plus-generator`: 3.5.12 - 代码生成器

### 2. 核心配置类
- `MyBatisPlusConfig`: 配置分页插件和乐观锁插件
- `MyMetaObjectHandler`: 自动填充处理器（创建时间、更新时间等）

### 3. 基础类
- `BaseEntity`: 基础实体类，包含通用字段
- `BaseMapper`: 基础Mapper接口

## 使用示例

### 1. 创建实体类

```java
@Data
@TableName("user")
public class User extends BaseEntity {
    
    @TableField("username")
    private String username;
    
    @TableField("email")
    private String email;
    
    @TableField("phone")
    private String phone;
}
```

### 2. 创建Mapper接口

```java
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    // 自定义查询方法
    @Select("SELECT * FROM user WHERE username = #{username}")
    User findByUsername(@Param("username") String username);
}
```

### 3. 创建Service

```java
@Service
public class UserService extends ServiceImpl<UserMapper, User> {
    
    public User findByUsername(String username) {
        return baseMapper.findByUsername(username);
    }
    
    public IPage<User> getUserPage(int current, int size) {
        Page<User> page = new Page<>(current, size);
        return this.page(page);
    }
}
```

## 主要特性

### 1. 自动填充
- `createTime`: 创建时自动填充
- `updateTime`: 插入和更新时自动填充
- `version`: 插入时填充为1（用于乐观锁）
- `deleted`: 插入时填充为0（逻辑删除标识）

### 2. 分页查询
```java
// 分页查询示例
Page<User> page = new Page<>(1, 10); // 第1页，每页10条
IPage<User> userPage = userService.page(page);
```

### 3. 条件构造器
```java
// 条件查询示例
QueryWrapper<User> queryWrapper = new QueryWrapper<>();
queryWrapper.eq("username", "admin")
           .like("email", "@gmail.com")
           .between("create_time", startTime, endTime);
List<User> users = userService.list(queryWrapper);
```

### 4. 乐观锁
```java
// 使用乐观锁更新
User user = userService.getById(1L);
user.setUsername("newUsername");
userService.updateById(user); // 会自动检查version字段
```

### 5. 逻辑删除
```java
// 逻辑删除（不会真正删除数据）
userService.removeById(1L); // deleted字段会被设置为1
```

## 配置文件

在`application.yml`中添加MyBatis-Plus配置：

```yaml
mybatis-plus:
  # 配置扫描路径
  mapper-locations: classpath*:/mapper/**/*.xml
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.deepaic.*.entity
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: false
    # 打印SQL
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      # 主键类型
      id-type: ASSIGN_ID
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

## 代码生成器使用

可以使用MyBatis-Plus的代码生成器快速生成实体类、Mapper、Service等：

```java
public class CodeGenerator {
    public static void main(String[] args) {
        FastAutoGenerator.create("*********************************************", "username", "password")
                .globalConfig(builder -> {
                    builder.author("Beautiful Posture Team") // 设置作者
                           .outputDir("src/main/java"); // 指定输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.deepaic") // 设置父包名
                           .moduleName("core") // 设置父包模块名
                           .entity("entity") // 设置entity包名
                           .mapper("mapper") // 设置mapper包名
                           .service("service") // 设置service包名
                           .serviceImpl("service.impl"); // 设置serviceImpl包名
                })
                .strategyConfig(builder -> {
                    builder.addInclude("user", "role") // 设置需要生成的表名
                           .entityBuilder()
                           .enableLombok() // 启用lombok
                           .addSuperEntityClass(BaseEntity.class) // 设置父类
                           .addSuperEntityColumns("id", "create_time", "update_time", "version", "deleted"); // 设置父类字段
                })
                .execute();
    }
}
```

## 注意事项

1. 确保数据库表设计符合MyBatis-Plus的约定
2. 实体类字段名使用驼峰命名，数据库字段使用下划线命名
3. 主键建议使用Long类型，配合雪花算法生成
4. 合理使用索引提高查询性能
5. 在生产环境中关闭SQL打印日志
