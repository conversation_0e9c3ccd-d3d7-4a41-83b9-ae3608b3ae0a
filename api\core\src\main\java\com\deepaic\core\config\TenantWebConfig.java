package com.deepaic.core.config;

import com.deepaic.core.tenant.TenantWebInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 租户Web配置类
 * 注册多租户拦截器，实现基于Sa-Token的自动租户上下文切换
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class TenantWebConfig implements WebMvcConfigurer {

    private final TenantWebInterceptor tenantWebInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tenantWebInterceptor)
                .addPathPatterns("/**")  // 拦截所有请求
                .excludePathPatterns(
                    "/actuator/**",      // 排除健康检查
                    "/error",            // 排除错误页面
                    "/favicon.ico",      // 排除图标
                    "/static/**",        // 排除静态资源
                    "/public/**",        // 排除公共资源
                    "/swagger-ui/**",    // 排除Swagger UI
                    "/v3/api-docs/**"    // 排除API文档
                );
    }
}
