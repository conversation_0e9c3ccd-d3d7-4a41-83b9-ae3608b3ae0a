# PostgreSQL 多租户 Schema 隔离指南

美姿姿项目实现了基于PostgreSQL Schema的多租户隔离方案，使用`SET search_path`动态切换schema，实现不同租户使用不同的数据库schema。

## 架构优势

### 1. 使用 SET search_path 的优势
- **性能优异**: 不需要修改SQL语句，直接在连接级别设置schema
- **透明性**: 业务代码无需感知多租户逻辑
- **安全性**: 通过数据库级别的schema隔离，确保数据安全
- **灵活性**: 可以动态创建和删除租户schema

### 2. 与SQL重写方案对比
| 特性 | SET search_path | SQL重写 |
|------|----------------|---------|
| 性能 | 高 | 中等 |
| 复杂度 | 低 | 高 |
| 维护性 | 好 | 一般 |
| 透明性 | 完全透明 | 需要处理复杂SQL |

## 核心组件

### 1. 租户上下文管理 (`TenantContext`)
- 使用ThreadLocal存储当前线程的租户信息
- 提供线程安全的租户schema设置和获取
- 支持租户schema的验证和清理

### 2. MyBatis-Plus拦截器 (`TenantSchemaInterceptor`)
- 在SQL执行前自动设置数据库连接的search_path
- 支持查询和更新操作的租户隔离
- 自动管理连接的原始search_path恢复

### 3. Web拦截器 (`TenantWebInterceptor`)
- 从HTTP请求中解析租户信息
- 自动设置和清理租户上下文
- 支持多种租户解析策略

### 4. 基于Sa-Token的自动租户识别
- 用户登录后自动根据Sa-Token获取租户信息
- 无需手动传递租户参数
- 自动设置租户上下文

### 5. Schema管理服务 (`TenantSchemaService`)
- 动态创建和删除租户schema
- 租户schema的初始化和管理
- 提供schema存在性检查

## 配置说明

### 1. 数据库配置 (application.yml)

```yaml
spring:
  datasource:
    druid:
      url: **************************************************
      username: your_username
      password: your_password
      driver-class-name: org.postgresql.Driver
      
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      
      # PostgreSQL特定配置
      connection-properties: stringtype=unspecified
      
# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### 2. 租户解析配置

支持多种租户识别方式：

#### HTTP Header方式
```http
GET /api/users
X-Tenant-Schema: tenant1
```

#### 请求参数方式
```http
GET /api/users?tenantSchema=tenant1
```

#### 子域名方式
```
https://tenant1.yourdomain.com/api/users
```

#### URL路径方式
```
https://yourdomain.com/api/tenant1/users
```

## 使用示例

### 1. 创建租户Schema

```java
@Autowired
private TenantSchemaService tenantSchemaService;

// 创建租户schema
boolean success = tenantSchemaService.createTenantSchema("tenant1");

// 初始化租户schema的表结构
String initSql = """
    CREATE TABLE users (
        id BIGINT PRIMARY KEY,
        username VARCHAR(50) NOT NULL,
        email VARCHAR(100),
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """;
tenantSchemaService.initializeTenantSchema("tenant1", initSql);
```

### 2. 在代码中使用租户上下文

```java
@Service
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    public List<User> getAllUsers() {
        // 自动使用当前租户的schema
        return userMapper.selectList(null);
    }
    
    public void processWithSpecificTenant() {
        // 在指定租户schema下执行操作
        TenantContext.runWithTenantSchema("tenant2", () -> {
            List<User> users = userMapper.selectList(null);
            // 处理tenant2的用户数据
        });
    }
}
```

### 3. 实体类定义

```java
@Data
@TableName("users")
public class User extends BaseEntity {
    
    @TableField("username")
    private String username;
    
    @TableField("email")
    private String email;
    
    // 其他字段...
}
```

## 数据库Schema设计

### 1. 租户Schema结构

```sql
-- 为每个租户创建独立的schema
CREATE SCHEMA tenant1;
CREATE SCHEMA tenant2;

-- 在每个schema中创建相同的表结构
-- tenant1.users, tenant1.orders, etc.
-- tenant2.users, tenant2.orders, etc.
```

### 2. 公共Schema (public)

```sql
-- 公共配置表，所有租户共享
CREATE TABLE public.tenant_config (
    tenant_id VARCHAR(50) PRIMARY KEY,
    tenant_name VARCHAR(100) NOT NULL,
    schema_name VARCHAR(50) NOT NULL,
    status INTEGER DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 最佳实践

### 1. 租户Schema命名规范
- 使用小写字母和下划线
- 避免使用PostgreSQL保留字
- 建议格式：`tenant_xxx` 或 `company_xxx`

### 2. 连接池配置
- 合理设置连接池大小，考虑多租户并发访问
- 启用连接验证，确保连接可用性
- 配置合适的连接超时时间

### 3. 安全考虑
- 验证租户schema名称，防止SQL注入
- 限制租户可访问的schema范围
- 记录租户操作日志

### 4. 性能优化
- 为每个租户schema的表创建合适的索引
- 定期分析表统计信息
- 监控不同租户的资源使用情况

## 监控和维护

### 1. 租户Schema监控

```java
@RestController
@RequestMapping("/admin/tenant")
public class TenantAdminController {
    
    @Autowired
    private TenantSchemaService tenantSchemaService;
    
    @GetMapping("/schemas")
    public List<String> getAllTenantSchemas() {
        return tenantSchemaService.getAllTenantSchemas();
    }
    
    @PostMapping("/schema/{tenantSchema}")
    public ResponseEntity<String> createTenantSchema(@PathVariable String tenantSchema) {
        boolean success = tenantSchemaService.createTenantSchema(tenantSchema);
        return success ? ResponseEntity.ok("创建成功") : ResponseEntity.badRequest().body("创建失败");
    }
}
```

### 2. 租户上下文监控

```java
@Component
public class TenantMonitor {

    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void logTenantStats() {
        if (TenantContext.hasTenant()) {
            log.info("当前租户上下文: {}", TenantContext.getTenantSummary());
        }
    }
}
```

## 故障排除

### 1. 常见问题

**问题**: Schema不存在错误
**解决**: 检查租户schema是否已创建，使用`TenantSchemaService.createTenantSchema()`创建

**问题**: 权限不足错误
**解决**: 确保数据库用户有创建schema和访问schema的权限

**问题**: 连接池耗尽
**解决**: 检查连接是否正确关闭，调整连接池配置

### 2. 调试技巧

- 启用SQL日志查看实际执行的SQL
- 使用`SHOW search_path`检查当前连接的schema设置
- 监控租户上下文的设置和清理过程

## 扩展功能

### 1. 自定义租户上下文设置

```java
@Component
public class CustomTenantHandler {

    public void setCustomTenantContext(String tenantCode) {
        // 实现自定义的租户上下文设置逻辑
        TenantUtils.runInTenant(tenantCode, "tenant_" + tenantCode, () -> {
            // 在指定租户上下文中执行操作
        });
    }
}
```

### 2. 租户数据迁移

```java
@Service
public class TenantMigrationService {
    
    public void migrateTenantData(String fromSchema, String toSchema) {
        // 实现租户数据迁移逻辑
    }
}
```

这种基于PostgreSQL Schema的多租户方案提供了高性能、高安全性的数据隔离，同时保持了代码的简洁性和可维护性。
