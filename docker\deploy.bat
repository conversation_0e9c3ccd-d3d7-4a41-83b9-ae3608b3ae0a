@echo off
REM 美姿姿 - 分布式部署脚本 (Windows版本)
REM 支持分别部署基础设施和各个API服务

setlocal enabledelayedexpansion

REM 默认配置
set ENV_FILE=.env
set PROFILE=
set DETACH=true
set FORCE_BUILD=false
set ACTION=deploy
set SERVICE=

REM 解析命令行参数
:parse_args
if "%~1"=="" goto check_service
if "%~1"=="-h" goto show_help
if "%~1"=="--help" goto show_help
if "%~1"=="-e" (
    set ENV_FILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--env" (
    set ENV_FILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-p" (
    set PROFILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--profile" (
    set PROFILE=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-f" (
    set FORCE_BUILD=true
    shift
    goto parse_args
)
if "%~1"=="--force" (
    set FORCE_BUILD=true
    shift
    goto parse_args
)
if "%~1"=="--stop" (
    set ACTION=stop
    shift
    goto parse_args
)
if "%~1"=="--restart" (
    set ACTION=restart
    shift
    goto parse_args
)
if "%~1"=="--logs" (
    set ACTION=logs
    shift
    goto parse_args
)
if "%~1"=="--status" (
    set ACTION=status
    shift
    goto parse_args
)
if "%~1"=="infrastructure" (
    set SERVICE=infrastructure
    shift
    goto parse_args
)
if "%~1"=="admin-api" (
    set SERVICE=admin-api
    shift
    goto parse_args
)
if "%~1"=="app-api" (
    set SERVICE=app-api
    shift
    goto parse_args
)
if "%~1"=="client-api" (
    set SERVICE=client-api
    shift
    goto parse_args
)
if "%~1"=="all" (
    set SERVICE=all
    shift
    goto parse_args
)
echo [ERROR] 未知参数: %~1
goto show_help

:check_service
if "%SERVICE%"=="" (
    echo [ERROR] 请指定要操作的服务
    goto show_help
)

REM 主逻辑
echo [INFO] 美姿姿分布式部署脚本启动

REM 检查Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装或不在PATH中
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装或不在PATH中
    exit /b 1
)

echo [INFO] Docker 和 Docker Compose 检查通过

REM 检查环境变量文件
if not exist "%ENV_FILE%" (
    echo [WARNING] 环境变量文件 %ENV_FILE% 不存在
    if exist ".env.example" (
        echo [INFO] 复制 .env.example 到 %ENV_FILE%
        copy .env.example %ENV_FILE%
        echo [WARNING] 请编辑 %ENV_FILE% 文件配置正确的环境变量
        pause
        exit /b 1
    ) else (
        echo [ERROR] 找不到环境变量文件模板
        exit /b 1
    )
)
echo [INFO] 使用环境变量文件: %ENV_FILE%

REM 创建网络
docker network ls | findstr "beautiful-posture-network" >nul
if errorlevel 1 (
    echo [INFO] 创建Docker网络: beautiful-posture-network
    docker network create beautiful-posture-network --driver bridge --subnet=172.20.0.0/16
) else (
    echo [INFO] Docker网络已存在: beautiful-posture-network
)

REM 执行操作
if "%ACTION%"=="deploy" goto deploy
if "%ACTION%"=="stop" goto stop
if "%ACTION%"=="restart" goto restart
if "%ACTION%"=="logs" goto logs
if "%ACTION%"=="status" goto status

:deploy
if "%SERVICE%"=="infrastructure" goto deploy_infrastructure
if "%SERVICE%"=="admin-api" goto deploy_api
if "%SERVICE%"=="app-api" goto deploy_api
if "%SERVICE%"=="client-api" goto deploy_api
if "%SERVICE%"=="all" goto deploy_all
goto end

:deploy_infrastructure
echo [INFO] 部署基础设施组件...
set COMPOSE_CMD=docker-compose -f docker-compose.infrastructure.yml --env-file %ENV_FILE%
if not "%PROFILE%"=="" set COMPOSE_CMD=%COMPOSE_CMD% --profile %PROFILE%
if "%FORCE_BUILD%"=="true" %COMPOSE_CMD% build --no-cache
%COMPOSE_CMD% up -d
echo [SUCCESS] 基础设施部署完成
goto end

:deploy_api
echo [INFO] 部署 %SERVICE% 服务...
set COMPOSE_FILE=docker-compose.%SERVICE%.yml
if not exist "%COMPOSE_FILE%" (
    echo [ERROR] 找不到配置文件: %COMPOSE_FILE%
    exit /b 1
)
set COMPOSE_CMD=docker-compose -f %COMPOSE_FILE% --env-file %ENV_FILE%
if "%FORCE_BUILD%"=="true" %COMPOSE_CMD% build --no-cache
%COMPOSE_CMD% up -d
echo [SUCCESS] %SERVICE% 服务部署完成
goto end

:deploy_all
echo [INFO] 部署基础设施组件...
set COMPOSE_CMD=docker-compose -f docker-compose.yml --env-file %ENV_FILE%
if "%FORCE_BUILD%"=="true" %COMPOSE_CMD% build --no-cache
%COMPOSE_CMD% up -d
echo [SUCCESS] 基础设施部署完成
goto end

:stop
echo [INFO] 停止 %SERVICE% 服务...
if "%SERVICE%"=="infrastructure" (
    docker-compose -f docker-compose.infrastructure.yml --env-file %ENV_FILE% down
) else if "%SERVICE%"=="all" (
    docker-compose -f docker-compose.yml --env-file %ENV_FILE% down
) else (
    docker-compose -f docker-compose.%SERVICE%.yml --env-file %ENV_FILE% down
)
echo [SUCCESS] %SERVICE% 服务已停止
goto end

:restart
echo [INFO] 重启 %SERVICE% 服务...
call %0 --stop %SERVICE%
timeout /t 2 /nobreak >nul
call %0 %SERVICE%
goto end

:logs
echo [INFO] 查看 %SERVICE% 服务日志...
if "%SERVICE%"=="infrastructure" (
    docker-compose -f docker-compose.infrastructure.yml --env-file %ENV_FILE% logs -f
) else if "%SERVICE%"=="all" (
    docker-compose -f docker-compose.yml --env-file %ENV_FILE% logs -f
) else (
    docker-compose -f docker-compose.%SERVICE%.yml --env-file %ENV_FILE% logs -f
)
goto end

:status
echo [INFO] 查看 %SERVICE% 服务状态...
if "%SERVICE%"=="infrastructure" (
    docker-compose -f docker-compose.infrastructure.yml --env-file %ENV_FILE% ps
) else if "%SERVICE%"=="all" (
    docker-compose -f docker-compose.yml --env-file %ENV_FILE% ps
) else (
    docker-compose -f docker-compose.%SERVICE%.yml --env-file %ENV_FILE% ps
)
goto end

:show_help
echo 美姿姿 - 分布式部署脚本
echo.
echo 用法: %0 [选项] ^<服务^>
echo.
echo 服务选项:
echo   infrastructure    部署基础设施组件（数据库、缓存、监控等）
echo   admin-api        部署管理后台API服务
echo   app-api          部署移动应用API服务
echo   client-api       部署客户端API服务
echo   all              部署基础设施（等同于infrastructure）
echo   full             部署所有服务（单机完整模式）
echo.
echo 选项:
echo   -h, --help       显示此帮助信息
echo   -e, --env FILE   指定环境变量文件（默认: .env）
echo   -p, --profile    指定Docker Compose profile
echo   -f, --force      强制重新构建镜像
echo   --stop           停止指定服务
echo   --restart        重启指定服务
echo   --logs           查看服务日志
echo   --status         查看服务状态
echo.
echo 示例:
echo   %0 infrastructure              # 部署基础设施
echo   %0 admin-api -f                # 强制重新构建并部署Admin API
echo   %0 app-api -p monitoring       # 部署App API并启用监控
echo   %0 --stop client-api           # 停止Client API服务
echo   %0 --logs admin-api            # 查看Admin API日志
goto end

:end
echo [SUCCESS] 操作完成
