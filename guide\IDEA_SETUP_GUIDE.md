# IntelliJ IDEA 项目设置指南

本指南帮助您正确在IntelliJ IDEA中导入和配置美姿姿项目。

## 🚀 快速导入步骤

### 1. 准备工作

确保您的环境满足要求：
- ✅ Java 21 LTS
- ✅ IntelliJ IDEA 2023.3或更高版本
- ✅ Gradle 8.13或更高版本

### 2. 导入项目

#### 步骤1：关闭现有项目
如果IDEA中已经打开了项目：
- File → Close Project

#### 步骤2：清理IDEA配置（如果存在问题）
```bash
# 在beautiful-posture根目录执行
rm -rf .idea/
rm -rf .gradle/
rm -rf */build/

# Windows用户可以手动删除这些文件夹
```

#### 步骤3：导入项目
1. 打开IntelliJ IDEA
2. 选择 **"Open or Import"**
3. 导航到 `beautiful-posture` 根目录
4. 选择根目录（包含`build.gradle`和`settings.gradle`的目录）
5. 点击 **"OK"**

#### 步骤4：配置导入选项
在导入对话框中：
- ✅ 选择 **"Use Gradle from: gradle-wrapper.properties file"**
- ✅ 选择 **"Build and run using: Gradle"**
- ✅ 选择 **"Run tests using: Gradle"**
- ✅ Gradle JVM: 选择 **"Project SDK (Java 21)"**

### 3. 验证项目结构

导入完成后，项目结构应该如下：

```
beautiful-posture
├── 📁 admin-api
├── 📁 app-api
├── 📁 client-api
├── 📁 core
├── 📁 service
├── 📁 docker
├── 📁 guide
├── 📁 gradle
├── 📄 build.gradle
├── 📄 settings.gradle
└── 📄 gradlew
```

## 🔧 常见问题解决

### 问题1: "Project with path ':core' could not be found"

**原因**: IDEA将子目录识别为根项目，通常是因为子模块中存在多余的Gradle文件

**解决方案**:

#### 方法1: 使用修复脚本（推荐）
```bash
# Windows
fix-idea-project.bat

# Linux/Mac
./fix-idea-project.sh
```

#### 方法2: 手动清理
1. 关闭IDEA项目
2. 删除子模块中的多余文件：
   ```bash
   # 删除每个子模块中的这些文件
   rm -f */settings.gradle
   rm -f */gradlew
   rm -f */gradlew.bat
   rm -rf */gradle/
   ```
3. 删除`.idea`文件夹
4. 重新导入项目，确保选择根目录

### 问题2: Gradle同步失败

**解决方案**:
1. 检查网络连接
2. 清理Gradle缓存：
   ```bash
   ./gradlew clean
   rm -rf ~/.gradle/caches/
   ```
3. 重新同步：View → Tool Windows → Gradle → 点击刷新按钮

### 问题3: Java版本不匹配

**解决方案**:
1. File → Project Structure → Project
2. 设置Project SDK为Java 21
3. 设置Project language level为21

### 问题4: 模块依赖错误

**解决方案**:
1. File → Project Structure → Modules
2. 检查每个模块的Dependencies
3. 确保模块间依赖正确：
   - admin-api → core, service
   - app-api → core, service
   - client-api → core, service
   - service → core

## ⚙️ IDEA配置优化

### 1. Gradle设置

File → Settings → Build, Execution, Deployment → Build Tools → Gradle

```
✅ Use Gradle from: gradle-wrapper.properties file
✅ Gradle JVM: Project SDK (Java 21)
✅ Build and run using: Gradle
✅ Run tests using: Gradle
✅ Download external annotations: ✓
✅ Download sources: ✓
✅ Download documentation: ✓
```

### 2. Java编译器设置

File → Settings → Build, Execution, Deployment → Compiler → Java Compiler

```
✅ Project bytecode version: 21
✅ Use '--release' option for cross-compilation: ✓
```

### 3. 代码风格设置

File → Settings → Editor → Code Style → Java

推荐导入项目的代码风格配置（如果有的话）。

### 4. Lombok插件

确保安装并启用Lombok插件：
1. File → Settings → Plugins
2. 搜索"Lombok"
3. 安装并启用
4. File → Settings → Build, Execution, Deployment → Compiler → Annotation Processors
5. ✅ Enable annotation processing

### 5. MyBatis插件（可选）

推荐安装MyBatis相关插件：
- MyBatisX
- MyBatis Log Plugin

## 🏃‍♂️ 运行和调试

### 1. 运行应用

#### 方式1: 使用Gradle任务
1. 打开Gradle工具窗口：View → Tool Windows → Gradle
2. 展开项目 → admin-api → Tasks → application
3. 双击 `bootRun`

#### 方式2: 创建运行配置
1. Run → Edit Configurations
2. 点击 "+" → Gradle
3. 配置：
   - Name: Admin API
   - Gradle project: :admin-api
   - Tasks: bootRun
   - Arguments: --args='--server.port=8080'

### 2. 调试应用

1. 在代码中设置断点
2. 右键运行配置 → Debug
3. 或使用Gradle任务的Debug模式

### 3. 运行测试

#### 运行所有测试
```bash
./gradlew test
```

#### 运行特定模块测试
```bash
./gradlew :core:test
```

#### 在IDEA中运行测试
- 右键测试类 → Run 'TestClassName'
- 或使用快捷键 Ctrl+Shift+F10

## 📊 性能优化

### 1. 增加IDEA内存

在`idea64.exe.vmoptions`中：
```
-Xms2048m
-Xmx4096m
-XX:ReservedCodeCacheSize=1024m
```

### 2. Gradle性能优化

在`gradle.properties`中：
```properties
org.gradle.jvmargs=-Xmx2048m -XX:MaxMetaspaceSize=512m
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.daemon=true
```

### 3. 排除不必要的文件

在`.gitignore`中确保排除：
```
.idea/
.gradle/
build/
*.iml
*.iws
*.ipr
```

## 🔍 故障排除

### 重置项目配置

如果遇到无法解决的问题：

```bash
# 1. 关闭IDEA
# 2. 清理所有配置
rm -rf .idea/
rm -rf .gradle/
rm -rf */build/

# 3. 重新导入项目
```

### 检查日志

如果导入失败，查看IDEA日志：
- Help → Show Log in Explorer
- 查看`idea.log`文件

### 联系支持

如果问题仍然存在：
1. 收集错误信息和日志
2. 检查IDEA版本兼容性
3. 考虑升级IDEA到最新版本

---

按照这个指南，您应该能够成功在IDEA中导入和配置美姿姿项目。如果遇到问题，请按照故障排除部分的步骤操作。
