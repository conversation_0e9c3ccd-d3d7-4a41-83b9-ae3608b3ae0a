package com.deepaic.core.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据源配置类
 * 配置PostgreSQL数据源，使用HikariCP连接池，支持多租户schema
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class DataSourceConfig {

    @Value("${spring.datasource.url}")
    private String url;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    /**
     * 主数据源配置
     * 使用HikariCP连接池（Spring Boot默认）
     * 手动配置以确保属性正确映射
     */
    @Bean
    @Primary
    public HikariDataSource dataSource() {
        HikariConfig config = new HikariConfig();

        // 基本连接信息
        config.setJdbcUrl(url);  // HikariCP使用jdbcUrl而不是url
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName(driverClassName);

        // 应用HikariCP特定配置
        applyHikariConfig(config);

        HikariDataSource dataSource = new HikariDataSource(config);
        log.info("PostgreSQL DataSource configured successfully - using HikariCP connection pool");
        return dataSource;
    }

    /**
     * 应用HikariCP配置
     */
    private void applyHikariConfig(HikariConfig config) {
        // 连接池配置
        config.setMinimumIdle(5);
        config.setMaximumPoolSize(20);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        config.setLeakDetectionThreshold(60000);

        // 连接池名称
        config.setPoolName("HikariCP-PostgreSQL");

        // 连接测试
        config.setConnectionTestQuery("SELECT 1");

        // 性能优化配置
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
    }
}
