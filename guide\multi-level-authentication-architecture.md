# 多层级认证架构设计

## 🎯 架构概述

在SaaS平台中，我们设计了三层用户认证体系，满足不同角色和场景的登录需求：

### 用户类型分层

```
SaaS平台多层级用户体系
├── 🏢 平台用户 (Platform Users)
│   ├── 超级管理员 - 拥有最高权限
│   ├── 平台管理员 - 管理租户和系统
│   ├── 运营人员 - 运营数据分析
│   └── 技术支持 - 技术支持服务
├── 👥 客户账户 (Customer Accounts)
│   ├── 租户管理员 - 管理租户内部
│   ├── 租户用户 - 使用租户功能
│   └── 租户操作员 - 日常操作
└── 📱 会员账户 (Member Accounts)
    ├── 小程序会员 - 微信小程序用户
    ├── H5会员 - 网页端用户
    └── APP会员 - 移动应用用户
```

## 🗄️ 数据库设计

### 1. 平台账户表 (pub_platform_account)
```sql
-- 存储在公共schema，用于平台级别管理
CREATE TABLE pub_platform_account (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,           -- 用户名
    password VARCHAR(100) NOT NULL,          -- 加密密码
    email VARCHAR(100),                      -- 邮箱
    phone VARCHAR(20),                       -- 手机号
    real_name VARCHAR(50),                   -- 真实姓名
    account_type INTEGER DEFAULT 2,          -- 账户类型
    permission_level INTEGER DEFAULT 1,      -- 权限级别
    status INTEGER DEFAULT 1,                -- 状态
    -- 其他字段...
);
```

### 2. 客户账户表 (pub_customer_account)
```sql
-- 存储在公共schema，按租户隔离
CREATE TABLE pub_customer_account (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,           -- 用户名
    password VARCHAR(100) NOT NULL,          -- 加密密码
    tenant_code VARCHAR(50) NOT NULL,        -- 租户编码
    account_type INTEGER DEFAULT 3,          -- 账户类型
    status INTEGER DEFAULT 1,                -- 状态
    -- 其他字段...
);
```

### 3. 会员账户表 (pub_member_account)
```sql
-- 存储在公共schema，映射到租户schema中的会员
CREATE TABLE pub_member_account (
    id BIGSERIAL PRIMARY KEY,
    login_type INTEGER NOT NULL,             -- 登录类型
    login_identifier VARCHAR(200) NOT NULL,  -- 登录标识
    tenant_code VARCHAR(50) NOT NULL,        -- 租户编码
    member_id BIGINT NOT NULL,               -- 租户内会员ID
    member_no VARCHAR(50) NOT NULL,          -- 会员编号
    -- 其他字段...
);
```

## 🔐 认证流程设计

### 1. 平台账户登录流程

```mermaid
sequenceDiagram
    participant 管理员 as 平台管理员
    participant PlatformAPI as platform-api
    participant UnifiedAuth as 统一认证服务
    participant DB as 公共数据库

    管理员->>PlatformAPI: 平台登录请求
    PlatformAPI->>UnifiedAuth: platformLogin()
    UnifiedAuth->>DB: 查询平台账户
    DB-->>UnifiedAuth: 返回账户信息
    UnifiedAuth->>UnifiedAuth: 验证密码和权限
    UnifiedAuth->>UnifiedAuth: 生成令牌 (platform:accountId)
    UnifiedAuth-->>PlatformAPI: 返回登录结果
    PlatformAPI-->>管理员: 返回访问令牌
```

### 2. 客户账户登录流程

```mermaid
sequenceDiagram
    participant 客户 as 租户用户
    participant AdminAPI as admin-api
    participant UnifiedAuth as 统一认证服务
    participant DB as 公共数据库
    participant TenantCtx as 租户上下文
    
    客户->>AdminAPI: 客户登录请求 (含租户编码)
    AdminAPI->>UnifiedAuth: customerLogin()
    UnifiedAuth->>DB: 查询客户账户
    DB-->>UnifiedAuth: 返回账户信息
    UnifiedAuth->>UnifiedAuth: 验证密码和租户
    UnifiedAuth->>TenantCtx: 设置租户上下文
    UnifiedAuth->>UnifiedAuth: 生成令牌 (customer:accountId)
    UnifiedAuth-->>AdminAPI: 返回登录结果
    AdminAPI-->>客户: 返回访问令牌
```

### 3. 会员账户登录流程

```mermaid
sequenceDiagram
    participant 会员 as 小程序会员
    participant AppAPI as app-api
    participant MemberAuth as 会员认证服务
    participant 公共DB as 公共Schema
    participant 租户DB as 租户Schema
    
    会员->>AppAPI: 会员登录请求 (微信/手机号)
    AppAPI->>MemberAuth: wechatMiniLogin()
    MemberAuth->>公共DB: 查询会员账户映射
    alt 会员已存在
        公共DB-->>MemberAuth: 返回租户信息
        MemberAuth->>租户DB: 查询会员详情
        租户DB-->>MemberAuth: 返回会员信息
    else 新会员
        MemberAuth->>MemberAuth: 确定租户
        MemberAuth->>租户DB: 创建会员
        MemberAuth->>公共DB: 创建账户映射
    end
    MemberAuth->>MemberAuth: 生成令牌 (member:memberId)
    MemberAuth-->>AppAPI: 返回登录结果
    AppAPI-->>会员: 返回访问令牌
```

## 🚀 API接口设计

### 1. Platform API (平台管理)

#### 认证接口 (/api/platform/auth)
```
POST /login              # 平台用户登录
POST /logout             # 平台用户登出
GET  /current-user       # 获取当前平台用户信息
GET  /check-permission   # 权限检查
```

#### 使用示例
```javascript
// 平台用户登录
fetch('/api/platform/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'superadmin',
    password: 'admin123'
  })
});
```

### 2. Admin API (租户管理)

#### 认证接口 (/api/admin/auth)
```
POST /login              # 客户账户登录
POST /logout             # 客户账户登出
GET  /current-user       # 获取当前客户账户信息
GET  /check-login        # 检查登录状态
GET  /login-info         # 获取登录信息
```

#### 使用示例
```javascript
// 客户账户登录
fetch('/api/admin/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    username: 'admin',
    password: 'admin123',
    tenantCode: 'demo'
  })
});
```

### 3. App API (小程序)

#### 认证接口 (/api/app/auth)
```
POST /wechat-mini-login  # 微信小程序登录
POST /phone-login        # 手机号登录
POST /logout             # 会员登出
POST /refresh-token      # 刷新令牌
GET  /current-member     # 获取当前会员信息
GET  /check-login        # 检查登录状态
```

#### 使用示例
```javascript
// 微信小程序登录
wx.login({
  success: (res) => {
    wx.request({
      url: '/api/app/auth/wechat-mini-login',
      method: 'POST',
      data: {
        code: res.code,
        platform: 1,
        userInfo: { /* 微信用户信息 */ }
      }
    });
  }
});
```

## 🔑 令牌设计

### 令牌格式
```
平台账户: platform:{accountId}  # 例: platform:1
客户账户: customer:{accountId}   # 例: customer:100
会员账户: member:{memberId}      # 例: member:1000
```

### 权限验证
```java
// 获取当前用户信息
String loginId = StpUtil.getLoginIdAsString();
String[] parts = loginId.split(":");
String userType = parts[0];  // platform/customer/member
Long accountId = Long.valueOf(parts[1]);

// 根据用户类型进行不同的权限验证
switch (userType) {
    case "platform":
        // 平台账户权限验证
        break;
    case "customer":
        // 客户账户权限验证
        break;
    case "member":
        // 会员权限验证
        break;
}
```

## 🎯 使用场景

### 1. 平台管理场景
- **超级管理员**: 管理整个SaaS平台
- **平台管理员**: 管理租户、监控系统
- **运营人员**: 数据分析、用户运营
- **技术支持**: 技术支持、问题处理

### 2. 租户管理场景
- **租户管理员**: 管理租户内部用户和权限
- **租户用户**: 使用租户提供的功能
- **租户操作员**: 日常业务操作

### 3. 会员服务场景
- **小程序会员**: 通过微信小程序使用服务
- **H5会员**: 通过网页端使用服务
- **APP会员**: 通过移动应用使用服务

## ✅ 架构优势

### 1. **清晰的职责分离**
- 平台级、租户级、会员级权限完全分离
- 不同类型用户使用不同的认证流程

### 2. **安全性保障**
- 多层级的权限验证
- 不同用户类型的令牌隔离
- 完整的数据隔离

### 3. **扩展性强**
- 支持新增用户类型
- 支持自定义权限级别
- 支持多种登录方式

### 4. **易于管理**
- 统一的认证服务
- 标准化的API接口
- 完整的日志记录

## 🎉 总结

这个多层级认证架构完美解决了SaaS平台中不同类型用户的认证需求：

1. **平台账户** - 管理整个SaaS平台
2. **客户账户** - 管理租户内部业务
3. **会员账户** - 使用租户提供的服务

每种用户类型都有独立的认证流程、权限体系和API接口，确保了系统的安全性、可扩展性和易维护性！🚀
