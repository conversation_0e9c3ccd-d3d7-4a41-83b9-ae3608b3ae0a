# 数据库接口优化总结

## 概述

根据提供的数据库连接信息（8.153.193.114:5432/mzz），对 `api/` 目录中的接口进行了全面优化，使其与数据库表字段保持一致。

## 数据库连接配置

### 连接信息
- **主机**: 8.153.193.114
- **端口**: 5432
- **数据库**: mzz
- **用户名**: postgres
- **密码**: deepaic!2025

### 配置文件更新
1. **`api/core/src/main/resources/application-common.yml`**
   - 更新数据库连接URL
   - 配置MyBatis Plus使用雪花算法ID生成
   - 调整逻辑删除配置为布尔类型

2. **`api/platform-api/src/main/resources/application.yml`**
   - 更新平台API的数据库连接配置

## 实体类优化

### 1. BaseEntity 基础实体类
**优化内容：**
- 字段名调整：`createTime` → `createdAt`，`updateTime` → `updatedAt`
- 新增：`createdBy`、`updatedBy` 字段
- 逻辑删除字段类型：`Integer` → `Boolean`
- 所有字段映射到正确的数据库列名

### 2. Tenant 租户实体类
**优化内容：**
- 移除重复字段定义
- 根据数据库表结构调整字段
- 新增：`expireTime`、`maxStorage`、`features`、`settings` 字段
- 更新租户类型常量：1试用 2标准 3专业 4企业
- 修复业务方法中的字段引用

### 3. CustomerAccount 租户用户账户类
**优化内容：**
- 支持多种登录方式：用户名密码、微信、手机、邮箱
- 新增：`loginType`、`loginIdentifier`、`wechatOpenid`、`wechatUnionid` 字段
- 新增：`bindTime`、`sysUserId`、`isPrimary` 字段
- 更新账户类型常量：1管理员 2普通用户
- 添加登录类型常量和相关业务方法

### 4. User 用户实体类
**优化内容：**
- 新增：`username`、`password` 字段
- 调整字段映射：`realName`、`employeeNo`、`entryDate`
- 移除：`departmentId`、`position` 字段（使用组织机构替代）
- 新增：`lastLoginTime`、`lastLoginIp` 字段
- 更新状态常量：增加锁定状态

### 5. 新增实体类

#### Organization 组织机构实体类
- 替代原有的部门概念
- 支持层级结构：`parentId`、`ancestors`、`orgLevel`
- 组织类型：公司、部门、小组、虚拟组织
- 负责人管理：`leaderId`、`leaderName`

#### Permission 权限实体类
- 权限类型：组织权限、数据权限、字段权限、功能权限
- 权限范围：全部、本组织、本组织及下级、自定义、仅本人
- 支持JSON格式的权限范围配置

#### UserOrganization 用户组织关联类
- 支持用户属于多个组织
- 主组织标识：`isPrimary`
- 负责人标识：`isLeader`
- 职务管理：`jobTitle`
- 状态管理：在职/离职

#### RolePermission 角色权限关联类
- 简化的关联表设计
- 只包含必要的关联字段和创建时间

#### RoleOrganization 角色组织关联类
- 角色与组织的关联管理
- 支持角色的数据权限范围控制

## 工具类和配置

### 1. DatabaseTestUtil 数据库测试工具
**功能：**
- 数据库连接测试
- 表结构验证
- 字段信息检查
- 记录数统计
- 索引验证

### 2. DatabaseInitializer 数据库初始化器
**功能：**
- 自动检测数据库初始化状态
- 执行SQL初始化脚本
- 创建演示租户和管理员账户
- 错误处理和日志记录

## 数据库表结构对应

### 公共表 (public schema)
| 实体类 | 数据库表 | 主要字段 |
|--------|----------|----------|
| Tenant | pub_tenant | tenant_code, tenant_name, schema_name |
| CustomerAccount | pub_customer_account | tenant_code, login_type, login_identifier |
| - | pub_platform_account | username, password, permission_level |
| - | pub_member_account | tenant_code, member_id, login_type |

### 租户表 (tenant schema)
| 实体类 | 数据库表 | 主要字段 |
|--------|----------|----------|
| User | sys_user | user_code, username, password |
| Role | sys_role | role_name, role_code, data_scope |
| Menu | sys_menu | menu_name, parent_id, menu_type |
| Organization | sys_organization | org_code, org_name, org_type |
| Permission | sys_permission | permission_name, permission_code, permission_type |
| UserOrganization | sys_user_organization | user_id, org_id, is_primary |
| RolePermission | sys_role_permission | role_id, permission_id |
| RoleOrganization | sys_role_organization | role_id, org_id |

## 主要优化特性

### 1. 字段约束优化
- 必要字段设置为 NOT NULL
- 合理的默认值设置
- 布尔类型字段统一使用 Boolean

### 2. 松耦合设计
- 不使用外键约束
- 逻辑关联通过应用层维护
- 提高系统灵活性和性能

### 3. 多租户支持
- 完善的租户隔离机制
- 支持多种登录方式
- 灵活的权限管理体系

### 4. 扩展性设计
- 支持用户多组织归属
- 可扩展的权限类型
- JSON格式的配置存储

## 使用说明

### 1. 启动应用
应用启动时会自动：
- 测试数据库连接
- 验证表结构
- 执行初始化脚本（如需要）
- 创建演示数据

### 2. 演示账户
- **租户代码**: demo_tenant
- **用户名**: admin
- **密码**: admin123

### 3. 开发建议
- 使用提供的实体类进行数据操作
- 遵循松耦合设计原则
- 在应用层维护数据一致性
- 定期运行数据库测试工具验证结构

## 注意事项

1. **数据迁移**: 如果已有数据，需要编写迁移脚本适配新结构
2. **权限验证**: 需要在业务层实现完整的权限验证逻辑
3. **多租户切换**: 需要实现动态schema切换机制
4. **性能优化**: 为常用查询字段创建适当的索引
5. **数据一致性**: 定期运行一致性检查工具

## 后续工作

1. 完善业务服务层代码
2. 实现权限验证拦截器
3. 添加数据校验和异常处理
4. 编写单元测试和集成测试
5. 优化查询性能和索引策略
