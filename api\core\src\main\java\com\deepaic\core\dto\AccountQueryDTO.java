package com.deepaic.core.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 账户查询DTO
 * 
 * <AUTHOR>
 */
@Data
public class AccountQueryDTO {

    private String username;

    private String email;

    private String phone;

    private String realName;

    private String tenantCode;

    private Integer status;

    private Integer accountType;

    private LocalDateTime createTimeStart;

    private LocalDateTime createTimeEnd;

    private LocalDateTime lastLoginTimeStart;

    private LocalDateTime lastLoginTimeEnd;
}
