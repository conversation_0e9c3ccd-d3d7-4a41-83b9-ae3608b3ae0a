# Gradle 打包部署指南

美姿姿项目使用Gradle进行统一构建和打包，支持多模块项目的独立打包和部署。

## 项目结构

```
beautiful-posture/
├── build.gradle                    # 根项目构建配置
├── settings.gradle                 # 项目设置
├── gradle/
│   └── libs.versions.toml         # 版本目录
├── admin-api/                      # 管理后台API (可执行JAR)
├── app-api/                        # 移动端API (可执行JAR)
├── client-api/                     # 客户端API (可执行JAR)
├── core/                          # 核心模块 (库JAR)
├── service/                       # 业务服务模块 (库JAR)
├── build.sh                       # Linux/Mac构建脚本
├── build.bat                      # Windows构建脚本
└── docker-compose.yml             # Docker编排文件
```

## 构建命令

### 1. 基础构建命令

```bash
# 构建所有模块
./gradlew buildAll

# 清理所有模块
./gradlew cleanAll

# 构建特定模块
./gradlew :admin-api:bootJar
./gradlew :app-api:bootJar
./gradlew :client-api:bootJar
./gradlew :core:jar
./gradlew :service:jar

# 运行测试
./gradlew test

# 查看项目结构
./gradlew projects
```

### 2. 使用构建脚本

#### Linux/Mac系统
```bash
# 赋予执行权限
chmod +x build.sh

# 执行构建
./build.sh
```

#### Windows系统
```cmd
# 执行构建
build.bat
```

## 打包输出

### 1. API模块 (Spring Boot应用)

生成可执行的JAR文件：
- `admin-api/build/libs/beautiful-posture-admin-api.jar`
- `app-api/build/libs/beautiful-posture-app-api.jar`
- `client-api/build/libs/beautiful-posture-client-api.jar`

这些JAR文件包含所有依赖，可以直接运行：
```bash
java -jar beautiful-posture-admin-api.jar
```

### 2. 库模块 (Java库)

生成库JAR文件：
- `core/build/libs/beautiful-posture-core.jar`
- `service/build/libs/beautiful-posture-service.jar`

这些JAR文件用作其他模块的依赖。

## 部署方式

### 1. 传统部署

构建完成后，`deploy/`目录包含：
```
deploy/
├── beautiful-posture-admin-api.jar
├── beautiful-posture-app-api.jar
├── beautiful-posture-client-api.jar
├── start-admin-api.sh (或 .bat)
├── start-app-api.sh (或 .bat)
├── start-client-api.sh (或 .bat)
├── application.yml.example
└── version.txt
```

启动服务：
```bash
cd deploy
./start-admin-api.sh    # 启动管理后台API (端口8080)
./start-app-api.sh      # 启动移动端API (端口8081)
./start-client-api.sh   # 启动客户端API (端口8082)
```

### 2. Docker部署

#### 构建Docker镜像
```bash
# 构建所有服务的镜像
docker-compose build

# 构建特定服务的镜像
docker build -f Dockerfile.admin-api -t beautiful-posture-admin-api .
```

#### 启动服务
```bash
# 启动所有服务（包括PostgreSQL和Redis）
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f admin-api
```

#### 停止服务
```bash
# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

## 配置管理

### 1. 环境配置

每个API模块支持多环境配置：

```yaml
# application.yml
spring:
  profiles:
    active: dev  # dev, test, prod, docker

---
# 开发环境
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: **************************************************
    username: postgres
    password: postgres123

---
# 生产环境
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
```

### 2. 外部配置

支持通过以下方式覆盖配置：

#### 环境变量
```bash
export SPRING_DATASOURCE_URL=************************************************
export SPRING_DATASOURCE_USERNAME=prod_user
export SPRING_DATASOURCE_PASSWORD=prod_password
```

#### 命令行参数
```bash
java -jar beautiful-posture-admin-api.jar \
  --spring.datasource.url=************************************************** \
  --server.port=8080
```

#### 外部配置文件
```bash
# 在JAR文件同目录放置application.yml
java -jar beautiful-posture-admin-api.jar
```

## 性能优化

### 1. JVM参数优化

```bash
# 生产环境推荐JVM参数
java -Xms1g -Xmx2g \
     -XX:+UseG1GC \
     -XX:+UseContainerSupport \
     -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError \
     -XX:HeapDumpPath=/tmp/heapdump.hprof \
     -jar beautiful-posture-admin-api.jar
```

### 2. 构建优化

```gradle
// 在build.gradle中添加
tasks.withType(JavaCompile) {
    options.compilerArgs += [
        '-parameters',           // 保留参数名
        '-Xlint:unchecked',     // 显示未检查警告
        '-Xlint:deprecation'    // 显示过时警告
    ]
}

// 优化JAR大小
bootJar {
    exclude '**/*.example'
    exclude '**/test/**'
    exclude '**/*.md'
}
```

## 监控和日志

### 1. 应用监控

启用Spring Boot Actuator：
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

访问监控端点：
- 健康检查: `http://localhost:8080/actuator/health`
- 应用信息: `http://localhost:8080/actuator/info`
- 指标数据: `http://localhost:8080/actuator/metrics`

### 2. 日志配置

```yaml
logging:
  level:
    com.deepaic: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log
    max-size: 100MB
    max-history: 30
```

## 故障排除

### 1. 常见构建问题

**问题**: 构建失败，提示找不到依赖
```bash
# 解决方案：清理并重新构建
./gradlew clean build --refresh-dependencies
```

**问题**: 内存不足
```bash
# 解决方案：增加Gradle内存
export GRADLE_OPTS="-Xmx2g -XX:MaxMetaspaceSize=512m"
```

**问题**: 权限问题
```bash
# 解决方案：修复权限
chmod +x gradlew
chmod +x build.sh
```

### 2. 运行时问题

**问题**: 端口冲突
```bash
# 解决方案：修改端口
java -jar app.jar --server.port=8090
```

**问题**: 数据库连接失败
```bash
# 解决方案：检查数据库配置和网络连接
java -jar app.jar --spring.datasource.url=**************************************************
```

## 持续集成

### 1. GitHub Actions示例

```yaml
# .github/workflows/build.yml
name: Build and Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 21
      uses: actions/setup-java@v3
      with:
        java-version: '21'
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Build with Gradle
      run: ./gradlew buildAll
      
    - name: Run tests
      run: ./gradlew test
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: jar-artifacts
        path: |
          */build/libs/*.jar
```

### 2. Jenkins Pipeline示例

```groovy
pipeline {
    agent any
    
    tools {
        jdk 'JDK21'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Build') {
            steps {
                sh './gradlew buildAll'
            }
        }
        
        stage('Test') {
            steps {
                sh './gradlew test'
            }
            post {
                always {
                    publishTestResults testResultsPattern: '**/build/test-results/test/*.xml'
                }
            }
        }
        
        stage('Package') {
            steps {
                sh './build.sh'
                archiveArtifacts artifacts: 'deploy/*.jar', fingerprint: true
            }
        }
        
        stage('Docker Build') {
            steps {
                sh 'docker-compose build'
            }
        }
    }
}
```

这个Gradle打包方案提供了完整的构建、打包、部署流程，支持多种部署方式，并包含了性能优化和监控配置。
