package com.deepaic.core.auth.interfaces;

import com.deepaic.core.auth.models.UserPrincipal;

/**
 * 令牌服务接口
 * 定义令牌的生成、验证、刷新等操作
 *
 * <AUTHOR>
 */
public interface TokenService {

    /**
     * 生成访问令牌
     *
     * @param userPrincipal 用户主体
     * @return 访问令牌
     */
    String generateAccessToken(UserPrincipal userPrincipal);

    /**
     * 生成刷新令牌
     *
     * @param userPrincipal 用户主体
     * @return 刷新令牌
     */
    String generateRefreshToken(UserPrincipal userPrincipal);

    /**
     * 验证令牌
     *
     * @param token 令牌
     * @return 是否有效
     */
    boolean validateToken(String token);

    /**
     * 从令牌中解析用户主体
     *
     * @param token 令牌
     * @return 用户主体
     */
    UserPrincipal parseToken(String token);

    /**
     * 获取令牌过期时间（秒）
     *
     * @param token 令牌
     * @return 过期时间
     */
    Long getTokenExpiration(String token);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
    String refreshAccessToken(String refreshToken);

    /**
     * 撤销令牌
     *
     * @param token 令牌
     */
    void revokeToken(String token);

    /**
     * 撤销用户的所有令牌
     *
     * @param userId 用户ID
     */
    void revokeAllTokens(String userId);
}
