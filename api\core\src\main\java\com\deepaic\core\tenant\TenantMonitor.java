package com.deepaic.core.tenant;

import com.deepaic.core.config.MultiTenantProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 多租户监控组件
 * 监控租户上下文的使用情况和性能指标
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "multitenant.monitor", name = "enabled", havingValue = "true", matchIfMissing = true)
public class TenantMonitor {

    private final MultiTenantProperties properties;
    private final TenantSchemaService tenantSchemaService;

    // 性能计数器
    private final AtomicLong schemaSwithCount = new AtomicLong(0);
    private final AtomicLong contextSetupCount = new AtomicLong(0);
    private final AtomicLong slowQueryCount = new AtomicLong(0);

    /**
     * 记录Schema切换
     */
    public void recordSchemaSwitch(String fromSchema, String toSchema, long duration) {
        schemaSwithCount.incrementAndGet();
        
        if (properties.getMonitor().isLogSchemaSwitch()) {
            log.debug("Schema切换: {} -> {}, 耗时: {}ms", fromSchema, toSchema, duration);
        }

        if (properties.getMonitor().isLogPerformance() && 
            duration > properties.getMonitor().getSlowQueryThreshold()) {
            slowQueryCount.incrementAndGet();
            log.warn("Schema切换耗时过长: {} -> {}, 耗时: {}ms", fromSchema, toSchema, duration);
        }
    }

    /**
     * 记录租户上下文设置
     */
    public void recordContextSetup(String tenantCode, String username, boolean success) {
        contextSetupCount.incrementAndGet();
        
        if (properties.getMonitor().isLogSchemaSwitch()) {
            log.debug("租户上下文设置: tenant={}, user={}, success={}", 
                tenantCode, username, success);
        }
    }

    /**
     * 定期输出监控统计信息
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void logStatistics() {
        if (!properties.getMonitor().isEnabled()) {
            return;
        }

        long schemaCount = schemaSwithCount.get();
        long contextCount = contextSetupCount.get();
        long slowCount = slowQueryCount.get();

        if (schemaCount > 0 || contextCount > 0) {
            log.info("多租户监控统计 - Schema切换: {}, 上下文设置: {}, 慢操作: {}", 
                schemaCount, contextCount, slowCount);
        }
    }

    /**
     * 检查租户Schema健康状态
     */
    @Scheduled(fixedRate = 600000) // 每10分钟执行一次
    public void checkTenantSchemaHealth() {
        if (!properties.getMonitor().isEnabled()) {
            return;
        }

        try {
            var schemas = tenantSchemaService.getAllTenantSchemas();
            log.debug("当前活跃租户Schema数量: {}", schemas.size());

            // 检查是否有孤立的Schema
            // TODO: 实现孤立Schema检测逻辑

        } catch (Exception e) {
            log.error("检查租户Schema健康状态失败", e);
        }
    }

    /**
     * 获取监控指标
     */
    public MonitorMetrics getMetrics() {
        return MonitorMetrics.builder()
            .schemaSwithCount(schemaSwithCount.get())
            .contextSetupCount(contextSetupCount.get())
            .slowQueryCount(slowQueryCount.get())
            .build();
    }

    /**
     * 监控指标数据类
     */
    public static class MonitorMetrics {
        private final long schemaSwithCount;
        private final long contextSetupCount;
        private final long slowQueryCount;

        private MonitorMetrics(long schemaSwithCount, long contextSetupCount, long slowQueryCount) {
            this.schemaSwithCount = schemaSwithCount;
            this.contextSetupCount = contextSetupCount;
            this.slowQueryCount = slowQueryCount;
        }

        public static Builder builder() {
            return new Builder();
        }

        public long getSchemaSwithCount() { return schemaSwithCount; }
        public long getContextSetupCount() { return contextSetupCount; }
        public long getSlowQueryCount() { return slowQueryCount; }

        public static class Builder {
            private long schemaSwithCount;
            private long contextSetupCount;
            private long slowQueryCount;

            public Builder schemaSwithCount(long schemaSwithCount) {
                this.schemaSwithCount = schemaSwithCount;
                return this;
            }

            public Builder contextSetupCount(long contextSetupCount) {
                this.contextSetupCount = contextSetupCount;
                return this;
            }

            public Builder slowQueryCount(long slowQueryCount) {
                this.slowQueryCount = slowQueryCount;
                return this;
            }

            public MonitorMetrics build() {
                return new MonitorMetrics(schemaSwithCount, contextSetupCount, slowQueryCount);
            }
        }
    }
}
