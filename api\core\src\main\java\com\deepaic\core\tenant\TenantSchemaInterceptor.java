package com.deepaic.core.tenant;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * 多租户Schema拦截器 - 优化版
 * 基于Sa-Token登录状态自动切换租户schema
 *
 * 工作原理：
 * 1. 用户登录后，Sa-Token存储用户ID
 * 2. 拦截器根据用户ID自动查询并设置租户schema
 * 3. 公共表(pub_*)强制使用public schema
 * 4. 业务表自动使用租户schema
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantSchemaInterceptor implements InnerInterceptor {

    private final TenantContextService tenantContextService;
    private final TenantMonitor tenantMonitor;

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter,
                           RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql)
                           throws SQLException {
        setTenantSchema(executor, ms);
    }

    @Override
    public void beforeUpdate(Executor executor, MappedStatement ms, Object parameter)
                            throws SQLException {
        setTenantSchema(executor, ms);
    }

    /**
     * 设置租户schema到数据库连接
     * 优化版：自动根据Sa-Token登录状态获取租户信息
     */
    private void setTenantSchema(Executor executor, MappedStatement ms) throws SQLException {
        String currentSchema = getCurrentSchema(executor.getTransaction().getConnection());
        String targetSchema = determineTargetSchema(ms);

        // 如果目标schema与当前schema相同，无需切换
        if (targetSchema.equals(currentSchema)) {
            return;
        }

        Connection connection = executor.getTransaction().getConnection();
        if (connection == null || connection.isClosed()) {
            log.warn("数据库连接无效，无法设置租户schema");
            return;
        }

        long startTime = System.currentTimeMillis();
        try {
            // 优先使用JDBC标准的setSchema方法
            setSchemaWithFallback(connection, targetSchema, ms.getId());

            // 记录监控信息
            long duration = System.currentTimeMillis() - startTime;
            tenantMonitor.recordSchemaSwitch(currentSchema, targetSchema, duration);

        } catch (SQLException e) {
            log.error("设置租户schema失败: schema={}, mappedStatement={}", targetSchema, ms.getId(), e);
            throw e;
        }
    }

    /**
     * 确定目标schema
     * 1. 公共表强制使用public schema
     * 2. 业务表根据Sa-Token Session中的租户信息获取schema
     */
    private String determineTargetSchema(MappedStatement ms) {
        // 1. 如果是公共表操作，强制使用public schema
        if (isPublicTableQuery(ms)) {
            log.debug("公共表查询，使用public schema: {}", ms.getId());
            return SaTokenTenantContext.DEFAULT_SCHEMA;
        }

        // 2. 从Sa-Token Session获取租户schema
        String sessionSchema = SaTokenTenantContext.getTenantSchema();
        if (sessionSchema != null && !sessionSchema.equals(SaTokenTenantContext.DEFAULT_SCHEMA)) {
            log.debug("使用Session租户schema: {} for {}", sessionSchema, ms.getId());
            return sessionSchema;
        }

        // 3. 如果Session中没有，尝试自动获取并设置
        if (StpUtil.isLogin()) {
            try {
                tenantContextService.setTenantContextForUser(StpUtil.getLoginIdAsLong());
                String autoSchema = SaTokenTenantContext.getTenantSchema();
                if (autoSchema != null && !autoSchema.equals(SaTokenTenantContext.DEFAULT_SCHEMA)) {
                    log.debug("自动获取租户schema: {} for {}", autoSchema, ms.getId());
                    return autoSchema;
                }
            } catch (Exception e) {
                log.warn("自动设置租户上下文失败: {}", e.getMessage());
            }
        }

        // 4. 默认使用public schema
        log.debug("使用默认schema: {} for {}", SaTokenTenantContext.DEFAULT_SCHEMA, ms.getId());
        return SaTokenTenantContext.DEFAULT_SCHEMA;
    }



    /**
     * 判断是否为公共表查询
     * 公共表包括：pub_account, pub_tenant等
     */
    private boolean isPublicTableQuery(MappedStatement ms) {
        String statementId = ms.getId().toLowerCase();

        // Account相关的mapper（公共表）
        if (statementId.contains("accountmapper")) {
            return true;
        }

        // Tenant相关的mapper（公共表）
        if (statementId.contains("tenantmapper")) {
            return true;
        }

        // 其他公共表的mapper
        if (statementId.contains("publicmapper") ||
            statementId.contains("pub_") ||
            statementId.contains("system")) {
            return true;
        }

        // 认证相关的查询
        if (statementId.contains("auth") ||
            statementId.contains("login") ||
            statementId.contains("token")) {
            return true;
        }

        return false;
    }

    /**
     * 设置数据库Schema - 优雅的实现方式
     * 优先使用JDBC标准方法，必要时使用PostgreSQL特定的search_path
     *
     * @param connection 数据库连接
     * @param targetSchema 目标schema
     * @param statementId 语句ID（用于日志）
     * @throws SQLException SQL异常
     */
    private void setSchemaWithFallback(Connection connection, String targetSchema, String statementId) throws SQLException {
        try {
            // 方式1：使用JDBC标准的setSchema方法（推荐）
            connection.setSchema(targetSchema);
            log.debug("使用JDBC标准方法设置schema: {} for {}", targetSchema, statementId);

        } catch (SQLException e) {
            // 方式2：如果标准方法失败，使用PostgreSQL的search_path（兼容性fallback）
            log.debug("JDBC标准方法失败，尝试使用search_path: {}", e.getMessage());
            setSchemaUsingSearchPath(connection, targetSchema, statementId);
        }
    }

    /**
     * 使用PostgreSQL的search_path设置schema
     * 作为JDBC标准方法的fallback
     *
     * @param connection 数据库连接
     * @param targetSchema 目标schema
     * @param statementId 语句ID（用于日志）
     * @throws SQLException SQL异常
     */
    private void setSchemaUsingSearchPath(Connection connection, String targetSchema, String statementId) throws SQLException {
        // 构建search_path，包含目标schema和public作为fallback
        String searchPath = targetSchema.equals(SaTokenTenantContext.DEFAULT_SCHEMA)
            ? "public"
            : targetSchema + ", public";

        try (var statement = connection.createStatement()) {
            String sql = "SET search_path TO " + searchPath;
            statement.execute(sql);
            log.debug("使用search_path设置schema: {} for {}", searchPath, statementId);
        }
    }

    /**
     * 获取当前连接的schema
     */
    private String getCurrentSchema(Connection connection) {
        try {
            return connection.getSchema();
        } catch (SQLException e) {
            log.debug("获取当前schema失败，使用默认值", e);
            return SaTokenTenantContext.DEFAULT_SCHEMA;
        }
    }

}
