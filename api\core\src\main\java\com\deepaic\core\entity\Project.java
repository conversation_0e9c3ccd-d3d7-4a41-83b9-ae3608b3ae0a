package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_project")
public class Project extends BaseEntity {

    private String name;

    private String number;

    /**
     * 项目分类
     */
    private Long dictCategoryId;

    /**
     * 服务时长，分钟
     */
    private Short serviceDuration;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    /**
     * 提成分值
     */
    private BigDecimal commissionScore;

    /**
     * 说明
     */
    private String remark;

    /**
     * 1 上架 | 0 下架
     */
    private Short status;

    // 状态常量
    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_NORMAL = 1;   // 正常
}
