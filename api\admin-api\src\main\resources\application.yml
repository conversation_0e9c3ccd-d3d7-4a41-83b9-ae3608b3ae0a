# Beautiful Posture Admin API Configuration
spring:
  application:
    name: admin-api
  

  
  # Database Configuration - PostgreSQL
  datasource:
    url: *************************************************************
    username: postgres
    password: deepaic!2025
    driver-class-name: org.postgresql.Driver
    
    # Connection Pool Configuration - HikariCP
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  
  # Redis Configuration
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      # password: ${REDIS_PASSWORD:}  # 本地开发环境不设置密码
      database: 1
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
    encoding:
      charset: UTF-8
      enabled: true
      force: true

server:
  port: 8080
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
    context-path: /admin-api

# MyBatis-Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: true
      logic-not-delete-value: false
  mapper-locations: classpath*:mapper/**/*.xml

# Logging Configuration
logging:
  level:
    root: INFO
    com.deepaic.core.mapper: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

system:
  salt: f0b55b94927446098a9290f46c35b496

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# Multi-tenant Configuration - Based on Sa-Token Session
multitenant:
  enabled: true
  default-schema: public
  auto-context-setup: true 