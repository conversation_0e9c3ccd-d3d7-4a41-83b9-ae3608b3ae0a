package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_recharge_package_product")
public class RechargePackageProduct extends BaseEntity {

    private Long rechargeId;

    private Long rechargePackageId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private BigDecimal productName;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Short quantity;

    /**
     * 总价
     */
    private BigDecimal totlePrice;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 折扣总价
     */
    private BigDecimal discountTotalPrice;
}
