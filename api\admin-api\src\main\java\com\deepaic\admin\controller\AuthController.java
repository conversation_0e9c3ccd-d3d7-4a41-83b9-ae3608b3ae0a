package com.deepaic.admin.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.deepaic.core.auth.AuthenticationManager;
import com.deepaic.core.auth.models.AuthResponse;
import com.deepaic.core.auth.models.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * 租户管理系统认证控制器
 * 处理租户用户的登录、登出等认证操作
 * 支持手机号登录和微信授权登录
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final AuthenticationManager authenticationManager;

   

    
    /**
     * 租户用户登出
     */
    @PostMapping("/logout")
    @SaCheckLogin
    public Map<String, Object> logout() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取当前用户信息
            String loginId = StpUtil.getLoginId().toString();
            
            // 执行登出
            StpUtil.logout();
            
            response.put("code", 200);
            response.put("message", "登出成功");
            response.put("data", null);
            
            log.info("租户用户登出成功: loginId={}", loginId);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "登出失败: " + e.getMessage());
            response.put("data", null);
            
            log.error("租户用户登出失败", e);
        }
        
        return response;
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/current-user")
    @SaCheckLogin
    public Map<String, Object> getCurrentUser() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从Session中获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) StpUtil.getSession().get("userPrincipal");
            
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", userPrincipal);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "获取用户信息失败: " + e.getMessage());
            response.put("data", null);
            
            log.error("获取当前用户信息失败", e);
        }
        
        return response;
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/check-login")
    public Map<String, Object> checkLogin() {
        Map<String, Object> response = new HashMap<>();

        boolean isLogin = StpUtil.isLogin();
        response.put("code", 200);
        response.put("message", "检查完成");

        Map<String, Object> data = new HashMap<>();
        data.put("isLogin", isLogin);
        if (isLogin) {
            data.put("loginId", StpUtil.getLoginId());
            data.put("tokenValue", StpUtil.getTokenValue());
        }
        response.put("data", data);

        return response;
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh-token")
    public Map<String, Object> refreshToken(@RequestBody RefreshTokenRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 租户用户类型为CUSTOMER_ACCOUNT
            AuthResponse authResponse = authenticationManager.refreshToken(
                request.getRefreshToken(),
                UserPrincipal.UserType.SASS_CLIENT_ACCOUNT
            );

            if (authResponse.isSuccess()) {
                response.put("code", 200);
                response.put("message", "刷新成功");

                Map<String, Object> data = new HashMap<>();
                data.put("accessToken", authResponse.getAccessToken());
                data.put("expiresIn", authResponse.getExpiresIn());
                response.put("data", data);
            } else {
                response.put("code", authResponse.getErrorCode());
                response.put("message", authResponse.getMessage());
                response.put("data", null);
            }
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "刷新令牌失败: " + e.getMessage());
            response.put("data", null);

            log.error("刷新令牌失败", e);
        }

        return response;
    }



    /**
     * 手机号登录请求DTO
     */
    public static class PhoneLoginRequest {
        @NotBlank(message = "手机号不能为空")
        private String phone;
        
        @NotBlank(message = "短信验证码不能为空")
        private String smsCode;
        
        @NotBlank(message = "租户代码不能为空")
        private String tenantCode;

        // Getters and Setters
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getSmsCode() { return smsCode; }
        public void setSmsCode(String smsCode) { this.smsCode = smsCode; }
        public String getTenantCode() { return tenantCode; }
        public void setTenantCode(String tenantCode) { this.tenantCode = tenantCode; }
    }

    /**
     * 微信登录请求DTO
     */
    public static class WechatLoginRequest {
        @NotBlank(message = "微信授权码不能为空")
        private String code;
        
        @NotBlank(message = "租户代码不能为空")
        private String tenantCode;
        
        private Integer platform = 1; // 默认为微信公众号

        // Getters and Setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getTenantCode() { return tenantCode; }
        public void setTenantCode(String tenantCode) { this.tenantCode = tenantCode; }
        public Integer getPlatform() { return platform; }
        public void setPlatform(Integer platform) { this.platform = platform; }
    }

    /**
     * 刷新令牌请求DTO
     */
    public static class RefreshTokenRequest {
        @NotBlank(message = "刷新令牌不能为空")
        private String refreshToken;

        // Getters and Setters
        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }
    }
}
