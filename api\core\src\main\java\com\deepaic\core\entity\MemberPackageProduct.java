package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 会员权益-套餐-产品
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_package_product")
public class MemberPackageProduct extends BaseEntity {

    private String sysMemberPackageId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private BigDecimal productName;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Short quantity;

    /**
     * 总价
     */
    private BigDecimal totlePrice;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 折扣总价
     */
    private BigDecimal discountTotalPrice;

    /**
     * 1=未用完，2=已用完
     */
    private Short status;
}
