package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_recharge")
public class Recharge extends BaseEntity {

    private Long memberId;

    private String memberName;

    private String memberCode;

    private String memberMobile;

    /**
     * 客户签名
     */
    private String signatureFile;

    private Long storeId;

    /**
     * 1=储值卡，2=项目，3=套餐卡,4=定金
     */
    private Short type;

    /**
     * 子类
     */
    private String subType;

    private String remark;

    /**
     * 1=已支付,2=未支付,3=已退还
     */
    private Short status;

    /**
     * 编号
     */
    private String code;

    /**
     * 销售员
     */
    private Long saleUserId;

    /**
     * 交易金额
     */
    private BigDecimal balance;

    /**
     * 1=收银系统，2=小程序
     */
    private Short platform;

    // 状态常量
    public static final short STATUS_PENDING = 0;  // 待处理
    public static final short STATUS_SUCCESS = 1;  // 成功
    public static final short STATUS_FAILED = 2;   // 失败
}
