package com.deepaic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.deepaic.core.entity.Menu;
import com.deepaic.core.dto.MenuDTO;

import java.util.List;

/**
 * 菜单服务接口
 *
 * <AUTHOR>
 */
public interface IMenuService extends IService<Menu> {

    /**
     * 查询菜单列表（带分页）
     */
    IPage<MenuDTO> getMenuPage(Page<MenuDTO> page, MenuDTO.MenuQueryDTO query);

    /**
     * 查询所有菜单列表
     */
    List<MenuDTO> getMenuList(MenuDTO.MenuQueryDTO query);

    /**
     * 查询菜单树
     */
    List<MenuDTO> getMenuTree(MenuDTO.MenuQueryDTO query);

    /**
     * 根据用户ID查询菜单树
     */
    List<MenuDTO> getMenuTreeByUserId(Long userId);

    /**
     * 根据用户ID查询菜单权限
     */
    List<String> getMenuPermissionsByUserId(Long userId);

    /**
     * 根据角色ID查询菜单列表
     */
    List<MenuDTO> getMenuListByRoleId(Long roleId);

    /**
     * 创建菜单
     */
    Long createMenu(MenuDTO menuDTO);

    /**
     * 更新菜单
     */
    boolean updateMenu(Long id, MenuDTO menuDTO);

    /**
     * 删除菜单
     */
    boolean deleteMenu(Long id);

    /**
     * 批量删除菜单
     */
    boolean deleteMenus(List<Long> ids);

    /**
     * 获取菜单详情
     */
    MenuDTO getMenuById(Long id);

    /**
     * 检查菜单名称是否唯一
     */
    boolean checkMenuNameUnique(String menuName, Long parentId, Long id);

    /**
     * 构建菜单树
     */
    List<MenuDTO> buildMenuTree(List<MenuDTO> menuList);

    /**
     * 获取菜单选择树（用于前端选择父菜单）
     */
    List<MenuDTO> getMenuSelectTree();
}
