package com.deepaic.core.dto;

import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 账户数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class AccountDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 账户名
     */
    @NotBlank(message = "账户名不能为空")
    @Size(min = 3, max = 50, message = "账户名长度必须在3-50个字符之间")
    private String username;

    /**
     * 密码 - 创建时必填，更新时可选
     */
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 手机号
     */
    @Size(max = 20, message = "手机号长度不能超过20个字符")
    private String phone;

    /**
     * 真实姓名
     */
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    /**
     * 头像URL
     */
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    private String avatar;

    /**
     * 所属租户代码
     */
    @NotBlank(message = "租户代码不能为空")
    @Size(max = 50, message = "租户代码长度不能超过50个字符")
    private String tenantCode;

    /**
     * 租户名称 - 只读字段，用于显示
     */
    private String tenantName;

    /**
     * 账户状态 - 0:禁用 1:启用 2:锁定
     */
    private Integer status;

    /**
     * 账户类型 - 1:超级管理员 2:租户管理员 3:普通用户
     */
    private Integer accountType;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 登录失败次数
     */
    private Integer loginFailCount;

    /**
     * 账户锁定时间
     */
    private LocalDateTime lockTime;

    /**
     * 密码过期时间
     */
    private LocalDateTime passwordExpireTime;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    private Integer version;
}

/**
 * 账户创建DTO
 */
@Data
class AccountCreateDTO {

    @NotBlank(message = "账户名不能为空")
    @Size(min = 3, max = 50, message = "账户名长度必须在3-50个字符之间")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

    @Email(message = "邮箱格式不正确")
    private String email;

    @Size(max = 20, message = "手机号长度不能超过20个字符")
    private String phone;

    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    @NotBlank(message = "租户代码不能为空")
    @Size(max = 50, message = "租户代码长度不能超过50个字符")
    private String tenantCode;

    private Integer accountType;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}

/**
 * 账户更新DTO
 */
@Data
class AccountUpdateDTO {

    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    private String password;

    @Email(message = "邮箱格式不正确")
    private String email;

    @Size(max = 20, message = "手机号长度不能超过20个字符")
    private String phone;

    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    private String avatar;

    private Integer status;

    private Integer accountType;

    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    private Integer version;
}




