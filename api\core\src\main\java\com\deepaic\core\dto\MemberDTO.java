package com.deepaic.core.dto;

import lombok.Data;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 会员DTO
 *
 * <AUTHOR>
 */
@Data
public class MemberDTO {

    /**
     * 会员ID
     */
    private Long id;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 微信OpenID
     */
    private String wxOpenid;

    /**
     * 微信UnionID
     */
    private String wxUnionid;

    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 会员昵称
     */
    @Size(max = 50, message = "会员昵称长度不能超过50个字符")
    private String nickname;

    /**
     * 真实姓名
     */
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 会员等级ID
     */
    private Long levelId;

    /**
     * 会员等级名称
     */
    private String levelName;

    /**
     * 积分余额
     */
    private Integer points;

    /**
     * 余额（分）
     */
    private Long balance;

    /**
     * 会员状态
     */
    private Integer status;

    /**
     * 注册来源
     */
    private Integer registerSource;

    /**
     * 推荐人会员ID
     */
    private Long referrerId;

    /**
     * 推荐人会员编号
     */
    private String referrerNo;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 会员查询DTO
     */
    @Data
    public static class MemberQueryDTO {
        /**
         * 会员编号
         */
        private String memberNo;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 会员昵称
         */
        private String nickname;

        /**
         * 真实姓名
         */
        private String realName;

        /**
         * 会员状态
         */
        private Integer status;

        /**
         * 会员等级ID
         */
        private Long levelId;

        /**
         * 注册来源
         */
        private Integer registerSource;

        /**
         * 注册开始时间
         */
        private LocalDateTime registerStartTime;

        /**
         * 注册结束时间
         */
        private LocalDateTime registerEndTime;
    }

    /**
     * 会员登录DTO
     */
    @Data
    public static class MemberLoginDTO {
        /**
         * 微信授权码
         */
        private String code;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 短信验证码
         */
        private String smsCode;

        /**
         * 微信用户信息
         */
        private WechatUserInfo userInfo;

        /**
         * 登录平台
         */
        private Integer platform;

        /**
         * 推荐人会员编号
         */
        private String referrerNo;
    }

    /**
     * 微信用户信息
     */
    @Data
    public static class WechatUserInfo {
        /**
         * 昵称
         */
        private String nickName;

        /**
         * 头像
         */
        private String avatarUrl;

        /**
         * 性别
         */
        private Integer gender;

        /**
         * 国家
         */
        private String country;

        /**
         * 省份
         */
        private String province;

        /**
         * 城市
         */
        private String city;
    }

    /**
     * 会员登录响应DTO
     */
    @Data
    public static class MemberLoginResponse {
        /**
         * 访问令牌
         */
        private String accessToken;

        /**
         * 刷新令牌
         */
        private String refreshToken;

        /**
         * 令牌过期时间（秒）
         */
        private Long expiresIn;

        /**
         * 会员信息
         */
        private MemberDTO memberInfo;

        /**
         * 是否新用户
         */
        private Boolean isNewUser;
    }
}
