package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 储值卡权益使用
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_consume_paid_card")
public class ConsumePaidCard extends BaseEntity {

    private Long consumeId;

    private Long memberPaidCardId;

    private BigDecimal paidAmount;
}
