package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_consume_payment")
public class ConsumePayment extends BaseEntity {

    private Long consumeId;

    /**
     * 储值卡支付
     */
    private BigDecimal paidCard;

    /**
     * 积分抵扣
     */
    private BigDecimal point;

    /**
     * 阿里
     */
    private BigDecimal alipay;

    /**
     * 微信
     */
    private BigDecimal wechat;

    /**
     * 现金
     */
    private BigDecimal cash;

    /**
     * 信用卡
     */
    private BigDecimal creditCard;

    /**
     * 其它支付
     */
    private BigDecimal other;

    /**
     * 总额
     */
    private Long totalBalance;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 实际应付
     */
    private BigDecimal actualBalance;

    /**
     * 定金支付
     */
    private BigDecimal deposit;
}
