import java.sql.*;
import java.util.*;

/**
 * 数据库表结构检查工具
 */
public class DatabaseChecker {
    
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres";
    private static final String DB_PASSWORD = "deepaic!2025";
    
    public static void main(String[] args) {
        try {
            // 加载PostgreSQL驱动
            Class.forName("org.postgresql.Driver");
            
            try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
                System.out.println("成功连接到数据库: " + DB_URL);
                
                // 检查public schema的表
                checkSchemaTable(conn, "public");
                
                // 检查其他schema的表
                List<String> schemas = getSchemas(conn);
                for (String schema : schemas) {
                    if (!"public".equals(schema) && 
                        !"information_schema".equals(schema) && 
                        !schema.startsWith("pg_")) {
                        checkSchemaTable(conn, schema);
                    }
                }
                
            }
        } catch (Exception e) {
            System.err.println("数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static List<String> getSchemas(Connection conn) throws SQLException {
        List<String> schemas = new ArrayList<>();
        String sql = "SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT LIKE 'pg_%'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                schemas.add(rs.getString("schema_name"));
            }
        }
        
        return schemas;
    }
    
    private static void checkSchemaTable(Connection conn, String schemaName) throws SQLException {
        System.out.println("\n=== 检查 " + schemaName + " schema 中的表 ===");
        
        String sql = "SELECT table_name FROM information_schema.tables WHERE table_schema = ? ORDER BY table_name";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, schemaName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String tableName = rs.getString("table_name");
                    System.out.println("发现表: " + schemaName + "." + tableName);
                    
                    // 获取表的列信息
                    getTableColumns(conn, schemaName, tableName);
                }
            }
        }
    }
    
    private static void getTableColumns(Connection conn, String schemaName, String tableName) throws SQLException {
        String sql = """
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length
            FROM information_schema.columns 
            WHERE table_schema = ? AND table_name = ? 
            ORDER BY ordinal_position
        """;
        
        System.out.println("  表 " + schemaName + "." + tableName + " 的列信息:");
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, schemaName);
            stmt.setString(2, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    String nullable = rs.getString("is_nullable");
                    String defaultValue = rs.getString("column_default");
                    Integer maxLength = rs.getObject("character_maximum_length", Integer.class);
                    
                    System.out.printf("    %-20s %-15s %-8s %s%n", 
                        columnName, 
                        dataType + (maxLength != null ? "(" + maxLength + ")" : ""),
                        "YES".equals(nullable) ? "NULL" : "NOT NULL",
                        defaultValue != null ? "DEFAULT " + defaultValue : ""
                    );
                }
            }
        }
        
        // 生成实体类建议
        generateEntitySuggestion(schemaName, tableName);
    }
    
    private static void generateEntitySuggestion(String schemaName, String tableName) {
        String className = toCamelCase(tableName.replaceFirst("^(pub_|sys_)", ""));
        
        System.out.println("  建议的实体类名: " + className);
        System.out.println("  对应的@TableName: " + tableName);
        
        // 检查是否有对应的实体类文件
        String entityPath = String.format("core/src/main/java/com/deepaic/core/entity/%s.java", className);
        System.out.println("  实体类文件路径: " + entityPath);
    }
    
    private static String toCamelCase(String str) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;
        
        for (char c : str.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }
        
        return result.toString();
    }
}
