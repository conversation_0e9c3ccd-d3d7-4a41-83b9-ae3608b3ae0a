# 数据库字段约束优化

## 概述

对数据库表结构进行字段约束优化，将必要的字段设置为 NOT NULL，非必要的字段保持可选，提高数据质量和系统稳定性。

## 约束原则

### 1. 必要字段 (NOT NULL)
- **主键字段**：所有主键字段必须 NOT NULL
- **业务必需字段**：业务逻辑中必须存在的字段
- **状态字段**：系统状态控制字段
- **时间戳字段**：创建时间、更新时间等审计字段
- **标识字段**：具有默认值的标识性字段

### 2. 可选字段 (允许 NULL)
- **用户输入字段**：用户可能不填写的信息
- **扩展信息字段**：非核心业务字段
- **关联字段**：可能为空的逻辑关联字段
- **备注字段**：描述性信息字段

## 字段约束详情

### 公共表约束

#### pub_tenant (租户信息表)
```sql
-- 必要字段 (NOT NULL)
id BIGINT PRIMARY KEY                    -- 主键
tenant_code VARCHAR(50) NOT NULL         -- 租户编码
tenant_name VARCHAR(100) NOT NULL        -- 租户名称
schema_name VARCHAR(100) NOT NULL        -- 数据库模式名
tenant_type INTEGER NOT NULL DEFAULT 1  -- 租户类型
status INTEGER NOT NULL DEFAULT 1       -- 状态
max_users INTEGER NOT NULL DEFAULT 10   -- 最大用户数
max_storage BIGINT NOT NULL DEFAULT 1GB -- 最大存储
created_at TIMESTAMP NOT NULL           -- 创建时间
updated_at TIMESTAMP NOT NULL           -- 更新时间
deleted BOOLEAN NOT NULL DEFAULT FALSE  -- 删除标记

-- 可选字段 (允许 NULL)
contact_name VARCHAR(50)                 -- 联系人姓名
contact_phone VARCHAR(20)               -- 联系电话
contact_email VARCHAR(100)              -- 联系邮箱
address TEXT                            -- 地址
expire_time TIMESTAMP                   -- 过期时间
features JSONB                          -- 功能特性
settings JSONB                          -- 租户设置
remark TEXT                             -- 备注
created_by BIGINT                       -- 创建人
updated_by BIGINT                       -- 更新人
```

#### pub_platform_account (平台用户表)
```sql
-- 必要字段 (NOT NULL)
id BIGINT PRIMARY KEY                    -- 主键
username VARCHAR(50) NOT NULL            -- 用户名
password VARCHAR(100) NOT NULL           -- 密码
permission_level INTEGER NOT NULL       -- 权限级别
status INTEGER NOT NULL DEFAULT 1       -- 状态
login_fail_count INTEGER NOT NULL       -- 登录失败次数
created_at TIMESTAMP NOT NULL           -- 创建时间
updated_at TIMESTAMP NOT NULL           -- 更新时间
deleted BOOLEAN NOT NULL DEFAULT FALSE  -- 删除标记

-- 可选字段 (允许 NULL)
email VARCHAR(100)                       -- 邮箱
phone VARCHAR(20)                        -- 电话
real_name VARCHAR(50)                    -- 真实姓名
avatar VARCHAR(500)                      -- 头像
last_login_time TIMESTAMP               -- 最后登录时间
last_login_ip INET                      -- 最后登录IP
lock_time TIMESTAMP                     -- 锁定时间
password_expire_time TIMESTAMP          -- 密码过期时间
remark TEXT                             -- 备注
created_by BIGINT                       -- 创建人
updated_by BIGINT                       -- 更新人
```

#### pub_customer_account (租户用户表)
```sql
-- 必要字段 (NOT NULL)
id BIGINT PRIMARY KEY                    -- 主键
tenant_code VARCHAR(50) NOT NULL         -- 租户编码
login_type INTEGER NOT NULL DEFAULT 1   -- 登录类型
login_identifier VARCHAR(200) NOT NULL  -- 登录标识
account_type INTEGER NOT NULL DEFAULT 1 -- 账户类型
status INTEGER NOT NULL DEFAULT 1       -- 状态
login_fail_count INTEGER NOT NULL       -- 登录失败次数
bind_time TIMESTAMP NOT NULL            -- 绑定时间
is_primary BOOLEAN NOT NULL DEFAULT TRUE -- 是否主账户
created_at TIMESTAMP NOT NULL           -- 创建时间
updated_at TIMESTAMP NOT NULL           -- 更新时间
deleted BOOLEAN NOT NULL DEFAULT FALSE  -- 删除标记

-- 可选字段 (允许 NULL)
username VARCHAR(50)                     -- 用户名（微信登录时可为空）
password VARCHAR(100)                    -- 密码（微信登录时可为空）
email VARCHAR(100)                       -- 邮箱
phone VARCHAR(20)                        -- 电话
real_name VARCHAR(50)                    -- 真实姓名
avatar VARCHAR(500)                      -- 头像
wechat_openid VARCHAR(100)               -- 微信openid
wechat_unionid VARCHAR(100)              -- 微信unionid
last_login_time TIMESTAMP               -- 最后登录时间
last_login_ip INET                      -- 最后登录IP
lock_time TIMESTAMP                     -- 锁定时间
password_expire_time TIMESTAMP          -- 密码过期时间
sys_user_id BIGINT                      -- 关联用户ID
remark TEXT                             -- 备注
created_by BIGINT                       -- 创建人
updated_by BIGINT                       -- 更新人
```

### 租户表约束

#### sys_user (用户表)
```sql
-- 必要字段 (NOT NULL)
id BIGINT PRIMARY KEY                    -- 主键
user_code VARCHAR(50) NOT NULL           -- 用户编码
username VARCHAR(50) NOT NULL            -- 用户名
password VARCHAR(100) NOT NULL           -- 密码
status INTEGER NOT NULL DEFAULT 1       -- 状态
created_at TIMESTAMP NOT NULL           -- 创建时间
updated_at TIMESTAMP NOT NULL           -- 更新时间
deleted BOOLEAN NOT NULL DEFAULT FALSE  -- 删除标记

-- 可选字段 (允许 NULL)
email VARCHAR(100)                       -- 邮箱
phone VARCHAR(20)                        -- 电话
real_name VARCHAR(50)                    -- 真实姓名
avatar VARCHAR(500)                      -- 头像
gender INTEGER                          -- 性别
birthday DATE                           -- 生日
employee_no VARCHAR(50)                 -- 员工编号
entry_date DATE                         -- 入职日期
account_id BIGINT                       -- 关联账户ID
last_login_time TIMESTAMP               -- 最后登录时间
last_login_ip INET                      -- 最后登录IP
remark TEXT                             -- 备注
created_by BIGINT                       -- 创建人
updated_by BIGINT                       -- 更新人
```

#### sys_role (角色表)
```sql
-- 必要字段 (NOT NULL)
id BIGINT PRIMARY KEY                    -- 主键
role_name VARCHAR(50) NOT NULL           -- 角色名称
role_code VARCHAR(50) NOT NULL           -- 角色编码
role_sort INTEGER NOT NULL DEFAULT 0    -- 排序
data_scope INTEGER NOT NULL DEFAULT 1   -- 数据范围
status INTEGER NOT NULL DEFAULT 1       -- 状态
created_at TIMESTAMP NOT NULL           -- 创建时间
updated_at TIMESTAMP NOT NULL           -- 更新时间
deleted BOOLEAN NOT NULL DEFAULT FALSE  -- 删除标记

-- 可选字段 (允许 NULL)
remark TEXT                             -- 备注
created_by BIGINT                       -- 创建人
updated_by BIGINT                       -- 更新人
```

#### sys_menu (菜单表)
```sql
-- 必要字段 (NOT NULL)
id BIGINT PRIMARY KEY                    -- 主键
menu_name VARCHAR(50) NOT NULL           -- 菜单名称
parent_id BIGINT NOT NULL DEFAULT 0     -- 父菜单ID
order_num INTEGER NOT NULL DEFAULT 0    -- 排序
is_frame BOOLEAN NOT NULL DEFAULT FALSE -- 是否外链
is_cache BOOLEAN NOT NULL DEFAULT FALSE -- 是否缓存
menu_type CHAR(1) NOT NULL DEFAULT 'M'  -- 菜单类型
visible BOOLEAN NOT NULL DEFAULT TRUE   -- 是否可见
status INTEGER NOT NULL DEFAULT 1       -- 状态
created_at TIMESTAMP NOT NULL           -- 创建时间
updated_at TIMESTAMP NOT NULL           -- 更新时间
deleted BOOLEAN NOT NULL DEFAULT FALSE  -- 删除标记

-- 可选字段 (允许 NULL)
path VARCHAR(200)                        -- 路由路径
component VARCHAR(255)                   -- 组件路径
query_param VARCHAR(255)                 -- 查询参数
perms VARCHAR(100)                       -- 权限标识
icon VARCHAR(100)                        -- 图标
remark TEXT                             -- 备注
created_by BIGINT                       -- 创建人
updated_by BIGINT                       -- 更新人
```

## 约束优化的好处

### 1. 数据质量保障
- 防止关键字段为空导致的业务异常
- 确保系统状态字段的一致性
- 保证审计字段的完整性

### 2. 性能优化
- 减少NULL值检查的开销
- 优化查询执行计划
- 提高索引效率

### 3. 开发便利性
- 减少代码中的空值检查
- 明确字段的必要性
- 提高代码可读性

### 4. 系统稳定性
- 避免因空值导致的系统错误
- 提高数据一致性
- 减少异常处理复杂度

## 最佳实践

### 1. 设计原则
- 业务必需的字段设置为 NOT NULL
- 用户可选的字段允许 NULL
- 系统字段通常设置为 NOT NULL
- 关联字段根据业务需求决定

### 2. 默认值策略
- 状态字段提供合理的默认值
- 数值字段提供0或其他业务默认值
- 布尔字段明确默认状态
- 时间字段使用系统当前时间

### 3. 迁移注意事项
- 现有数据需要检查空值情况
- 添加NOT NULL约束前先处理空值
- 提供数据清理脚本
- 测试约束变更的影响

## 总结

通过合理设置字段约束，可以显著提高数据质量和系统稳定性。在设计时应该根据业务需求和字段特性，合理选择是否设置NOT NULL约束，既要保证数据完整性，又要保持系统的灵活性。
