-- 权限管理相关表初始化脚本
-- 这些表需要在每个租户的schema中创建

-- 菜单表
CREATE TABLE IF NOT EXISTS sys_menu (
    id BIGSERIAL PRIMARY KEY,
    menu_name VARCHAR(50) NOT NULL COMMENT '菜单名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父菜单ID',
    menu_type INTEGER DEFAULT 1 COMMENT '菜单类型(1:目录 2:菜单 3:按钮)',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    permission VARCHAR(100) COMMENT '权限标识',
    icon VARCHAR(100) COMMENT '菜单图标',
    sort_order INTEGER DEFAULT 0 COMMENT '排序号',
    visible INTEGER DEFAULT 1 COMMENT '是否可见(0:隐藏 1:显示)',
    status INTEGER DEFAULT 1 COMMENT '菜单状态(0:禁用 1:启用)',
    is_frame INTEGER DEFAULT 0 COMMENT '是否外链(0:否 1:是)',
    is_cache INTEGER DEFAULT 0 COMMENT '是否缓存(0:不缓存 1:缓存)',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建菜单表索引
CREATE INDEX IF NOT EXISTS idx_sys_menu_parent_id ON sys_menu(parent_id);
CREATE INDEX IF NOT EXISTS idx_sys_menu_status ON sys_menu(status);
CREATE INDEX IF NOT EXISTS idx_sys_menu_deleted ON sys_menu(deleted);

-- 角色表
CREATE TABLE IF NOT EXISTS sys_role (
    id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL COMMENT '角色编码',
    description VARCHAR(200) COMMENT '角色描述',
    data_scope INTEGER DEFAULT 2 COMMENT '数据权限范围(1:全部数据 2:部门数据 3:部门及下级数据 4:仅本人数据 5:自定义数据)',
    sort_order INTEGER DEFAULT 0 COMMENT '排序号',
    status INTEGER DEFAULT 1 COMMENT '角色状态(0:禁用 1:启用)',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建角色表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_role_code ON sys_role(role_code) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_role_status ON sys_role(status);
CREATE INDEX IF NOT EXISTS idx_sys_role_deleted ON sys_role(deleted);

-- 部门表
CREATE TABLE IF NOT EXISTS sys_department (
    id BIGSERIAL PRIMARY KEY,
    dept_name VARCHAR(50) NOT NULL COMMENT '部门名称',
    dept_code VARCHAR(50) NOT NULL COMMENT '部门编码',
    parent_id BIGINT DEFAULT 0 COMMENT '父部门ID',
    ancestors VARCHAR(500) DEFAULT '0' COMMENT '祖级列表',
    leader_id BIGINT COMMENT '部门负责人ID',
    leader_name VARCHAR(50) COMMENT '部门负责人姓名',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    sort_order INTEGER DEFAULT 0 COMMENT '排序号',
    status INTEGER DEFAULT 1 COMMENT '部门状态(0:禁用 1:启用)',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建部门表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_department_code ON sys_department(dept_code) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_department_parent_id ON sys_department(parent_id);
CREATE INDEX IF NOT EXISTS idx_sys_department_status ON sys_department(status);
CREATE INDEX IF NOT EXISTS idx_sys_department_deleted ON sys_department(deleted);

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS sys_user_role (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建用户角色关联表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_user_role ON sys_user_role(user_id, role_id) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_user_role_user_id ON sys_user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_role_role_id ON sys_user_role(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_role_deleted ON sys_user_role(deleted);

-- 角色菜单关联表
CREATE TABLE IF NOT EXISTS sys_role_menu (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL COMMENT '角色ID',
    menu_id BIGINT NOT NULL COMMENT '菜单ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建角色菜单关联表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_role_menu ON sys_role_menu(role_id, menu_id) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_role_menu_role_id ON sys_role_menu(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_menu_menu_id ON sys_role_menu(menu_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_menu_deleted ON sys_role_menu(deleted);

-- 角色部门关联表
CREATE TABLE IF NOT EXISTS sys_role_department (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL COMMENT '角色ID',
    dept_id BIGINT NOT NULL COMMENT '部门ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建角色部门关联表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_role_department ON sys_role_department(role_id, dept_id) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_role_department_role_id ON sys_role_department(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_department_dept_id ON sys_role_department(dept_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_department_deleted ON sys_role_department(deleted);

-- 更新sys_user表，添加部门关联
ALTER TABLE sys_user ADD COLUMN IF NOT EXISTS dept_id BIGINT COMMENT '部门ID';
CREATE INDEX IF NOT EXISTS idx_sys_user_dept_id ON sys_user(dept_id);

-- 插入默认菜单数据
INSERT INTO sys_menu (menu_name, parent_id, menu_type, path, component, permission, icon, sort_order, visible, status) VALUES
('系统管理', 0, 1, '/system', NULL, NULL, 'system', 1, 1, 1),
('用户管理', 1, 2, '/system/user', 'system/user/index', 'system:user:list', 'user', 1, 1, 1),
('角色管理', 1, 2, '/system/role', 'system/role/index', 'system:role:list', 'peoples', 2, 1, 1),
('菜单管理', 1, 2, '/system/menu', 'system/menu/index', 'system:menu:list', 'tree-table', 3, 1, 1),
('部门管理', 1, 2, '/system/dept', 'system/dept/index', 'system:dept:list', 'tree', 4, 1, 1),
('租户管理', 1, 2, '/system/tenant', 'system/tenant/index', 'system:tenant:list', 'company', 5, 1, 1)
ON CONFLICT DO NOTHING;

-- 插入按钮权限
INSERT INTO sys_menu (menu_name, parent_id, menu_type, permission, sort_order, visible, status) VALUES
('用户查询', 2, 3, 'system:user:query', 1, 1, 1),
('用户新增', 2, 3, 'system:user:add', 2, 1, 1),
('用户修改', 2, 3, 'system:user:edit', 3, 1, 1),
('用户删除', 2, 3, 'system:user:remove', 4, 1, 1),
('角色查询', 3, 3, 'system:role:query', 1, 1, 1),
('角色新增', 3, 3, 'system:role:add', 2, 1, 1),
('角色修改', 3, 3, 'system:role:edit', 3, 1, 1),
('角色删除', 3, 3, 'system:role:remove', 4, 1, 1),
('菜单查询', 4, 3, 'system:menu:query', 1, 1, 1),
('菜单新增', 4, 3, 'system:menu:add', 2, 1, 1),
('菜单修改', 4, 3, 'system:menu:edit', 3, 1, 1),
('菜单删除', 4, 3, 'system:menu:remove', 4, 1, 1),
('部门查询', 5, 3, 'system:dept:query', 1, 1, 1),
('部门新增', 5, 3, 'system:dept:add', 2, 1, 1),
('部门修改', 5, 3, 'system:dept:edit', 3, 1, 1),
('部门删除', 5, 3, 'system:dept:remove', 4, 1, 1),
('租户查询', 6, 3, 'system:tenant:query', 1, 1, 1),
('租户新增', 6, 3, 'system:tenant:add', 2, 1, 1),
('租户修改', 6, 3, 'system:tenant:edit', 3, 1, 1),
('租户删除', 6, 3, 'system:tenant:remove', 4, 1, 1)
ON CONFLICT DO NOTHING;

-- 插入默认角色
INSERT INTO sys_role (role_name, role_code, description, data_scope, sort_order, status) VALUES
('超级管理员', 'SUPER_ADMIN', '超级管理员角色，拥有所有权限', 1, 1, 1),
('管理员', 'ADMIN', '管理员角色，拥有大部分权限', 2, 2, 1),
('普通用户', 'USER', '普通用户角色，拥有基本权限', 4, 3, 1)
ON CONFLICT DO NOTHING;

-- 插入默认部门
INSERT INTO sys_department (dept_name, dept_code, parent_id, ancestors, sort_order, status) VALUES
('美姿姿科技', 'MZZKJ', 0, '0', 1, 1),
('研发部', 'RD', 1, '0,1', 1, 1),
('市场部', 'MKT', 1, '0,1', 2, 1),
('人事部', 'HR', 1, '0,1', 3, 1)
ON CONFLICT DO NOTHING;

-- 为超级管理员角色分配所有菜单权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, id FROM sys_menu WHERE deleted = 0
ON CONFLICT DO NOTHING;
