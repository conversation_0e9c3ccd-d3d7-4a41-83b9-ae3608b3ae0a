package com.deepaic.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepaic.core.entity.MemberAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会员账户Mapper接口
 * 操作公共schema中的会员账户表
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberAccountMapper extends BaseMapper<MemberAccount> {

    /**
     * 根据登录标识查询账户信息
     */
    MemberAccount selectByLoginIdentifier(@Param("loginType") Integer loginType, @Param("loginIdentifier") String loginIdentifier);

    /**
     * 根据租户和会员ID查询账户信息
     */
    MemberAccount selectByTenantAndMemberId(@Param("tenantCode") String tenantCode, @Param("memberId") Long memberId);

    /**
     * 检查登录标识是否已存在
     */
    Integer checkLoginIdentifierExists(@Param("loginType") Integer loginType, @Param("loginIdentifier") String loginIdentifier, @Param("id") Long id);

    /**
     * 根据租户编码查询会员账户列表
     */
    java.util.List<MemberAccount> selectByTenantCode(@Param("tenantCode") String tenantCode);

    /**
     * 删除会员的所有账户信息
     */
    int deleteByTenantAndMemberId(@Param("tenantCode") String tenantCode, @Param("memberId") Long memberId);

    /**
     * 更新账户状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
}
