package com.deepaic.core.auth;

import com.deepaic.core.auth.interfaces.AuthenticationService;
import com.deepaic.core.auth.models.AuthRequest;
import com.deepaic.core.auth.models.AuthResponse;
import com.deepaic.core.auth.models.UserPrincipal;
import com.deepaic.core.exception.BussinessException;
import com.deepaic.core.util.AssertUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 统一认证管理器
 * 负责协调不同类型的认证服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationManager {

    private final List<AuthenticationService> authenticationServices;

    /**
     * 统一认证入口
     *
     * @param request 认证请求
     * @param httpRequest HTTP请求
     * @return 认证响应
     * @throws BussinessException 
     */
    public AuthResponse authenticate(AuthRequest request) throws BussinessException {
            // 查找支持该认证类型的服务
            AuthenticationService authService = findAuthenticationServiceByUserType(request.getUserType());
           
            if (authService == null) {
                log.warn("未找到支持的认证服务: authenticationType={}", request.getAuthenticationType());
                AssertUtil.notNull(authService, "不支持的认证类型");
            }

            // 执行认证
            log.info("开始认证: authenticationType={}, username={}", 
                    request.getAuthenticationType(), request.getUsername());
            
            AuthResponse response = authService.authenticate(request);
            
            log.info("认证成功: authenticationType={}, userId={}", 
            request.getAuthenticationType(), response.getUserPrincipal().getUserId());
            
            return response;
        
    }

    /**
     * 统一登出
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 是否成功
     */
    public boolean logout(String userId, UserPrincipal.UserType userType) {
        try {
            // 根据用户类型查找对应的认证服务
            AuthenticationService authService = findAuthenticationServiceByUserType(userType);
            if (authService == null) {
                log.warn("未找到对应的认证服务: userType={}", userType);
                return false;
            }

            boolean success = authService.logout(userId);
            if (success) {
                log.info("登出成功: userId={}, userType={}", userId, userType);
            } else {
                log.warn("登出失败: userId={}, userType={}", userId, userType);
            }
            
            return success;

        } catch (Exception e) {
            log.error("登出过程发生异常: userId={}, userType={}", userId, userType, e);
            return false;
        }
    }

    /**
     * 刷新令牌（自动推断用户类型）
     *
     * @param refreshToken 刷新令牌
     * @return 认证响应
     */
    public AuthResponse refreshToken(String refreshToken) {
        try {
            // 尝试从令牌中解析用户类型
            UserPrincipal userPrincipal = validateToken(refreshToken);
            if (userPrincipal != null) {
                return refreshToken(refreshToken, userPrincipal.getUserType());
            }

            // 如果无法解析，尝试所有认证服务
            for (AuthenticationService authService : authenticationServices) {
                try {
                    AuthResponse response = authService.refreshToken(refreshToken);
                    if (response.isSuccess()) {
                        return response;
                    }
                } catch (Exception e) {
                    log.debug("认证服务 {} 刷新令牌失败", authService.getClass().getSimpleName(), e);
                }
            }

            return AuthResponse.failure("刷新令牌失败", AuthResponse.ERROR_TOKEN_INVALID);

        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            return AuthResponse.failure("刷新令牌失败: " + e.getMessage(), AuthResponse.ERROR_TOKEN_INVALID);
        }
    }

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @param userType 用户类型
     * @return 认证响应
     */
    public AuthResponse refreshToken(String refreshToken, UserPrincipal.UserType userType) {
        try {
            AuthenticationService authService = findAuthenticationServiceByUserType(userType);
            if (authService == null) {
                return AuthResponse.failure("未找到对应的认证服务", AuthResponse.ERROR_TOKEN_INVALID);
            }

            return authService.refreshToken(refreshToken);

        } catch (Exception e) {
            log.error("刷新令牌失败: userType={}", userType, e);
            return AuthResponse.failure("刷新令牌失败: " + e.getMessage(), AuthResponse.ERROR_TOKEN_INVALID);
        }
    }

    /**
     * 验证令牌
     *
     * @param token 访问令牌
     * @return 用户主体信息
     */
    public UserPrincipal validateToken(String token) {
        try {
            // 尝试所有认证服务来验证令牌
            for (AuthenticationService authService : authenticationServices) {
                UserPrincipal userPrincipal = authService.validateToken(token);
                if (userPrincipal != null) {
                    return userPrincipal;
                }
            }
            return null;

        } catch (Exception e) {
            log.debug("令牌验证失败", e);
            return null;
        }
    }

    /**
     * 获取当前用户
     *
     * @return 用户主体信息
     */
    public UserPrincipal getCurrentUser() {
        try {
            // 尝试所有认证服务来获取当前用户
            for (AuthenticationService authService : authenticationServices) {
                if (authService.isAuthenticated()) {
                    return authService.getCurrentUser();
                }
            }
            return null;

        } catch (Exception e) {
            log.debug("获取当前用户失败", e);
            return null;
        }
    }

    /**
     * 检查是否已认证
     *
     * @return 是否已认证
     */
    public boolean isAuthenticated() {
        try {
            return authenticationServices.stream()
                    .anyMatch(AuthenticationService::isAuthenticated);
        } catch (Exception e) {
            log.debug("检查认证状态失败", e);
            return false;
        }
    }



    /**
     * 根据用户类型查找认证服务
     */
    private AuthenticationService findAuthenticationServiceByUserType(UserPrincipal.UserType userType) {
        return authenticationServices.stream()
                .filter(service -> service.getUserType().equals(userType))
                .findFirst()
                .orElse(null);
    }

}
