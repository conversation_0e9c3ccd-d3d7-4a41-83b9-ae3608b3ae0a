package com.deepaic.core.tenant;

import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 多租户工具类
 * 基于Sa-Token Session的简便多租户操作
 *
 * <AUTHOR>
 */
@Slf4j
public class TenantUtils {

    /**
     * 获取当前租户代码
     */
    public static String getCurrentTenantCode() {
        return SaTokenTenantContext.getTenantCode();
    }

    /**
     * 获取当前租户Schema
     */
    public static String getCurrentTenantSchema() {
        return SaTokenTenantContext.getTenantSchema();
    }

    /**
     * 获取当前登录用户ID
     */
    public static Long getCurrentUserId() {
        return SaTokenTenantContext.getCurrentUserId();
    }

    /**
     * 获取当前登录用户名
     */
    public static String getCurrentUsername() {
        SaTokenTenantContext.TenantInfo tenantInfo = SaTokenTenantContext.getTenantInfo();
        return tenantInfo != null ? tenantInfo.getUsername() : null;
    }

    /**
     * 检查当前是否有有效的租户上下文
     */
    public static boolean hasValidTenantContext() {
        return SaTokenTenantContext.hasValidTenantContext();
    }

    /**
     * 检查当前用户是否已登录
     */
    public static boolean isUserLoggedIn() {
        return StpUtil.isLogin();
    }

    /**
     * 在指定租户上下文中执行操作
     * 基于Sa-Token Session的临时租户切换
     *
     * @param tenantCode 租户代码
     * @param schemaName Schema名称
     * @param runnable 要执行的操作
     */
    public static void runInTenant(String tenantCode, String schemaName, Runnable runnable) {
        if (!StpUtil.isLogin()) {
            log.warn("用户未登录，无法切换租户上下文");
            runnable.run();
            return;
        }

        // 保存原始租户信息
        SaTokenTenantContext.TenantInfo originalTenant = SaTokenTenantContext.getTenantInfo();

        try {
            // 临时设置新的租户信息
            SaTokenTenantContext.setTenant(tenantCode, "临时租户", schemaName);
            runnable.run();
        } finally {
            // 恢复原始租户信息
            if (originalTenant != null) {
                SaTokenTenantContext.setTenant(
                    originalTenant.getTenantCode(),
                    originalTenant.getTenantName(),
                    originalTenant.getSchemaName(),
                    originalTenant.getUsername()
                );
            } else {
                SaTokenTenantContext.clearTenant();
            }
        }
    }

    /**
     * 在公共Schema中执行操作
     * 通常用于查询公共表（pub_account, pub_tenant等）
     *
     * @param runnable 要执行的操作
     */
    public static void runInPublicSchema(Runnable runnable) {
        runInTenant("public", SaTokenTenantContext.DEFAULT_SCHEMA, runnable);
    }

    /**
     * 强制清除当前租户上下文
     * 谨慎使用，通常在特殊情况下才需要
     */
    public static void clearTenantContext() {
        SaTokenTenantContext.clearTenant();
        log.debug("强制清除租户上下文");
    }

    /**
     * 获取租户信息摘要（用于日志）
     */
    public static String getTenantSummary() {
        return SaTokenTenantContext.getTenantSummary();
    }

    /**
     * 获取用户信息摘要（用于日志）
     */
    public static String getUserSummary() {
        Long userId = getCurrentUserId();
        String username = getCurrentUsername();

        if (userId == null && username == null) {
            return "未登录用户";
        }

        return String.format("用户[%s:%s]",
            userId != null ? userId : "unknown",
            username != null ? username : "unknown");
    }

    /**
     * 获取完整的上下文摘要（用于日志和调试）
     */
    public static String getContextSummary() {
        return String.format("%s @ %s", getUserSummary(), getTenantSummary());
    }
}
