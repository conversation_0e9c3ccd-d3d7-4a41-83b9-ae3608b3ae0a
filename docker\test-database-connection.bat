@echo off
REM 美姿姿 - 数据库连接测试脚本 (Windows版本)

setlocal enabledelayedexpansion

echo [INFO] 美姿姿数据库连接测试开始

REM 检查参数
if "%~1"=="clean" goto cleanup

REM 检查Docker
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未运行，请先启动Docker
    exit /b 1
)
echo [SUCCESS] Docker 运行正常

REM 创建网络
docker network ls | findstr "beautiful-posture-network" >nul
if errorlevel 1 (
    echo [INFO] 创建Docker网络...
    docker network create beautiful-posture-network --driver bridge
    echo [SUCCESS] 网络创建成功
) else (
    echo [INFO] 网络已存在
)

REM 启动PostgreSQL
echo [INFO] 启动PostgreSQL容器...
docker ps | findstr "beautiful-posture-postgres" >nul
if errorlevel 1 (
    docker run -d ^
        --name beautiful-posture-postgres ^
        --network beautiful-posture-network ^
        -e POSTGRES_DB=beautiful_posture ^
        -e POSTGRES_USER=postgres ^
        -e POSTGRES_PASSWORD=postgres123 ^
        -p 5432:5432 ^
        -v "%cd%\scripts\init-db.sql:/docker-entrypoint-initdb.d/init-db.sql" ^
        postgres:15-alpine
    
    echo [INFO] 等待PostgreSQL启动...
    timeout /t 10 /nobreak >nul
) else (
    echo [WARNING] PostgreSQL容器已在运行
)

REM 测试PostgreSQL连接
docker exec beautiful-posture-postgres pg_isready -U postgres >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PostgreSQL 连接失败
    exit /b 1
)
echo [SUCCESS] PostgreSQL 连接正常

REM 启动Redis
echo [INFO] 启动Redis容器...
docker ps | findstr "beautiful-posture-redis" >nul
if errorlevel 1 (
    docker run -d ^
        --name beautiful-posture-redis ^
        --network beautiful-posture-network ^
        -p 6379:6379 ^
        redis:7-alpine
    
    echo [INFO] 等待Redis启动...
    timeout /t 5 /nobreak >nul
) else (
    echo [WARNING] Redis容器已在运行
)

REM 测试Redis连接
docker exec beautiful-posture-redis redis-cli ping | findstr "PONG" >nul
if errorlevel 1 (
    echo [ERROR] Redis 连接失败
    exit /b 1
)
echo [SUCCESS] Redis 连接正常

REM 等待服务完全启动
echo [INFO] 等待服务完全启动...
timeout /t 5 /nobreak >nul

REM 测试数据库表
echo [INFO] 测试数据库表结构...
docker exec beautiful-posture-postgres psql -U postgres -d beautiful_posture -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public';" | findstr "sys_user" >nul
if errorlevel 1 (
    echo [ERROR] 用户表创建失败
    exit /b 1
)
echo [SUCCESS] 用户表创建成功

docker exec beautiful-posture-postgres psql -U postgres -d beautiful_posture -t -c "SELECT tablename FROM pg_tables WHERE schemaname = 'public';" | findstr "sys_role" >nul
if errorlevel 1 (
    echo [ERROR] 角色表创建失败
    exit /b 1
)
echo [SUCCESS] 角色表创建成功

REM 检查默认数据
for /f %%i in ('docker exec beautiful-posture-postgres psql -U postgres -d beautiful_posture -t -c "SELECT COUNT(*) FROM sys_user WHERE username = 'admin';"') do set user_count=%%i
if "%user_count%"==" 1" (
    echo [SUCCESS] 默认管理员用户创建成功
) else (
    echo [ERROR] 默认管理员用户创建失败
    exit /b 1
)

REM 测试Redis数据库
echo [INFO] 测试Redis数据库...
for %%d in (0 1 2) do (
    docker exec beautiful-posture-redis redis-cli -n %%d ping | findstr "PONG" >nul
    if errorlevel 1 (
        echo [ERROR] Redis数据库 %%d 连接失败
        exit /b 1
    )
    echo [SUCCESS] Redis数据库 %%d 连接正常
)

REM 显示连接信息
echo [INFO] 数据库连接信息：
echo PostgreSQL:
echo   Host: localhost
echo   Port: 5432
echo   Database: beautiful_posture
echo   Username: postgres
echo   Password: postgres123
echo.
echo Redis:
echo   Host: localhost
echo   Port: 6379
echo   Databases: 0 (Admin API), 1 (App API), 2 (Client API)
echo.
echo 测试连接：
echo   psql -h localhost -p 5432 -U postgres -d beautiful_posture
echo   redis-cli -h localhost -p 6379

echo [SUCCESS] 所有测试通过！数据库配置正确
echo [INFO] 使用 '%0 clean' 清理测试环境
goto end

:cleanup
echo [INFO] 清理测试环境...
docker stop beautiful-posture-postgres beautiful-posture-redis >nul 2>&1
docker rm beautiful-posture-postgres beautiful-posture-redis >nul 2>&1
docker network rm beautiful-posture-network >nul 2>&1
echo [SUCCESS] 清理完成
goto end

:end
