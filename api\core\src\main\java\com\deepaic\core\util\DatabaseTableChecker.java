package com.deepaic.core.util;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.*;

/**
 * 数据库表结构检查工具
 * 用于检查数据库中的实际表结构并生成对应的实体类
 * 
 * <AUTHOR>
 */
@Slf4j
public class DatabaseTableChecker {
    
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres";
    private static final String DB_PASSWORD = "deepaic!2025";
    
    public static void main(String[] args) {
        DatabaseTableChecker checker = new DatabaseTableChecker();
        checker.checkDatabaseTables();
    }
    
    public void checkDatabaseTables() {
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            log.info("成功连接到数据库: {}", DB_URL);
            
            // 检查public schema的表
            checkSchemaTable(conn, "public");
            
            // 检查其他schema的表
            List<String> schemas = getSchemas(conn);
            for (String schema : schemas) {
                if (!"public".equals(schema) && !"information_schema".equals(schema) && !"pg_catalog".equals(schema)) {
                    checkSchemaTable(conn, schema);
                }
            }
            
        } catch (SQLException e) {
            log.error("数据库连接失败: {}", e.getMessage(), e);
        }
    }
    
    private List<String> getSchemas(Connection conn) throws SQLException {
        List<String> schemas = new ArrayList<>();
        String sql = "SELECT schema_name FROM information_schema.schemata WHERE schema_name NOT LIKE 'pg_%'";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                schemas.add(rs.getString("schema_name"));
            }
        }
        
        return schemas;
    }
    
    private void checkSchemaTable(Connection conn, String schemaName) throws SQLException {
        log.info("\n=== 检查 {} schema 中的表 ===", schemaName);
        
        String sql = "SELECT table_name FROM information_schema.tables WHERE table_schema = ? ORDER BY table_name";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, schemaName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String tableName = rs.getString("table_name");
                    log.info("发现表: {}.{}", schemaName, tableName);
                    
                    // 获取表的列信息
                    getTableColumns(conn, schemaName, tableName);
                }
            }
        }
    }
    
    private void getTableColumns(Connection conn, String schemaName, String tableName) throws SQLException {
        String sql = """
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length,
                numeric_precision,
                numeric_scale
            FROM information_schema.columns 
            WHERE table_schema = ? AND table_name = ? 
            ORDER BY ordinal_position
        """;
        
        List<ColumnInfo> columns = new ArrayList<>();
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, schemaName);
            stmt.setString(2, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ColumnInfo column = new ColumnInfo();
                    column.name = rs.getString("column_name");
                    column.dataType = rs.getString("data_type");
                    column.nullable = "YES".equals(rs.getString("is_nullable"));
                    column.defaultValue = rs.getString("column_default");
                    column.maxLength = rs.getObject("character_maximum_length", Integer.class);
                    column.precision = rs.getObject("numeric_precision", Integer.class);
                    column.scale = rs.getObject("numeric_scale", Integer.class);
                    
                    columns.add(column);
                }
            }
        }
        
        // 打印表结构
        log.info("  表 {}.{} 的列信息:", schemaName, tableName);
        for (ColumnInfo column : columns) {
            log.info("    {} {} {} {}", 
                column.name, 
                column.dataType,
                column.nullable ? "NULL" : "NOT NULL",
                column.defaultValue != null ? "DEFAULT " + column.defaultValue : ""
            );
        }
        
        // 生成实体类建议
        generateEntitySuggestion(schemaName, tableName, columns);
    }
    
    private void generateEntitySuggestion(String schemaName, String tableName, List<ColumnInfo> columns) {
        String className = toCamelCase(tableName.replaceFirst("^(pub_|sys_)", ""));
        
        log.info("  建议的实体类名: {}", className);
        log.info("  对应的@TableName: {}", tableName);
        
        // 检查是否有对应的实体类文件
        String entityPath = String.format("api/core/src/main/java/com/deepaic/core/entity/%s.java", className);
        log.info("  实体类文件路径: {}", entityPath);
    }
    
    private String toCamelCase(String str) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;
        
        for (char c : str.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }
        
        return result.toString();
    }
    
    private static class ColumnInfo {
        String name;
        String dataType;
        boolean nullable;
        String defaultValue;
        Integer maxLength;
        Integer precision;
        Integer scale;
    }
}
