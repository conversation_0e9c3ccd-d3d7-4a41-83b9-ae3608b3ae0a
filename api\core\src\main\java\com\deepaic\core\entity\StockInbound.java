package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_stock_inbound")
public class StockInbound extends BaseEntity {

    /**
     * 入库单号
     */
    private String code;

    /**
     * 供应商
     */
    private Long supplierId;

    /**
     * 入库日期
     */
    private LocalDate inboundDate;

    private String remark;

    /**
     * 签字
     */
    private String signatureFile;

    /**
     * 门店id或者仓库id
     */
    private Long storeWarehouseId;

    /**
     * 门店或者仓库
     */
    private Short storeWarehouseType;

    private Short status;
}
