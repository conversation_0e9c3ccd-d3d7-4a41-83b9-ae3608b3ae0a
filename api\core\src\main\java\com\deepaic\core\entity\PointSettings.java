package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 积分策略设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_point_settings")
public class PointSettings extends BaseEntity {

    /**
     * 是否开启积分策略
     */
    private Boolean isEnabled;

    /**
     * 有效期类型:1-永久有效,2-每年固定日期清零,3-自动过期
     */
    private Short expirationType;

    /**
     * 每年清零的固定日期(当expiration_type=2时使用)
     */
    private LocalDate expirationDate;

    /**
     * 自动过期天数(当expiration_type=3时使用)
     */
    private Integer expiryDays;

    /**
     * 开单积分是否启用
     */
    private Boolean billingEnabled;

    /**
     * 开单适用场景:客户到店消费
     */
    private String billingScenario;

    /**
     * 开单积分方式:FIXED-每次固定积分,PERCENTAGE-按比例
     */
    private String billingEarningMethod;

    /**
     * 开单积分获取值
     */
    private Integer billingPointValue;

    /**
     * 开单固定金额
     */
    private BigDecimal billingFixedAmount;

    /**
     * 充值积分是否启用
     */
    private Boolean rechargeEnabled;

    /**
     * 充值适用场景:充值次数或储值卡
     */
    private String rechargeScenario;

    /**
     * 充值积分方式:FIXED-每次固定积分,PERCENTAGE-按比例
     */
    private String rechargeEarningMethod;

    /**
     * 充值积分获取值
     */
    private Integer rechargePointValue;

    /**
     * 充值固定金额
     */
    private BigDecimal rechargeFixedAmount;

    /**
     * 积分兑换比例(积分)
     */
    private Integer redemptionRatioPoints;

    /**
     * 积分兑换比例(现金)
     */
    private BigDecimal redemptionRatioCash;

    /**
     * 客户每次结账是否扣除积分
     */
    private Boolean deductOnCheckout;

    /**
     * 每次结账扣除积分数
     */
    private Integer deductPoints;
}
