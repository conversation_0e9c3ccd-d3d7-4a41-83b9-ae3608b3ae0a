package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 会员权益 - 定金
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_deposit")
public class MemberDeposit extends BaseEntity {

    private Long memberId;

    private Long rechargeId;

    private BigDecimal balance;

    /**
     * 1=未用完，2=已用完
     */
    private Short status;

    // 状态常量
    public static final short STATUS_NORMAL = 1;   // 正常
    public static final short STATUS_FROZEN = 2;   // 冻结
}
