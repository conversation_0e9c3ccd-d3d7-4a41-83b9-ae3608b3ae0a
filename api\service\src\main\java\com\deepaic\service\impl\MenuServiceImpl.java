package com.deepaic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.entity.Menu;
import com.deepaic.core.dto.MenuDTO;
import com.deepaic.core.mapper.MenuMapper;
import com.deepaic.core.mapper.RoleMenuMapper;

import com.deepaic.service.IMenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 菜单服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MenuServiceImpl extends BaseServiceImpl<MenuMapper, Menu> implements IMenuService {

    private final MenuMapper menuMapper;
    private final RoleMenuMapper roleMenuMapper;

    @Override
    public IPage<MenuDTO> getMenuPage(Page<MenuDTO> page, MenuDTO.MenuQueryDTO query) {
        return menuMapper.selectMenuPage(page, query);
    }

    @Override
    public List<MenuDTO> getMenuList(MenuDTO.MenuQueryDTO query) {
        return menuMapper.selectMenuList(query);
    }

    @Override
    public List<MenuDTO> getMenuTree(MenuDTO.MenuQueryDTO query) {
        List<MenuDTO> menuList = menuMapper.selectMenuList(query);
        return buildMenuTree(menuList);
    }

    @Override
    public List<MenuDTO> getMenuTreeByUserId(Long userId) {
        List<MenuDTO> menuList = menuMapper.selectMenuTreeByUserId(userId);
        return buildMenuTree(menuList);
    }

    @Override
    public List<String> getMenuPermissionsByUserId(Long userId) {
        return menuMapper.selectMenuPermissionsByUserId(userId);
    }

    @Override
    public List<MenuDTO> getMenuListByRoleId(Long roleId) {
        return menuMapper.selectMenuListByRoleId(roleId);
    }

    @Override
    @Transactional
    public Long createMenu(MenuDTO menuDTO) {
        // 检查菜单名称是否唯一
        if (!checkMenuNameUnique(menuDTO.getMenuName(), menuDTO.getParentId(), null)) {
            throw new RuntimeException("菜单名称已存在");
        }

        Menu menu = new Menu();
        BeanUtils.copyProperties(menuDTO, menu);

        // 设置默认值
        if (menu.getParentId() == null) {
            menu.setParentId(0L);
        }
        if (menu.getOrderNum() == null) {
            menu.setOrderNum(0);
        }
        if (menu.getVisible() == null) {
            menu.setVisible(Menu.VISIBLE_SHOW);
        }
        if (menu.getStatus() == null) {
            menu.setStatus(Menu.STATUS_ENABLED);
        }
        if (menu.getIsFrame() == null) {
            menu.setIsFrame(Menu.FRAME_NO);
        }
        if (menu.getIsCache() == null) {
            menu.setIsCache(Menu.CACHE_NO);
        }

        boolean success = save(menu);
        if (!success) {
            throw new RuntimeException("创建菜单失败");
        }

        log.info("创建菜单成功: menuName={}, id={}", menu.getMenuName(), menu.getId());
        return menu.getId();
    }

    @Override
    @Transactional
    public boolean updateMenu(Long id, MenuDTO menuDTO) {
        Menu existingMenu = getById(id);
        if (existingMenu == null) {
            throw new RuntimeException("菜单不存在: " + id);
        }

        // 检查菜单名称是否唯一
        if (!checkMenuNameUnique(menuDTO.getMenuName(), menuDTO.getParentId(), id)) {
            throw new RuntimeException("菜单名称已存在");
        }

        // 不能将父菜单设置为自己或自己的子菜单
        if (menuDTO.getParentId() != null && menuDTO.getParentId().equals(id)) {
            throw new RuntimeException("不能将父菜单设置为自己");
        }

        BeanUtils.copyProperties(menuDTO, existingMenu, "id", "createTime", "version", "deleted");

        boolean success = updateById(existingMenu);
        if (success) {
            log.info("更新菜单成功: id={}, menuName={}", id, existingMenu.getMenuName());
        }
        return success;
    }

    @Override
    @Transactional
    public boolean deleteMenu(Long id) {
        Menu menu = getById(id);
        if (menu == null) {
            return false;
        }

        // 检查是否有子菜单
        Integer childrenCount = menuMapper.countChildrenByParentId(id);
        if (childrenCount != null && childrenCount > 0) {
            throw new RuntimeException("存在子菜单，无法删除");
        }

        // 删除角色菜单关联
        roleMenuMapper.deleteByMenuId(id);

        boolean success = removeById(id);
        if (success) {
            log.info("删除菜单成功: id={}, menuName={}", id, menu.getMenuName());
        }
        return success;
    }

    @Override
    @Transactional
    public boolean deleteMenus(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 检查每个菜单是否可以删除
        for (Long id : ids) {
            Integer childrenCount = menuMapper.countChildrenByParentId(id);
            if (childrenCount != null && childrenCount > 0) {
                Menu menu = getById(id);
                throw new RuntimeException("菜单 " + (menu != null ? menu.getMenuName() : id) + " 存在子菜单，无法删除");
            }
        }

        // 删除角色菜单关联
        for (Long id : ids) {
            roleMenuMapper.deleteByMenuId(id);
        }

        boolean success = removeByIds(ids);
        if (success) {
            log.info("批量删除菜单成功: count={}", ids.size());
        }
        return success;
    }

    @Override
    public MenuDTO getMenuById(Long id) {
        return menuMapper.selectMenuById(id);
    }

    @Override
    public boolean checkMenuNameUnique(String menuName, Long parentId, Long id) {
        Integer count = menuMapper.checkMenuNameUnique(menuName, parentId, id);
        return count == null || count == 0;
    }

    @Override
    public List<MenuDTO> buildMenuTree(List<MenuDTO> menuList) {
        List<MenuDTO> tree = new ArrayList<>();
        
        for (MenuDTO menu : menuList) {
            if (menu.getParentId() == null || menu.getParentId() == 0) {
                tree.add(menu);
            }
        }
        
        for (MenuDTO parent : tree) {
            parent.setChildren(getChildren(parent, menuList));
        }
        
        return tree;
    }

    @Override
    public List<MenuDTO> getMenuSelectTree() {
        MenuDTO.MenuQueryDTO query = new MenuDTO.MenuQueryDTO();
        query.setStatus(Menu.STATUS_ENABLED);
        List<MenuDTO> menuList = menuMapper.selectMenuList(query);
        
        // 添加根节点
        MenuDTO rootMenu = new MenuDTO();
        rootMenu.setId(0L);
        rootMenu.setMenuName("主类目");
        rootMenu.setParentId(-1L);
        menuList.add(0, rootMenu);
        
        return buildMenuTree(menuList);
    }

    /**
     * 递归获取子菜单
     */
    private List<MenuDTO> getChildren(MenuDTO parent, List<MenuDTO> menuList) {
        List<MenuDTO> children = new ArrayList<>();
        
        for (MenuDTO menu : menuList) {
            if (parent.getId().equals(menu.getParentId())) {
                menu.setChildren(getChildren(menu, menuList));
                children.add(menu);
            }
        }
        
        return children;
    }
}
