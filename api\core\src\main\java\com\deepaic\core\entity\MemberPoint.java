package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 会员权益-积分记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_point")
public class MemberPoint extends BaseEntity {

    private Long memberId;

    /**
     * 1=收入积分，2=消耗积分
     */
    private Short type;

    private Long consumeId;

    private Short point;

    /**
     * 1=有效，2=过期
     */
    private Short status;
}
