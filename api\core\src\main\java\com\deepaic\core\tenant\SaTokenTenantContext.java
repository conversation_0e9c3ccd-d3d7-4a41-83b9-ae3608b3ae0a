package com.deepaic.core.tenant;

import cn.dev33.satoken.stp.StpUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 基于Sa-Token Session的租户上下文管理器
 * 更优雅的实现方式，利用Sa-Token的Session机制存储租户信息
 * 
 * 优势：
 * 1. 无需ThreadLocal，避免内存泄漏风险
 * 2. 与Sa-Token深度集成，生命周期一致
 * 3. 支持分布式Session（Redis）
 * 4. 自动清理，无需手动管理
 * 
 * <AUTHOR>
 */
@Slf4j
public class SaTokenTenantContext {

    /**
     * 默认schema名称
     */
    public static final String DEFAULT_SCHEMA = "public";
    
    /**
     * 默认租户代码
     */
    public static final String DEFAULT_TENANT_CODE = "default";

    // Session中的键名
    private static final String SESSION_TENANT_INFO = "tenant_info";
    private static final String SESSION_TENANT_CODE = "tenant_code";
    private static final String SESSION_TENANT_SCHEMA = "tenant_schema";
    private static final String SESSION_TENANT_NAME = "tenant_name";

    /**
     * 租户信息
     */
    @Data
    public static class TenantInfo {
        private String tenantCode;
        private String tenantName;
        private String schemaName;
        private Long userId;
        private String username;

        public TenantInfo() {}

        public TenantInfo(String tenantCode, String tenantName, String schemaName, Long userId, String username) {
            this.tenantCode = tenantCode;
            this.tenantName = tenantName;
            this.schemaName = schemaName;
            this.userId = userId;
            this.username = username;
        }
    }

    /**
     * 设置租户信息到Sa-Token Session
     */
    public static void setTenant(String tenantCode, String tenantName, String schemaName) {
        if (!StpUtil.isLogin()) {
            log.warn("用户未登录，无法设置租户信息");
            return;
        }

        try {
            Long userId = StpUtil.getLoginIdAsLong();
            TenantInfo tenantInfo = new TenantInfo(tenantCode, tenantName, schemaName, userId, null);
            
            // 存储到Session
            StpUtil.getSession().set(SESSION_TENANT_INFO, tenantInfo);
            StpUtil.getSession().set(SESSION_TENANT_CODE, tenantCode);
            StpUtil.getSession().set(SESSION_TENANT_SCHEMA, schemaName);
            StpUtil.getSession().set(SESSION_TENANT_NAME, tenantName);
            
            log.debug("设置租户信息到Session: tenantCode={}, schema={}, userId={}", 
                tenantCode, schemaName, userId);
        } catch (Exception e) {
            log.error("设置租户信息失败", e);
        }
    }

    /**
     * 设置完整的租户信息
     */
    public static void setTenant(String tenantCode, String tenantName, String schemaName, String username) {
        if (!StpUtil.isLogin()) {
            log.warn("用户未登录，无法设置租户信息");
            return;
        }

        try {
            Long userId = StpUtil.getLoginIdAsLong();
            TenantInfo tenantInfo = new TenantInfo(tenantCode, tenantName, schemaName, userId, username);
            
            // 存储到Session
            StpUtil.getSession().set(SESSION_TENANT_INFO, tenantInfo);
            StpUtil.getSession().set(SESSION_TENANT_CODE, tenantCode);
            StpUtil.getSession().set(SESSION_TENANT_SCHEMA, schemaName);
            StpUtil.getSession().set(SESSION_TENANT_NAME, tenantName);
            
            log.debug("设置完整租户信息到Session: user={}, tenant={}, schema={}", 
                username, tenantCode, schemaName);
        } catch (Exception e) {
            log.error("设置租户信息失败", e);
        }
    }

    /**
     * 获取当前租户代码
     */
    public static String getTenantCode() {
        if (!StpUtil.isLogin()) {
            return DEFAULT_TENANT_CODE;
        }

        try {
            String tenantCode = StpUtil.getSession().getString(SESSION_TENANT_CODE);
            return tenantCode != null ? tenantCode : DEFAULT_TENANT_CODE;
        } catch (Exception e) {
            log.debug("获取租户代码失败，使用默认值", e);
            return DEFAULT_TENANT_CODE;
        }
    }

    /**
     * 获取当前租户Schema
     */
    public static String getTenantSchema() {
        if (!StpUtil.isLogin()) {
            return DEFAULT_SCHEMA;
        }

        try {
            String schema = StpUtil.getSession().getString(SESSION_TENANT_SCHEMA);
            return schema != null ? schema : DEFAULT_SCHEMA;
        } catch (Exception e) {
            log.debug("获取租户Schema失败，使用默认值", e);
            return DEFAULT_SCHEMA;
        }
    }

    /**
     * 获取当前租户名称
     */
    public static String getTenantName() {
        if (!StpUtil.isLogin()) {
            return "默认租户";
        }

        try {
            String tenantName = StpUtil.getSession().getString(SESSION_TENANT_NAME);
            return tenantName != null ? tenantName : "默认租户";
        } catch (Exception e) {
            log.debug("获取租户名称失败，使用默认值", e);
            return "默认租户";
        }
    }

    /**
     * 获取完整的租户信息
     */
    public static TenantInfo getTenantInfo() {
        if (!StpUtil.isLogin()) {
            return null;
        }

        try {
            return StpUtil.getSession().getModel(SESSION_TENANT_INFO, TenantInfo.class);
        } catch (Exception e) {
            log.debug("获取租户信息失败", e);
            return null;
        }
    }

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentUserId() {
        if (!StpUtil.isLogin()) {
            return null;
        }
        return StpUtil.getLoginIdAsLong();
    }

    /**
     * 检查是否有有效的租户上下文
     */
    public static boolean hasValidTenantContext() {
        if (!StpUtil.isLogin()) {
            return false;
        }

        String schema = getTenantSchema();
        return schema != null && !DEFAULT_SCHEMA.equals(schema);
    }

    /**
     * 清除租户信息（通常在注销时调用）
     */
    public static void clearTenant() {
        if (!StpUtil.isLogin()) {
            return;
        }

        try {
            StpUtil.getSession().delete(SESSION_TENANT_INFO);
            StpUtil.getSession().delete(SESSION_TENANT_CODE);
            StpUtil.getSession().delete(SESSION_TENANT_SCHEMA);
            StpUtil.getSession().delete(SESSION_TENANT_NAME);
            
            log.debug("清除Session中的租户信息");
        } catch (Exception e) {
            log.error("清除租户信息失败", e);
        }
    }

    /**
     * 获取租户信息摘要
     */
    public static String getTenantSummary() {
        if (!StpUtil.isLogin()) {
            return "未登录";
        }

        String tenantCode = getTenantCode();
        String schema = getTenantSchema();
        Long userId = getCurrentUserId();
        
        return String.format("用户[%s] @ 租户[%s:%s]", userId, tenantCode, schema);
    }

    /**
     * 检查当前租户是否为默认租户
     */
    public static boolean isDefaultTenant() {
        return DEFAULT_TENANT_CODE.equals(getTenantCode()) || 
               DEFAULT_SCHEMA.equals(getTenantSchema());
    }
}
