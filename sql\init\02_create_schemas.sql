-- =====================================================
-- 美姿姿健康管理系统 - Schema创建脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-20
-- 作者: 美姿姿团队
-- 说明: 创建多租户Schema结构
-- 数据库连接: *****************************************************/mzz
-- =====================================================

-- 确保在正确的数据库中执行
\c mzz

-- 设置客户端编码
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- 创建必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" SCHEMA public;
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" SCHEMA public;
CREATE EXTENSION IF NOT EXISTS "pg_trgm" SCHEMA public;

-- 创建版本管理表（如果不存在）
CREATE TABLE IF NOT EXISTS public.schema_version (
    id SERIAL PRIMARY KEY,
    version VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    execution_time INTERVAL,
    checksum VARCHAR(64)
);

-- 记录开始时间
DO $$
DECLARE
    start_time TIMESTAMP := NOW();
BEGIN
    -- 创建公共Schema（如果不存在）
    -- public schema 已经存在，用于存储公共表
    
    -- 创建系统管理Schema
    CREATE SCHEMA IF NOT EXISTS system_admin;
    COMMENT ON SCHEMA system_admin IS '系统管理Schema，存储系统级配置和管理数据';
    
    -- 创建审计Schema
    CREATE SCHEMA IF NOT EXISTS audit;
    COMMENT ON SCHEMA audit IS '审计Schema，存储审计日志和操作记录';
    
    -- 创建报表Schema
    CREATE SCHEMA IF NOT EXISTS reporting;
    COMMENT ON SCHEMA reporting IS '报表Schema，存储报表相关的视图和函数';
    
    -- 创建临时Schema
    CREATE SCHEMA IF NOT EXISTS temp_data;
    COMMENT ON SCHEMA temp_data IS '临时数据Schema，存储临时表和中间数据';
    
    -- 创建示例租户Schema（用于测试）
    CREATE SCHEMA IF NOT EXISTS tenant_demo;
    COMMENT ON SCHEMA tenant_demo IS '示例租户Schema，用于演示和测试';
    
    RAISE NOTICE 'Schema创建完成，耗时: %', NOW() - start_time;
END $$;

-- 设置Schema权限
-- 授予应用用户权限
GRANT USAGE ON SCHEMA public TO mzz_user;
GRANT USAGE ON SCHEMA system_admin TO mzz_user;
GRANT USAGE ON SCHEMA audit TO mzz_user;
GRANT USAGE ON SCHEMA reporting TO mzz_user;
GRANT USAGE ON SCHEMA temp_data TO mzz_user;
GRANT USAGE ON SCHEMA tenant_demo TO mzz_user;

GRANT CREATE ON SCHEMA public TO mzz_user;
GRANT CREATE ON SCHEMA system_admin TO mzz_user;
GRANT CREATE ON SCHEMA audit TO mzz_user;
GRANT CREATE ON SCHEMA reporting TO mzz_user;
GRANT CREATE ON SCHEMA temp_data TO mzz_user;
GRANT CREATE ON SCHEMA tenant_demo TO mzz_user;

-- 授予只读用户权限
GRANT USAGE ON SCHEMA public TO mzz_readonly;
GRANT USAGE ON SCHEMA system_admin TO mzz_readonly;
GRANT USAGE ON SCHEMA audit TO mzz_readonly;
GRANT USAGE ON SCHEMA reporting TO mzz_readonly;
GRANT USAGE ON SCHEMA tenant_demo TO mzz_readonly;

-- 创建租户Schema管理函数
CREATE OR REPLACE FUNCTION public.create_tenant_schema(tenant_code VARCHAR(50))
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    schema_name VARCHAR(100);
    start_time TIMESTAMP := NOW();
BEGIN
    -- 验证租户代码格式
    IF tenant_code IS NULL OR LENGTH(tenant_code) < 3 OR LENGTH(tenant_code) > 50 THEN
        RAISE EXCEPTION '租户代码格式无效: %', tenant_code;
    END IF;
    
    -- 构造Schema名称
    schema_name := 'tenant_' || lower(tenant_code);
    
    -- 检查Schema是否已存在
    IF EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = schema_name) THEN
        RAISE NOTICE 'Schema % 已存在', schema_name;
        RETURN FALSE;
    END IF;
    
    -- 创建Schema
    EXECUTE format('CREATE SCHEMA %I', schema_name);
    EXECUTE format('COMMENT ON SCHEMA %I IS ''租户Schema: %s，创建时间: %s''', 
                   schema_name, tenant_code, NOW());
    
    -- 授予权限
    EXECUTE format('GRANT USAGE ON SCHEMA %I TO mzz_user', schema_name);
    EXECUTE format('GRANT CREATE ON SCHEMA %I TO mzz_user', schema_name);
    EXECUTE format('GRANT USAGE ON SCHEMA %I TO mzz_readonly', schema_name);
    
    -- 记录日志
    INSERT INTO audit.schema_operations (
        operation_type, schema_name, tenant_code, 
        executed_by, executed_at, execution_time
    ) VALUES (
        'CREATE_SCHEMA', schema_name, tenant_code,
        current_user, NOW(), NOW() - start_time
    );
    
    RAISE NOTICE 'Schema % 创建成功，耗时: %', schema_name, NOW() - start_time;
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION '创建租户Schema失败: %', SQLERRM;
END $$;

-- 创建删除租户Schema函数
CREATE OR REPLACE FUNCTION public.drop_tenant_schema(tenant_code VARCHAR(50), force_drop BOOLEAN DEFAULT FALSE)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    schema_name VARCHAR(100);
    table_count INTEGER;
    start_time TIMESTAMP := NOW();
BEGIN
    -- 构造Schema名称
    schema_name := 'tenant_' || lower(tenant_code);
    
    -- 检查Schema是否存在
    IF NOT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = schema_name) THEN
        RAISE NOTICE 'Schema % 不存在', schema_name;
        RETURN FALSE;
    END IF;
    
    -- 检查Schema中是否有表
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = schema_name;
    
    IF table_count > 0 AND NOT force_drop THEN
        RAISE EXCEPTION 'Schema % 中存在 % 个表，请使用 force_drop=true 强制删除', schema_name, table_count;
    END IF;
    
    -- 删除Schema
    EXECUTE format('DROP SCHEMA %I CASCADE', schema_name);
    
    -- 记录日志
    INSERT INTO audit.schema_operations (
        operation_type, schema_name, tenant_code, 
        executed_by, executed_at, execution_time, notes
    ) VALUES (
        'DROP_SCHEMA', schema_name, tenant_code,
        current_user, NOW(), NOW() - start_time,
        format('删除了包含 %s 个表的Schema', table_count)
    );
    
    RAISE NOTICE 'Schema % 删除成功，耗时: %', schema_name, NOW() - start_time;
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION '删除租户Schema失败: %', SQLERRM;
END $$;

-- 创建Schema操作审计表
CREATE TABLE IF NOT EXISTS audit.schema_operations (
    id BIGSERIAL PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL,
    schema_name VARCHAR(100) NOT NULL,
    tenant_code VARCHAR(50),
    executed_by VARCHAR(100) NOT NULL DEFAULT current_user,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    execution_time INTERVAL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_schema_operations_tenant ON audit.schema_operations(tenant_code);
CREATE INDEX IF NOT EXISTS idx_schema_operations_executed_at ON audit.schema_operations(executed_at);

-- 设置默认搜索路径
ALTER DATABASE mzz SET search_path TO public, system_admin, audit, reporting;

-- 记录脚本执行
INSERT INTO public.schema_version (version, description, executed_at) 
VALUES ('1.0.0', '创建Schema结构和管理函数', NOW())
ON CONFLICT (version) DO UPDATE SET 
    executed_at = NOW(),
    description = EXCLUDED.description;

RAISE NOTICE '==============================================';
RAISE NOTICE 'Schema创建脚本执行完成';
RAISE NOTICE '已创建的Schema:';
RAISE NOTICE '- public (公共表)';
RAISE NOTICE '- system_admin (系统管理)';
RAISE NOTICE '- audit (审计日志)';
RAISE NOTICE '- reporting (报表)';
RAISE NOTICE '- temp_data (临时数据)';
RAISE NOTICE '- tenant_demo (示例租户)';
RAISE NOTICE '==============================================';

-- 显示当前所有Schema
SELECT 
    schema_name,
    schema_owner,
    CASE 
        WHEN schema_name LIKE 'tenant_%' THEN '租户Schema'
        WHEN schema_name = 'public' THEN '公共Schema'
        WHEN schema_name = 'system_admin' THEN '系统管理Schema'
        WHEN schema_name = 'audit' THEN '审计Schema'
        WHEN schema_name = 'reporting' THEN '报表Schema'
        WHEN schema_name = 'temp_data' THEN '临时数据Schema'
        ELSE '系统Schema'
    END AS schema_type
FROM information_schema.schemata 
WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1')
ORDER BY schema_name;
