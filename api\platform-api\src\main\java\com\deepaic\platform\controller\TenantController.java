package com.deepaic.platform.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.dto.TenantDTO;
import com.deepaic.core.dto.TenantQueryDTO;
import com.deepaic.core.entity.Tenant;
import com.deepaic.service.ITenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 租户管理控制器 - Platform API
 * 提供租户的CRUD操作和管理功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/platform/tenant")
@RequiredArgsConstructor
@Validated
public class TenantController {

    private final ITenantService tenantService;

    /**
     * 分页查询租户列表
     */
    @GetMapping("/page")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:list")
    public Map<String, Object> getTenantPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            TenantQueryDTO query) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Page<TenantDTO> page = new Page<>(current, size);
            IPage<TenantDTO> tenantPage = tenantService.getTenantPage(page, query);
            
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", tenantPage);
        } catch (Exception e) {
            log.error("分页查询租户列表失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 查询所有启用的租户列表
     */
    @GetMapping("/list")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:list")
    public Map<String, Object> getEnabledTenants() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Tenant> tenants = tenantService.getEnabledTenants();
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", tenants);
        } catch (Exception e) {
            log.error("查询启用租户列表失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 根据ID查询租户详情
     */
    @GetMapping("/{id}")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:query")
    public Map<String, Object> getTenantById(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            TenantDTO tenant = tenantService.getTenantById(id);
            if (tenant != null) {
                response.put("code", 200);
                response.put("message", "查询成功");
                response.put("data", tenant);
            } else {
                response.put("code", 404);
                response.put("message", "租户不存在");
            }
        } catch (Exception e) {
            log.error("查询租户详情失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 创建租户
     */
    @PostMapping
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:add")
    public Map<String, Object> createTenant(@Valid @RequestBody TenantDTO tenantDTO) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查租户代码是否唯一
            if (tenantService.getTenantByCode(tenantDTO.getTenantCode()) != null) {
                response.put("code", 400);
                response.put("message", "租户代码已存在");
                return response;
            }
            
            // 检查Schema名称是否唯一
            if (tenantService.getTenantBySchemaName(tenantDTO.getSchemaName()) != null) {
                response.put("code", 400);
                response.put("message", "Schema名称已存在");
                return response;
            }
            
            Long tenantId = tenantService.createTenant(tenantDTO);
            if (tenantId != null) {
                response.put("code", 200);
                response.put("message", "创建成功");
                response.put("data", tenantId);
            } else {
                response.put("code", 500);
                response.put("message", "创建失败");
            }
        } catch (Exception e) {
            log.error("创建租户失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 更新租户
     */
    @PutMapping("/{id}")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:edit")
    public Map<String, Object> updateTenant(@PathVariable Long id, @Valid @RequestBody TenantDTO tenantDTO) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = tenantService.updateTenant(id, tenantDTO);
            if (success) {
                response.put("code", 200);
                response.put("message", "更新成功");
            } else {
                response.put("code", 500);
                response.put("message", "更新失败");
            }
        } catch (Exception e) {
            log.error("更新租户失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 删除租户
     */
    @DeleteMapping("/{id}")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:remove")
    public Map<String, Object> deleteTenant(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = tenantService.deleteTenant(id);
            if (success) {
                response.put("code", 200);
                response.put("message", "删除成功");
            } else {
                response.put("code", 500);
                response.put("message", "删除失败");
            }
        } catch (Exception e) {
            log.error("删除租户失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 批量删除租户
     */
    @DeleteMapping("/batch")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:remove")
    public Map<String, Object> deleteTenants(@RequestBody List<Long> ids) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = tenantService.deleteTenants(ids);
            if (success) {
                response.put("code", 200);
                response.put("message", "批量删除成功");
            } else {
                response.put("code", 500);
                response.put("message", "批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除租户失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 启用租户
     */
    @PutMapping("/{id}/enable")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:edit")
    public Map<String, Object> enableTenant(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = tenantService.enableTenant(id);
            if (success) {
                response.put("code", 200);
                response.put("message", "启用成功");
            } else {
                response.put("code", 500);
                response.put("message", "启用失败");
            }
        } catch (Exception e) {
            log.error("启用租户失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 禁用租户
     */
    @PutMapping("/{id}/disable")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:edit")
    public Map<String, Object> disableTenant(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = tenantService.disableTenant(id);
            if (success) {
                response.put("code", 200);
                response.put("message", "禁用成功");
            } else {
                response.put("code", 500);
                response.put("message", "禁用失败");
            }
        } catch (Exception e) {
            log.error("禁用租户失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 暂停租户
     */
    @PutMapping("/{id}/suspend")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:edit")
    public Map<String, Object> suspendTenant(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = tenantService.suspendTenant(id);
            if (success) {
                response.put("code", 200);
                response.put("message", "暂停成功");
            } else {
                response.put("code", 500);
                response.put("message", "暂停失败");
            }
        } catch (Exception e) {
            log.error("暂停租户失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 续费租户服务
     */
    @PutMapping("/{id}/renew")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:edit")
    public Map<String, Object> renewTenantService(@PathVariable Long id, @RequestParam Integer months) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = tenantService.renewTenantService(id, months);
            if (success) {
                response.put("code", 200);
                response.put("message", "续费成功");
            } else {
                response.put("code", 500);
                response.put("message", "续费失败");
            }
        } catch (Exception e) {
            log.error("续费租户服务失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 升级租户类型
     */
    @PutMapping("/{id}/upgrade")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:edit")
    public Map<String, Object> upgradeTenantType(@PathVariable Long id, @RequestParam Short newType) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = tenantService.upgradeTenantType(id, newType);
            if (success) {
                response.put("code", 200);
                response.put("message", "升级成功");
            } else {
                response.put("code", 500);
                response.put("message", "升级失败");
            }
        } catch (Exception e) {
            log.error("升级租户类型失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 初始化租户Schema
     */
    @PostMapping("/{id}/init-schema")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:edit")
    public Map<String, Object> initializeTenantSchema(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            TenantDTO tenant = tenantService.getTenantById(id);
            if (tenant == null) {
                response.put("code", 404);
                response.put("message", "租户不存在");
                return response;
            }

            boolean success = tenantService.initializeTenantSchema(tenant.getTenantCode());
            if (success) {
                response.put("code", 200);
                response.put("message", "Schema初始化成功");
            } else {
                response.put("code", 500);
                response.put("message", "Schema初始化失败");
            }
        } catch (Exception e) {
            log.error("初始化租户Schema失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }

        return response;
    }

    /**
     * 删除租户Schema
     */
    @DeleteMapping("/{id}/drop-schema")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:remove")
    public Map<String, Object> dropTenantSchema(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            TenantDTO tenant = tenantService.getTenantById(id);
            if (tenant == null) {
                response.put("code", 404);
                response.put("message", "租户不存在");
                return response;
            }

            boolean success = tenantService.dropTenantSchema(tenant.getTenantCode());
            if (success) {
                response.put("code", 200);
                response.put("message", "Schema删除成功");
            } else {
                response.put("code", 500);
                response.put("message", "Schema删除失败");
            }
        } catch (Exception e) {
            log.error("删除租户Schema失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }

        return response;
    }

    /**
     * 获取租户统计信息
     */
    @GetMapping("/statistics")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:list")
    public Map<String, Object> getTenantStatistics() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalTenants", tenantService.count());
            statistics.put("enabledTenants", tenantService.getEnabledTenants().size());
            // 暂时使用简单统计，后续可以实现具体的统计方法
            statistics.put("expiredTenants", 0);
            statistics.put("trialTenants", 0);
            statistics.put("userLimitExceededTenants", 0);
            statistics.put("storageLimitExceededTenants", 0);

            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", statistics);
        } catch (Exception e) {
            log.error("获取租户统计信息失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }

        return response;
    }

    /**
     * 获取即将过期的租户列表
     */
    @GetMapping("/expiring")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:list")
    public Map<String, Object> getExpiringTenants(@RequestParam(defaultValue = "30") Integer days) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 暂时返回空列表，后续实现具体的即将过期租户查询
            List<Tenant> expiringTenants = tenantService.getEnabledTenants();
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", expiringTenants);
        } catch (Exception e) {
            log.error("获取即将过期租户列表失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }

        return response;
    }

    /**
     * 发送租户到期提醒
     */
    @PostMapping("/{id}/send-expiration-reminder")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:edit")
    public Map<String, Object> sendExpirationReminder(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            boolean success = tenantService.sendExpirationReminder(id);
            if (success) {
                response.put("code", 200);
                response.put("message", "提醒发送成功");
            } else {
                response.put("code", 500);
                response.put("message", "提醒发送失败");
            }
        } catch (Exception e) {
            log.error("发送租户到期提醒失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }

        return response;
    }

    /**
     * 检查租户代码是否唯一
     */
    @GetMapping("/check-code")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:list")
    public Map<String, Object> checkTenantCodeUnique(@RequestParam String tenantCode) {
        Map<String, Object> response = new HashMap<>();

        try {
            boolean unique = tenantService.getTenantByCode(tenantCode) == null;
            response.put("code", 200);
            response.put("message", "检查完成");
            response.put("data", unique);
        } catch (Exception e) {
            log.error("检查租户代码唯一性失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }

        return response;
    }

    /**
     * 检查Schema名称是否唯一
     */
    @GetMapping("/check-schema")
    @SaCheckRole("platform-admin")
    @SaCheckPermission("platform:tenant:list")
    public Map<String, Object> checkSchemaNameUnique(@RequestParam String schemaName) {
        Map<String, Object> response = new HashMap<>();

        try {
            boolean unique = tenantService.getTenantBySchemaName(schemaName) == null;
            response.put("code", 200);
            response.put("message", "检查完成");
            response.put("data", unique);
        } catch (Exception e) {
            log.error("检查Schema名称唯一性失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }

        return response;
    }
}
