# 数据库设计原则 - 松耦合架构

## 设计理念

美姿姿健康管理系统采用**松耦合数据库设计**，不使用外键约束，通过应用层维护数据一致性。

## 核心原则

### 1. 不使用外键约束
- 所有表之间不建立物理外键关系
- 关联字段仅作为逻辑标识，不强制数据库层面的引用完整性
- 通过应用程序代码维护数据一致性

### 2. 逻辑关联设计
- 使用 `_id` 后缀的字段表示逻辑关联
- 在字段注释中明确说明关联关系
- 保持字段命名的一致性和可读性

### 3. 应用层数据完整性
- 在业务逻辑层实现数据验证
- 使用事务确保操作的原子性
- 通过代码逻辑维护引用完整性

## 设计优势

### 1. 高性能
- **减少锁竞争**：无外键约束，减少表级锁和行级锁的竞争
- **提升写入性能**：插入、更新、删除操作不需要检查外键约束
- **优化查询性能**：避免因外键约束导致的额外查询开销

### 2. 高可用性
- **降低死锁风险**：外键约束是数据库死锁的常见原因
- **简化故障恢复**：无外键依赖，数据恢复更加灵活
- **支持分库分表**：便于后期进行数据库拆分和分片

### 3. 高灵活性
- **易于重构**：表结构调整不受外键约束限制
- **支持数据迁移**：跨数据库迁移更加便捷
- **便于测试**：测试数据准备和清理更加简单

### 4. 微服务友好
- **服务解耦**：不同服务可以独立管理自己的数据
- **数据库独立**：每个服务可以选择最适合的数据库类型
- **分布式部署**：支持数据库的分布式部署架构

## 关联关系说明

### 公共表关联
```sql
-- 租户用户表
pub_customer_account.sys_user_id -> sys_user.id (逻辑关联)
pub_customer_account.tenant_code -> pub_tenant.tenant_code (逻辑关联)

-- 会员账户表  
pub_member_account.member_user_id -> sys_member.id (逻辑关联)
pub_member_account.tenant_code -> pub_tenant.tenant_code (逻辑关联)
```

### 租户表关联
```sql
-- 用户相关
sys_user.account_id -> pub_customer_account.id (逻辑关联)

-- 组织架构
sys_organization.parent_id -> sys_organization.id (逻辑关联)
sys_organization.leader_id -> sys_user.id (逻辑关联)

-- 菜单层级
sys_menu.parent_id -> sys_menu.id (逻辑关联)

-- 用户扩展信息
sys_user_profile.user_id -> sys_user.id (逻辑关联)
```

### 关联表设计
```sql
-- 用户角色关联
sys_user_role.user_id -> sys_user.id (逻辑关联)
sys_user_role.role_id -> sys_role.id (逻辑关联)

-- 角色权限关联
sys_role_permission.role_id -> sys_role.id (逻辑关联)
sys_role_permission.permission_id -> sys_permission.id (逻辑关联)

-- 用户组织关联
sys_user_organization.user_id -> sys_user.id (逻辑关联)
sys_user_organization.org_id -> sys_organization.id (逻辑关联)
```

## 数据一致性保障

### 1. 应用层验证
```java
// 示例：创建用户时验证租户存在
@Service
public class UserService {
    
    @Transactional
    public void createUser(CreateUserRequest request) {
        // 1. 验证租户是否存在
        Tenant tenant = tenantService.getByCode(request.getTenantCode());
        if (tenant == null) {
            throw new BusinessException("租户不存在");
        }
        
        // 2. 验证组织是否存在
        if (request.getOrgId() != null) {
            Organization org = organizationService.getById(request.getOrgId());
            if (org == null) {
                throw new BusinessException("组织不存在");
            }
        }
        
        // 3. 创建用户
        userMapper.insert(buildUser(request));
    }
}
```

### 2. 数据清理策略
```java
// 示例：删除用户时清理关联数据
@Service
public class UserService {
    
    @Transactional
    public void deleteUser(Long userId) {
        // 1. 删除用户角色关联
        userRoleService.deleteByUserId(userId);
        
        // 2. 删除用户组织关联
        userOrganizationService.deleteByUserId(userId);
        
        // 3. 删除用户扩展信息
        userProfileService.deleteByUserId(userId);
        
        // 4. 删除用户
        userMapper.deleteById(userId);
    }
}
```

### 3. 数据校验工具
```java
// 数据一致性检查工具
@Component
public class DataConsistencyChecker {
    
    public void checkUserDataConsistency() {
        // 检查孤立的用户角色关联
        List<UserRole> orphanUserRoles = userRoleMapper.findOrphanRecords();
        
        // 检查孤立的用户组织关联
        List<UserOrganization> orphanUserOrgs = userOrgMapper.findOrphanRecords();
        
        // 生成数据一致性报告
        generateConsistencyReport(orphanUserRoles, orphanUserOrgs);
    }
}
```

## 最佳实践

### 1. 命名规范
- 关联字段统一使用 `_id` 后缀
- 保持字段名与目标表主键名的一致性
- 在注释中明确说明关联关系

### 2. 索引策略
- 为所有关联字段创建索引
- 为常用查询组合创建复合索引
- 定期分析和优化索引使用情况

### 3. 数据验证
- 在业务层实现完整的数据验证逻辑
- 使用统一的异常处理机制
- 记录数据操作日志便于问题排查

### 4. 监控告警
- 监控数据一致性状态
- 设置数据异常告警机制
- 定期执行数据一致性检查

## 注意事项

### 1. 开发规范
- 严格按照业务逻辑维护数据关联
- 删除操作必须考虑级联影响
- 批量操作需要特别注意数据一致性

### 2. 测试要求
- 充分测试各种边界情况
- 验证数据一致性保障机制
- 模拟异常情况下的数据状态

### 3. 运维监控
- 定期检查数据一致性
- 监控系统性能指标
- 建立数据修复机制

## 总结

松耦合数据库设计虽然增加了应用层的复杂性，但带来了更高的性能、可用性和灵活性。通过合理的设计和严格的开发规范，可以在保证数据一致性的同时，获得更好的系统架构。
