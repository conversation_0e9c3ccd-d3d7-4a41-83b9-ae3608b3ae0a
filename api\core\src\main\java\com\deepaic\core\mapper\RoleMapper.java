package com.deepaic.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.entity.Role;
import com.deepaic.core.dto.RoleDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 查询角色列表（带分页）
     */
    IPage<RoleDTO> selectRolePage(Page<RoleDTO> page, @Param("query") RoleDTO.RoleQueryDTO query);

    /**
     * 查询所有角色列表
     */
    List<RoleDTO> selectRoleList(@Param("query") RoleDTO.RoleQueryDTO query);

    /**
     * 根据用户ID查询角色列表
     */
    List<RoleDTO> selectRoleListByUserId(@Param("userId") Long userId);

    /**
     * 检查角色名称是否唯一
     */
    Integer checkRoleNameUnique(@Param("roleName") String roleName, @Param("id") Long id);

    /**
     * 检查角色编码是否唯一
     */
    Integer checkRoleCodeUnique(@Param("roleCode") String roleCode, @Param("id") Long id);

    /**
     * 查询角色详情
     */
    RoleDTO selectRoleById(@Param("id") Long id);

    /**
     * 根据角色ID查询菜单ID列表
     */
    List<Long> selectMenuIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据角色ID查询部门ID列表
     */
    List<Long> selectDeptIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 统计角色使用数量
     */
    Integer countUsersByRoleId(@Param("roleId") Long roleId);
}
