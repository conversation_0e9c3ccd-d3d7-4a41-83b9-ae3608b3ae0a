# 美姿姿 应用配置模板
# 支持Java 21和Spring Boot 3.5.2

server:
  port: 8080
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  profiles:
    active: dev

  # 数据源配置
  datasource:
    druid:
      url: **************************************************
      username: postgres
      password: postgres123
      driver-class-name: org.postgresql.Driver
      
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      
      # 监控配置
      filters: stat,wall,slf4j
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin123

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.deepaic.*.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.deepaic: DEBUG
    org.springframework.security: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log
    max-size: 100MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  info:
    java:
      enabled: true
    os:
      enabled: true

# 应用信息
info:
  app:
    name: Beautiful Posture
    description: 美姿姿健康管理系统
    version: 0.0.1-SNAPSHOT
    java-version: 21

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    druid:
      url: **************************************************
      username: postgres
      password: postgres123
  redis:
    host: localhost
    port: 6379

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  datasource:
    druid:
      url: ************************************************
      username: test_user
      password: test_password
  redis:
    host: test-redis
    port: 6379

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    druid:
      url: ${DATABASE_URL:**************************************************}
      username: ${DATABASE_USERNAME:postgres}
      password: ${DATABASE_PASSWORD:postgres123}
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}

# 生产环境日志配置
logging:
  level:
    com.deepaic: INFO
    org.springframework.security: WARN
    com.baomidou.mybatisplus: WARN
  file:
    name: /var/log/beautiful-posture/application.log

---
# Docker环境配置
spring:
  config:
    activate:
      on-profile: docker
  datasource:
    druid:
      url: *************************************************
      username: postgres
      password: postgres123
  redis:
    host: redis
    port: 6379
