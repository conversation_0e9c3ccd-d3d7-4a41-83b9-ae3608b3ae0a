package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_room")
public class Room extends BaseEntity {

    private Long storeId;

    private String name;

    private String code;

    /**
     * 房型
     */
    private Short seatCount;

    /**
     * 1 启用| 0禁用
     */
    private Short status;

    private String remark;

    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_NORMAL = 1;   // 正常
}
