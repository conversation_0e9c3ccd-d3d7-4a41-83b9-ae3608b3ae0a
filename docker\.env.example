# 美姿姿 - Docker环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# ===========================================
# 基础配置
# ===========================================
VERSION=1.0.0
ENVIRONMENT=production
SPRING_PROFILES=production

# ===========================================
# 数据库配置
# ===========================================
# PostgreSQL配置
DATABASE_URL=********************************************************
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your-secure-password-here
POSTGRES_PASSWORD=your-secure-password-here
POSTGRES_PORT=5432

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MIN_IDLE=5

# ===========================================
# Redis配置
# ===========================================
REDIS_HOST=redis-server
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password-here
REDIS_DATABASE=0

# ===========================================
# 服务端口配置
# ===========================================
ADMIN_API_PORT=8080
APP_API_PORT=8081
CLIENT_API_PORT=8082

# 基础设施端口
HTTP_PORT=80
HTTPS_PORT=443
ES_PORT=9200
KIBANA_PORT=5601
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
REDIS_SENTINEL_PORT=26379

# ===========================================
# JVM配置
# ===========================================
JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
APP_LOG_LEVEL=DEBUG

# ===========================================
# 安全配置
# ===========================================
JWT_SECRET=your-jwt-secret-key-change-in-production-must-be-long-enough
JWT_EXPIRATION=86400

# ===========================================
# 多租户配置
# ===========================================
DEFAULT_TENANT_SCHEMA=public
TENANT_RESOLVER=header

# ===========================================
# 文件上传配置
# ===========================================
MAX_FILE_SIZE=10MB
MAX_REQUEST_SIZE=50MB

# ===========================================
# CORS配置（Client API）
# ===========================================
CORS_ORIGINS=http://localhost:3000,https://your-domain.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=*
CORS_CREDENTIALS=true

# ===========================================
# 移动端配置（App API）
# ===========================================
PUSH_ENABLED=true
PUSH_PROVIDER=firebase
OFFLINE_SYNC=true

# ===========================================
# 第三方集成配置（Client API）
# ===========================================
OAUTH_ENABLED=true
WEBHOOK_ENABLED=true
API_RATE_LIMIT=1000

# ===========================================
# 缓存配置
# ===========================================
CACHE_TYPE=redis
CACHE_TTL=3600
SESSION_TTL=7200

# ===========================================
# 监控配置
# ===========================================
GRAFANA_PASSWORD=your-grafana-password-here

# ===========================================
# 网络配置
# ===========================================
# 如果使用自定义网络，可以配置子网
NETWORK_SUBNET=**********/16

# ===========================================
# 生产环境特定配置
# ===========================================
# SSL证书路径（如果使用HTTPS）
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# 邮件配置（用于告警）
SMTP_HOST=smtp.your-domain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_FROM=<EMAIL>
