plugins {
    id 'java'
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
}

group = 'com.beautifulposture'
version = '1.0.0'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // 依赖核心模块和服务模块
    implementation project(':core')
    implementation project(':service')
    
    // Spring Boot Web
    implementation libs.spring.boot.starter.web
    implementation libs.spring.boot.starter.validation
    implementation libs.spring.boot.starter.actuator
    
    // 数据库相关
    implementation libs.mybatis.plus.spring.boot3.starter
    runtimeOnly libs.postgresql.connector

    // Sa-Token 权限认证
    implementation libs.sa.token.spring.boot3.starter
    implementation libs.sa.token.redis.jackson

    // Redis
    implementation libs.spring.boot.starter.data.redis

    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind'

    // 工具类
    implementation libs.hutool.all
    
    // Lombok
    compileOnly libs.lombok
    annotationProcessor libs.lombok

    // 测试依赖
    testImplementation libs.spring.boot.starter.test
    testRuntimeOnly libs.junit.platform.launcher
}

tasks.named('test') {
    useJUnitPlatform()
}

// 设置编码
tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

// 打包配置
jar {
    enabled = false
}

bootJar {
    archiveFileName = 'platform-api.jar'
}
