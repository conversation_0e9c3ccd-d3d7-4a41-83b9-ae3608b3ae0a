package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_menu")
public class Menu extends BaseEntity {

    private String menuName;

    private Long parentId;

    private Integer orderNum;

    private String path;

    private String component;

    private String queryParam;

    private Boolean isFrame;

    private Boolean isCache;

    private String menuType;

    private Boolean visible;

    private Short status;

    private String perms;

    private String icon;

    private String remark;

    // 菜单类型常量
    public static final short TYPE_DIRECTORY = 1;  // 目录
    public static final short TYPE_MENU = 2;       // 菜单
    public static final short TYPE_BUTTON = 3;     // 按钮

    // 状态常量
    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_ENABLED = 1;  // 启用

    // 可见性常量
    public static final boolean VISIBLE_HIDDEN = false;  // 隐藏
    public static final boolean VISIBLE_SHOW = true;    // 显示

    // 是否外链常量
    public static final boolean FRAME_NO = false;        // 否
    public static final boolean FRAME_YES = true;       // 是

    // 是否缓存常量
    public static final boolean CACHE_NO = false;        // 不缓存
    public static final boolean CACHE_YES = true;       // 缓存
}
