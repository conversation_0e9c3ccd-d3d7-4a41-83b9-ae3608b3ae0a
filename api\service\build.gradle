// Service模块通常也不需要Spring Boot启动器，只提供服务
plugins {
    id 'java-library'  // 使用java-library插件
}

// Service 模块特定配置 - 业务逻辑层
description = 'Beautiful Posture Service Module - Business logic layer'

dependencies {
    // 依赖core模块 - core模块已经包含了MyBatis-Plus和Spring相关依赖
    api project(':core')

    // 缓存相关
    api libs.spring.boot.starter.cache
    api libs.redisson.spring.boot.starter

    // 消息队列（如果需要）
    // api libs.spring.kafka
    // api libs.spring.boot.starter.amqp
}

// JAR打包配置
jar {
    archiveFileName = 'beautiful-posture-service.jar'

    // 设置重复文件处理策略
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE

    // 设置MANIFEST
    manifest {
        attributes(
            'Implementation-Title': project.name,
            'Implementation-Version': project.version,
            'Implementation-Vendor': 'Beautiful Posture Team'
        )
    }
}
