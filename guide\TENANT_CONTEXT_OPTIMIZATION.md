# 租户上下文设置优化

## 优化概述

根据三种不同用户登录类型的需求，对 `TenantContextService.setTenantContextForUser` 方法进行了全面优化，现在支持平台用户、客户用户和会员用户三种登录场景。

## 优化前的问题

原来的 `setTenantContextForUser` 方法只处理了租户用户登录，存在以下问题：

1. **单一用户类型**: 只支持客户用户登录
2. **硬编码逻辑**: 假设所有用户都需要租户上下文
3. **平台用户无法处理**: 平台用户不需要租户上下文，但原方法无法处理
4. **会员用户缺失**: 没有考虑小程序会员用户的特殊需求

## 优化后的架构

### 1. 用户类型枚举

```java
public enum UserType {
    PLATFORM_USER,  // 平台用户
    CUSTOMER_USER,  // 客户用户  
    MEMBER_USER     // 会员用户
}
```

### 2. 统一入口方法

```java
public void setTenantContextForUser(Long accountId, UserType userType) throws BussinessException
```

### 3. 三种专门的处理方法

#### 🏢 平台用户上下文设置 (setPlatformUserContext)

```java
private void setPlatformUserContext(Long accountId) throws BussinessException
```

**特点**:
- 查询 `pub_platform_account` 表
- 设置 schema 为 `public`
- tenantCode 为 `null`（表示平台用户）
- tenantName 为 "SaaS平台"
- userId 使用 accountId

**适用场景**: SaaS平台管理员登录

#### 🏢 客户用户上下文设置 (setCustomerUserContext)

```java
private void setCustomerUserContext(Long accountId) throws BussinessException
```

**特点**:
- 查询 `pub_user_account` 表
- 根据 tenantCode 查询租户信息
- 设置对应的租户 schema
- userId 使用关联的 sys_user_id
- 完整的租户信息设置

**适用场景**: 客户系统用户登录

#### 📱 会员用户上下文设置 (setMemberUserContext)

```java
private void setMemberUserContext(Long accountId) throws BussinessException
```

**特点**:
- 查询 `pub_member_account` 表
- 根据 tenantCode 查询租户信息
- 设置对应的租户 schema
- userId 使用关联的 member_id
- username 使用 "会员用户_" + accountId 格式

**适用场景**: 小程序会员登录

## 使用方式

### 在认证服务中的使用

#### 平台用户认证服务
```java
@Service
public class PlatformAuthService implements AuthenticationService {
    
    @Override
    public AuthResponse authenticate(AuthRequest request) throws BussinessException {
        // ... 认证逻辑
        
        // 设置平台用户上下文
        tenantContextService.setTenantContextForUser(
            account.getId(), 
            TenantContextService.UserType.PLATFORM_USER
        );
        
        // ... 后续处理
    }
}
```

#### 客户用户认证服务
```java
@Service
public class CustomerAuthService implements AuthenticationService {
    
    @Override
    public AuthResponse authenticate(AuthRequest request) throws BussinessException {
        // ... 认证逻辑
        
        // 设置客户用户租户上下文
        tenantContextService.setTenantContextForUser(
            account.getId(), 
            TenantContextService.UserType.CUSTOMER_USER
        );
        
        // ... 后续处理
    }
}
```

#### 会员用户认证服务
```java
@Service
public class MemberAuthService implements AuthenticationService {
    
    @Override
    public AuthResponse authenticate(AuthRequest request) throws BussinessException {
        // ... 认证逻辑
        
        // 设置会员用户租户上下文
        tenantContextService.setTenantContextForUser(
            account.getId(), 
            TenantContextService.UserType.MEMBER_USER
        );
        
        // ... 后续处理
    }
}
```

## 租户上下文设置对比

| 用户类型 | 账户表 | Schema设置 | TenantCode | UserId | Username |
|---------|--------|-----------|-----------|---------|----------|
| 平台用户 | pub_platform_account | public | null | accountId | account.username |
| 客户用户 | pub_user_account | tenant_xxx | tenant_code | sys_user_id | account.username |
| 会员用户 | pub_member_account | tenant_xxx | tenant_code | member_id | "会员用户_" + accountId |

## 错误处理

### 统一异常处理
- 账户不存在异常
- 租户信息缺失异常
- 租户被禁用异常
- Schema信息缺失异常

### 监控记录
- 成功设置的监控记录
- 失败设置的监控记录
- 用户类型和租户信息记录

## 向后兼容

### 废弃方法保留
```java
@Deprecated
public void setTenantContextForUser(Long accountId)
```

- 保留原有方法签名
- 默认按客户用户类型处理
- 添加废弃警告日志
- 建议使用新方法

## 优化效果

### ✅ 已解决的问题
1. **多用户类型支持**: 支持平台用户、客户用户、会员用户
2. **差异化处理**: 根据用户类型设置不同的租户上下文
3. **平台用户支持**: 正确处理不需要租户上下文的平台用户
4. **会员用户支持**: 专门处理小程序会员的特殊需求
5. **错误处理完善**: 统一的异常处理和监控记录

### 🚀 技术优势
- **类型安全**: 使用枚举避免字符串错误
- **职责分离**: 每种用户类型有专门的处理方法
- **扩展性强**: 易于添加新的用户类型
- **监控完善**: 完整的成功/失败监控记录
- **向后兼容**: 保留原有方法，平滑迁移

### 📊 使用统计

| 用户类型 | 处理方法 | Schema | 租户信息 | 监控标识 |
|---------|---------|--------|---------|----------|
| PLATFORM_USER | setPlatformUserContext | public | 无 | "platform" |
| CUSTOMER_USER | setCustomerUserContext | tenant_xxx | 完整 | tenant_code |
| MEMBER_USER | setMemberUserContext | tenant_xxx | 完整 | tenant_code |

## 总结

通过这次优化，`TenantContextService.setTenantContextForUser` 方法现在能够：

1. **正确处理三种用户类型**的不同需求
2. **自动设置合适的租户上下文**
3. **提供完善的错误处理和监控**
4. **保持向后兼容性**
5. **支持未来扩展**

这为美姿姿健康管理系统的多租户认证提供了坚实的基础，确保了不同用户类型都能获得正确的租户上下文设置！🎯
