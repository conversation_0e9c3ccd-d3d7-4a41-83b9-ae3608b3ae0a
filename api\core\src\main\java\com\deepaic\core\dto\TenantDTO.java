package com.deepaic.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 租户数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class TenantDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户代码
     */
    @NotBlank(message = "租户代码不能为空")
    @Size(min = 2, max = 50, message = "租户代码长度必须在2-50个字符之间")
    private String tenantCode;

    /**
     * 租户名称
     */
    @NotBlank(message = "租户名称不能为空")
    @Size(min = 2, max = 100, message = "租户名称长度必须在2-100个字符之间")
    private String tenantName;

    /**
     * 租户简称
     */
    @Size(max = 50, message = "租户简称长度不能超过50个字符")
    private String tenantShortName;

    /**
     * 数据库Schema名称
     */
    @NotBlank(message = "Schema名称不能为空")
    @Size(min = 2, max = 63, message = "Schema名称长度必须在2-63个字符之间")
    private String schemaName;

    /**
     * 租户类型
     */
    private Integer tenantType;

    /**
     * 租户状态
     */
    private Integer status;

    /**
     * 联系人姓名
     */
    @Size(max = 50, message = "联系人姓名长度不能超过50个字符")
    private String contactName;

    /**
     * 联系人电话
     */
    @Size(max = 20, message = "联系人电话长度不能超过20个字符")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @Size(max = 100, message = "联系人邮箱长度不能超过100个字符")
    private String contactEmail;

    /**
     * 公司地址
     */
    @Size(max = 200, message = "公司地址长度不能超过200个字符")
    private String address;

    /**
     * 公司网站
     */
    @Size(max = 200, message = "公司网站长度不能超过200个字符")
    private String website;

    /**
     * 租户Logo URL
     */
    @Size(max = 500, message = "Logo URL长度不能超过500个字符")
    private String logoUrl;

    /**
     * 最大用户数限制
     */
    private Integer maxUsers;

    /**
     * 当前用户数
     */
    private Integer currentUsers;

    /**
     * 存储空间限制(MB)
     */
    private Long storageLimit;

    /**
     * 已使用存储空间(MB)
     */
    private Long storageUsed;

    /**
     * 服务开始时间
     */
    private LocalDateTime serviceStartTime;

    /**
     * 服务结束时间
     */
    private LocalDateTime serviceEndTime;

    /**
     * 试用期结束时间
     */
    private LocalDateTime trialEndTime;

    /**
     * 自定义域名
     */
    @Size(max = 100, message = "自定义域名长度不能超过100个字符")
    private String customDomain;

    /**
     * 配置信息
     */
    private String configJson;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    private Integer version;

    // 计算字段
    /**
     * 存储使用率
     */
    private Double storageUsagePercentage;

    /**
     * 用户使用率
     */
    private Double userUsagePercentage;

    /**
     * 是否过期
     */
    private Boolean expired;

    /**
     * 是否在试用期
     */
    private Boolean inTrial;
}


