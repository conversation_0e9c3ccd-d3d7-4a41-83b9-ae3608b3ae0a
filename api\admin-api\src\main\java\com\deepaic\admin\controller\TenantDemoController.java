package com.deepaic.admin.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.deepaic.core.tenant.SaTokenTenantContext;
import com.deepaic.core.tenant.TenantUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 多租户演示Controller
 * 展示多租户schema自动切换功能
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/tenant-demo")
public class TenantDemoController {

    /**
     * 获取当前租户信息
     * 演示自动租户上下文获取
     */
    @GetMapping("/current-tenant")
    @SaCheckLogin
    public Map<String, Object> getCurrentTenant() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取租户信息
        SaTokenTenantContext.TenantInfo tenant = SaTokenTenantContext.getTenantInfo();
        if (tenant != null) {
            result.put("tenantCode", tenant.getTenantCode());
            result.put("tenantName", tenant.getTenantName());
            result.put("schemaName", tenant.getSchemaName());
            result.put("userId", tenant.getUserId());
            result.put("username", tenant.getUsername());
        } else {
            result.put("message", "未找到租户上下文");
        }
        
        // 使用工具类获取信息
        result.put("currentTenantCode", TenantUtils.getCurrentTenantCode());
        result.put("currentSchema", TenantUtils.getCurrentTenantSchema());
        result.put("currentUserId", TenantUtils.getCurrentUserId());
        result.put("currentUsername", TenantUtils.getCurrentUsername());
        result.put("hasValidContext", TenantUtils.hasValidTenantContext());
        result.put("contextSummary", TenantUtils.getContextSummary());
        
        log.info("获取租户信息: {}", TenantUtils.getContextSummary());
        
        return result;
    }

    /**
     * 测试在不同租户上下文中执行操作
     */
    @GetMapping("/test-context-switch")
    @SaCheckLogin
    public Map<String, Object> testContextSwitch() {
        Map<String, Object> result = new HashMap<>();
        
        // 记录当前上下文
        String originalContext = TenantUtils.getContextSummary();
        result.put("originalContext", originalContext);
        
        // 在公共Schema中执行操作
        TenantUtils.runInPublicSchema(() -> {
            String publicContext = TenantUtils.getContextSummary();
            result.put("publicSchemaContext", publicContext);
            log.info("在公共Schema中执行: {}", publicContext);
        });
        
        // 在指定租户中执行操作
        TenantUtils.runInTenant("demo", "tenant_demo", () -> {
            String demoContext = TenantUtils.getContextSummary();
            result.put("demoTenantContext", demoContext);
            log.info("在演示租户中执行: {}", demoContext);
        });
        
        // 验证上下文已恢复
        String restoredContext = TenantUtils.getContextSummary();
        result.put("restoredContext", restoredContext);
        result.put("contextRestored", originalContext.equals(restoredContext));
        
        log.info("上下文切换测试完成: 原始={}, 恢复={}", originalContext, restoredContext);
        
        return result;
    }

    /**
     * 获取系统状态信息
     * 不需要登录，用于测试公共API
     */
    @GetMapping("/system-status")
    public Map<String, Object> getSystemStatus() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("isUserLoggedIn", TenantUtils.isUserLoggedIn());
        result.put("hasValidTenantContext", TenantUtils.hasValidTenantContext());
        result.put("currentSchema", TenantUtils.getCurrentTenantSchema());
        result.put("contextSummary", TenantUtils.getContextSummary());
        
        log.info("系统状态查询: {}", TenantUtils.getContextSummary());
        
        return result;
    }
}
