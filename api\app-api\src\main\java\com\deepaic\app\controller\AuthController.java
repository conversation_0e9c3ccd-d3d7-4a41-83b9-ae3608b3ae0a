package com.deepaic.app.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.deepaic.core.auth.AuthenticationManager;
import com.deepaic.core.auth.models.AuthRequest;
import com.deepaic.core.auth.models.AuthResponse;
import com.deepaic.core.auth.models.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * 小程序会员认证控制器
 * 处理小程序会员用户的登录、登出等认证操作
 * 支持微信授权登录
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/app/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final AuthenticationManager authenticationManager;

    /**
     * 微信小程序登录
     * 使用微信小程序授权码进行登录
     */
    @PostMapping("/wechat-mini-login")
    public Map<String, Object> wechatMiniLogin(@RequestBody @Valid WechatMiniLoginRequest request, 
                                             HttpServletRequest httpRequest) {
        log.info("小程序会员微信登录请求: tenantCode={}", request.getTenantCode());
        
        // 创建会员微信认证请求
        AuthRequest authRequest = AuthRequest.memberWechatLogin(request.getCode(), AuthRequest.PLATFORM_WECHAT_MINI);
        authRequest.setTenantCode(request.getTenantCode()); // 设置租户代码
        
        // 执行认证
        AuthResponse authResponse = authenticationManager.authenticate(authRequest, httpRequest);
        
        Map<String, Object> response = new HashMap<>();
        if (authResponse.isSuccess()) {
            response.put("code", 200);
            response.put("message", "登录成功");
            
            Map<String, Object> data = new HashMap<>();
            data.put("accessToken", authResponse.getAccessToken());
            data.put("expiresIn", authResponse.getExpiresIn());
            data.put("userInfo", authResponse.getUserPrincipal());
            response.put("data", data);
            
            log.info("小程序会员微信登录成功: tenantCode={}", request.getTenantCode());
        } else {
            response.put("code", authResponse.getErrorCode());
            response.put("message", authResponse.getMessage());
            response.put("data", null);
            
            log.warn("小程序会员微信登录失败: tenantCode={}, error={}", 
                    request.getTenantCode(), authResponse.getMessage());
        }
        
        return response;
    }

    /**
     * 手机号登录
     * 使用手机号和短信验证码进行登录
     */
    @PostMapping("/phone-login")
    public Map<String, Object> phoneLogin(@RequestBody @Valid PhoneLoginRequest request, 
                                        HttpServletRequest httpRequest) {
        log.info("小程序会员手机号登录请求: phone={}, tenantCode={}", request.getPhone(), request.getTenantCode());
        
        // 创建会员手机号认证请求
        AuthRequest authRequest = AuthRequest.memberPhoneLogin(
            request.getPhone(), 
            request.getSmsCode(), 
            AuthRequest.PLATFORM_WECHAT_MINI
        );
        authRequest.setTenantCode(request.getTenantCode()); // 设置租户代码
        
        // 执行认证
        AuthResponse authResponse = authenticationManager.authenticate(authRequest, httpRequest);
        
        Map<String, Object> response = new HashMap<>();
        if (authResponse.isSuccess()) {
            response.put("code", 200);
            response.put("message", "登录成功");
            
            Map<String, Object> data = new HashMap<>();
            data.put("accessToken", authResponse.getAccessToken());
            data.put("expiresIn", authResponse.getExpiresIn());
            data.put("userInfo", authResponse.getUserPrincipal());
            response.put("data", data);
            
            log.info("小程序会员手机号登录成功: phone={}, tenantCode={}", request.getPhone(), request.getTenantCode());
        } else {
            response.put("code", authResponse.getErrorCode());
            response.put("message", authResponse.getMessage());
            response.put("data", null);
            
            log.warn("小程序会员手机号登录失败: phone={}, tenantCode={}, error={}", 
                    request.getPhone(), request.getTenantCode(), authResponse.getMessage());
        }
        
        return response;
    }

    /**
     * 会员登出
     */
    @PostMapping("/logout")
    @SaCheckLogin
    public Map<String, Object> logout() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取当前用户信息
            String loginId = StpUtil.getLoginId().toString();
            
            // 执行登出
            StpUtil.logout();
            
            response.put("code", 200);
            response.put("message", "登出成功");
            response.put("data", null);
            
            log.info("小程序会员登出成功: loginId={}", loginId);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "登出失败: " + e.getMessage());
            response.put("data", null);
            
            log.error("小程序会员登出失败", e);
        }
        
        return response;
    }

    /**
     * 获取当前登录会员信息
     */
    @GetMapping("/current-member")
    @SaCheckLogin
    public Map<String, Object> getCurrentMember() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从Session中获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) StpUtil.getSession().get("userPrincipal");
            
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", userPrincipal);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "获取会员信息失败: " + e.getMessage());
            response.put("data", null);
            
            log.error("获取当前会员信息失败", e);
        }
        
        return response;
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/check-login")
    public Map<String, Object> checkLogin() {
        Map<String, Object> response = new HashMap<>();
        
        boolean isLogin = StpUtil.isLogin();
        response.put("code", 200);
        response.put("message", "检查完成");
        
        Map<String, Object> data = new HashMap<>();
        data.put("isLogin", isLogin);
        if (isLogin) {
            data.put("loginId", StpUtil.getLoginId());
            data.put("tokenValue", StpUtil.getTokenValue());
        }
        response.put("data", data);
        
        return response;
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh-token")
    public Map<String, Object> refreshToken(@RequestBody RefreshTokenRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 会员用户类型为MEMBER_ACCOUNT
            AuthResponse authResponse = authenticationManager.refreshToken(
                request.getRefreshToken(),
                UserPrincipal.UserType.MEMBER_ACCOUNT
            );

            if (authResponse.isSuccess()) {
                response.put("code", 200);
                response.put("message", "刷新成功");

                Map<String, Object> data = new HashMap<>();
                data.put("accessToken", authResponse.getAccessToken());
                data.put("expiresIn", authResponse.getExpiresIn());
                response.put("data", data);
            } else {
                response.put("code", authResponse.getErrorCode());
                response.put("message", authResponse.getMessage());
                response.put("data", null);
            }
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "刷新令牌失败: " + e.getMessage());
            response.put("data", null);

            log.error("刷新令牌失败", e);
        }

        return response;
    }

    /**
     * 获取登录信息
     */
    @GetMapping("/login-info")
    @SaCheckLogin
    public Map<String, Object> getLoginInfo() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("loginId", StpUtil.getLoginId());
            data.put("tokenValue", StpUtil.getTokenValue());
            data.put("tokenTimeout", StpUtil.getTokenTimeout());
            data.put("sessionTimeout", StpUtil.getSessionTimeout());
            
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", data);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "获取登录信息失败: " + e.getMessage());
            response.put("data", null);
            
            log.error("获取登录信息失败", e);
        }
        
        return response;
    }

    /**
     * 微信小程序登录请求DTO
     */
    public static class WechatMiniLoginRequest {
        @NotBlank(message = "微信授权码不能为空")
        private String code;
        
        private String tenantCode; // 租户代码，可选
        
        private String referrerNo; // 推荐人会员编号，可选

        // Getters and Setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        public String getTenantCode() { return tenantCode; }
        public void setTenantCode(String tenantCode) { this.tenantCode = tenantCode; }
        public String getReferrerNo() { return referrerNo; }
        public void setReferrerNo(String referrerNo) { this.referrerNo = referrerNo; }
    }

    /**
     * 手机号登录请求DTO
     */
    public static class PhoneLoginRequest {
        @NotBlank(message = "手机号不能为空")
        private String phone;
        
        @NotBlank(message = "短信验证码不能为空")
        private String smsCode;
        
        private String tenantCode; // 租户代码，可选
        
        private String referrerNo; // 推荐人会员编号，可选

        // Getters and Setters
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getSmsCode() { return smsCode; }
        public void setSmsCode(String smsCode) { this.smsCode = smsCode; }
        public String getTenantCode() { return tenantCode; }
        public void setTenantCode(String tenantCode) { this.tenantCode = tenantCode; }
        public String getReferrerNo() { return referrerNo; }
        public void setReferrerNo(String referrerNo) { this.referrerNo = referrerNo; }
    }

    /**
     * 刷新令牌请求DTO
     */
    public static class RefreshTokenRequest {
        @NotBlank(message = "刷新令牌不能为空")
        private String refreshToken;

        // Getters and Setters
        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }
    }
}
