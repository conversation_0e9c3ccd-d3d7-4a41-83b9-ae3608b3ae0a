# 数据库结构优化调整

## 概述

根据实际业务需求，对数据库表结构进行了以下优化调整：

## 主要变更

### 1. 移除岗位相关功能

**原因：** 岗位的实际作用不大，简化系统结构

**变更内容：**
- 删除 `sys_position` 表
- 从 `sys_user` 表中移除 `org_id` 和 `position_id` 字段
- 从 `sys_user_organization` 表中移除 `position_id` 字段
- 在 `sys_user_organization` 表中新增 `job_title` 字段，用于记录职务名称

### 2. 权限表重命名

**原因：** 表名更简洁，便于使用

**变更内容：**
- 将 `sys_data_permission` 表重命名为 `sys_permission`
- 将 `sys_role_data_permission` 表重命名为 `sys_role_permission`
- 扩展权限类型，新增功能权限类型

### 3. 优化多组织支持

**原因：** 用户确实存在所属多个组织机构的情况

**变更内容：**
- 优化 `sys_user_organization` 表结构
- 新增 `uk_user_org` 唯一约束，防止用户在同一组织重复关联
- 保留 `uk_user_org_primary` 约束，确保每个用户只有一个主组织
- 新增 `job_title` 字段，记录用户在该组织的职务名称

### 4. 组织类型调整

**原因：** 移除岗位表后，组织类型需要相应调整

**变更内容：**
- 组织类型从 `1:公司 2:部门 3:小组 4:岗位 5:虚拟组织` 调整为 `1:公司 2:部门 3:小组 4:虚拟组织`

## 表结构变更详情

### sys_user 表变更
```sql
-- 移除字段
-- org_id BIGINT, -- 所属组织ID (已移除)
-- position_id BIGINT, -- 岗位ID (已移除)

-- 保留字段
employee_no VARCHAR(50), -- 员工编号
entry_date DATE, -- 入职日期
account_id BIGINT, -- 关联的登录账户ID
```

### sys_user_organization 表变更
```sql
-- 新增字段
job_title VARCHAR(100), -- 职务名称

-- 新增约束
CONSTRAINT uk_user_org UNIQUE (user_id, org_id), -- 防止重复关联

-- 移除字段
-- position_id BIGINT, -- 岗位ID (已移除)
```

### sys_permission 表 (原 sys_data_permission)
```sql
-- 表名变更
-- sys_data_permission -> sys_permission

-- 权限类型扩展
permission_type INTEGER NOT NULL, -- 1:组织权限 2:数据权限 3:字段权限 4:功能权限
```

### sys_role_permission 表 (原 sys_role_data_permission)
```sql
-- 表名变更
-- sys_role_data_permission -> sys_role_permission

-- 字段名变更
-- data_permission_id -> permission_id
```

### pub_customer_account 表变更
```sql
-- 新增字段
login_type INTEGER NOT NULL DEFAULT 1, -- 登录类型
login_identifier VARCHAR(200) NOT NULL, -- 登录标识
wechat_openid VARCHAR(100), -- 微信openid
wechat_unionid VARCHAR(100), -- 微信unionid
bind_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 绑定时间

-- 字段调整
username VARCHAR(50), -- 改为可选
password VARCHAR(100), -- 改为可选

-- 约束变更
-- uk_tenant_username -> uk_tenant_login
CONSTRAINT uk_tenant_login UNIQUE (tenant_code, login_type, login_identifier)
```

## 影响分析

### 正面影响
1. **简化系统结构**：移除不必要的岗位功能，降低系统复杂度
2. **提高灵活性**：支持用户属于多个组织，更符合实际业务场景
3. **优化命名**：权限表名更简洁，便于开发和维护
4. **扩展权限类型**：支持更多类型的权限控制

### 需要注意的事项
1. **代码调整**：需要更新相关的实体类、Mapper、Service 等代码
2. **数据迁移**：如果已有数据，需要编写数据迁移脚本
3. **权限逻辑**：需要调整权限验证逻辑，适应新的表结构
4. **用户界面**：需要更新用户管理相关的前端界面

## 后续工作

1. 更新实体类和相关代码
2. 调整权限验证逻辑
3. 更新API接口
4. 修改前端界面
5. 编写数据迁移脚本（如需要）
6. 更新相关文档

### 5. 租户用户微信登录支持

**原因：** 增强租户用户登录方式，支持微信扫码登录

**变更内容：**
- 在 `pub_customer_account` 表中新增 `login_type` 字段，支持多种登录方式
- 新增 `login_identifier` 字段作为统一的登录标识
- 新增 `wechat_openid` 和 `wechat_unionid` 字段支持微信登录
- 新增 `bind_time` 字段记录账户绑定时间
- 调整 `username` 和 `password` 字段为可选
- 更新唯一约束为 `uk_tenant_login (tenant_code, login_type, login_identifier)`

### 6. 明确松耦合设计原则

**原因：** 确保数据库设计遵循松耦合原则，提高系统灵活性

**变更内容：**
- 在SQL脚本开头明确说明不使用外键约束的设计原则
- 更新所有关联字段的注释，明确标注为"逻辑关联（无外键约束）"
- 为所有关联表添加详细的设计说明注释

### 7. 优化字段约束设置

**原因：** 提高数据质量和系统稳定性，明确字段的必要性

**变更内容：**
- 将必要的字段设置为 NOT NULL（主键、状态、时间戳、业务必需字段等）
- 保持非必要字段为可选（用户输入、扩展信息、备注等）
- 为所有有默认值的字段明确设置 NOT NULL 约束
- 优化所有表的字段约束，包括公共表和租户表

## 文件变更

- `sql/init/03_create_tables.sql` - 数据库表结构创建脚本
- `guide/tenant-user-wechat-login.md` - 租户用户微信登录设计文档
- `guide/database-design-principles.md` - 数据库松耦合设计原则文档
- `guide/application-layer-data-integrity.md` - 应用层数据完整性维护指南
- `guide/database-field-constraints.md` - 数据库字段约束优化指南
