-- =====================================================
-- 美姿姿健康管理系统 - 索引创建脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-20
-- 作者: 美姿姿团队
-- 说明: 创建性能优化索引
-- 数据库连接: *****************************************************/mzz
-- =====================================================

-- 确保在正确的数据库中执行
\c mzz

-- 设置客户端编码和时区
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- 开始事务
BEGIN;

-- ==============================================
-- 公共表索引
-- ==============================================

-- 租户表索引
CREATE INDEX IF NOT EXISTS idx_pub_tenant_code ON public.pub_tenant(tenant_code);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_schema ON public.pub_tenant(schema_name);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_status ON public.pub_tenant(status);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_type ON public.pub_tenant(tenant_type);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_expire ON public.pub_tenant(expire_time);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_created ON public.pub_tenant(created_at);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_deleted ON public.pub_tenant(deleted) WHERE deleted = FALSE;

-- 平台用户表索引
CREATE INDEX IF NOT EXISTS idx_pub_platform_username ON public.pub_platform_account(username);
CREATE INDEX IF NOT EXISTS idx_pub_platform_email ON public.pub_platform_account(email);
CREATE INDEX IF NOT EXISTS idx_pub_platform_phone ON public.pub_platform_account(phone);
CREATE INDEX IF NOT EXISTS idx_pub_platform_status ON public.pub_platform_account(status);
CREATE INDEX IF NOT EXISTS idx_pub_platform_permission ON public.pub_platform_account(permission_level);
CREATE INDEX IF NOT EXISTS idx_pub_platform_last_login ON public.pub_platform_account(last_login_time);
CREATE INDEX IF NOT EXISTS idx_pub_platform_deleted ON public.pub_platform_account(deleted) WHERE deleted = FALSE;

-- 用户账户表索引
CREATE INDEX IF NOT EXISTS idx_pub_user_tenant ON public.pub_user_account(tenant_code);
CREATE INDEX IF NOT EXISTS idx_pub_user_username ON public.pub_user_account(username);
CREATE INDEX IF NOT EXISTS idx_pub_user_email ON public.pub_user_account(email);
CREATE INDEX IF NOT EXISTS idx_pub_user_phone ON public.pub_user_account(phone);
CREATE INDEX IF NOT EXISTS idx_pub_user_status ON public.pub_user_account(status);
CREATE INDEX IF NOT EXISTS idx_pub_user_type ON public.pub_user_account(account_type);
CREATE INDEX IF NOT EXISTS idx_pub_user_last_login ON public.pub_user_account(last_login_time);
CREATE INDEX IF NOT EXISTS idx_pub_user_deleted ON public.pub_user_account(deleted) WHERE deleted = FALSE;
CREATE INDEX IF NOT EXISTS idx_pub_user_login_type ON public.pub_user_account(login_type);
CREATE INDEX IF NOT EXISTS idx_pub_user_identifier ON public.pub_user_account(login_identifier);
CREATE INDEX IF NOT EXISTS idx_pub_user_wechat_openid ON public.pub_user_account(wechat_openid);
-- 复合索引
CREATE INDEX IF NOT EXISTS idx_pub_user_tenant_username ON public.pub_user_account(tenant_code, username);
CREATE INDEX IF NOT EXISTS idx_pub_user_tenant_status ON public.pub_user_account(tenant_code, status);
CREATE INDEX IF NOT EXISTS idx_pub_user_tenant_type ON public.pub_user_account(tenant_code, login_type);
-- 新增关联索引
CREATE INDEX IF NOT EXISTS idx_pub_user_sys_user ON public.pub_user_account(sys_user_id);
CREATE INDEX IF NOT EXISTS idx_pub_user_primary ON public.pub_user_account(is_primary);

-- 会员账户表索引
CREATE INDEX IF NOT EXISTS idx_pub_member_tenant ON public.pub_member_account(tenant_code);
CREATE INDEX IF NOT EXISTS idx_pub_member_member_id ON public.pub_member_account(member_id);
CREATE INDEX IF NOT EXISTS idx_pub_member_wechat_openid ON public.pub_member_account(wechat_open_id);
CREATE INDEX IF NOT EXISTS idx_pub_member_last_login ON public.pub_member_account(last_login_time);
CREATE INDEX IF NOT EXISTS idx_pub_member_deleted ON public.pub_member_account(deleted) WHERE deleted = FALSE;
-- 复合索引
CREATE INDEX IF NOT EXISTS idx_pub_member_tenant_member ON public.pub_member_account(tenant_code, member_id);
CREATE INDEX IF NOT EXISTS idx_pub_member_tenant_wechat ON public.pub_member_account(tenant_code, wechat_open_id);

-- ==============================================
-- 租户表索引创建函数
-- ==============================================

-- 创建租户索引的函数
CREATE OR REPLACE FUNCTION public.create_tenant_indexes(tenant_schema_name VARCHAR(100))
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    start_time TIMESTAMP := NOW();
    index_count INTEGER := 0;
BEGIN
    -- 验证schema名称
    IF tenant_schema_name IS NULL OR LENGTH(tenant_schema_name) < 3 THEN
        RAISE EXCEPTION '租户Schema名称无效: %', tenant_schema_name;
    END IF;

    -- 检查schema是否存在
    IF NOT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = tenant_schema_name) THEN
        RAISE EXCEPTION 'Schema % 不存在', tenant_schema_name;
    END IF;

    RAISE NOTICE '开始在Schema % 中创建索引...', tenant_schema_name;

    -- 用户表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_username ON %I.sys_user(username)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_email ON %I.sys_user(email)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_phone ON %I.sys_user(phone)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_real_name ON %I.sys_user(real_name)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_status ON %I.sys_user(status)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_last_login ON %I.sys_user(last_login_time)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_created ON %I.sys_user(created_at)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_deleted ON %I.sys_user(deleted) WHERE deleted = FALSE', tenant_schema_name);
    index_count := index_count + 8;

    -- 角色表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_role_name ON %I.sys_role(role_name)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_role_code ON %I.sys_role(role_code)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_role_status ON %I.sys_role(status)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_role_created ON %I.sys_role(created_at)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_role_deleted ON %I.sys_role(deleted) WHERE deleted = FALSE', tenant_schema_name);
    index_count := index_count + 5;

    -- 菜单表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_menu_name ON %I.sys_menu(menu_name)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_menu_parent ON %I.sys_menu(parent_id)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_menu_type ON %I.sys_menu(menu_type)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_menu_status ON %I.sys_menu(status)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_menu_deleted ON %I.sys_menu(deleted) WHERE deleted = FALSE', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_menu_parent_sort ON %I.sys_menu(parent_id, sort_order)', tenant_schema_name);
    index_count := index_count + 6;

    -- 权限表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_permission_name ON %I.sys_permission(permission_name)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_permission_code ON %I.sys_permission(permission_code)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_permission_type ON %I.sys_permission(permission_type)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_permission_parent ON %I.sys_permission(parent_id)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_permission_status ON %I.sys_permission(status)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_permission_deleted ON %I.sys_permission(deleted) WHERE deleted = FALSE', tenant_schema_name);
    index_count := index_count + 6;

    -- 用户角色关联表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_role_user ON %I.sys_user_role(user_id)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_role_role ON %I.sys_user_role(role_id)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_user_role_created ON %I.sys_user_role(created_at)', tenant_schema_name);
    index_count := index_count + 3;

    -- 角色权限关联表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_role_permission_role ON %I.sys_role_permission(role_id)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_role_permission_permission ON %I.sys_role_permission(permission_id)', tenant_schema_name);
    index_count := index_count + 2;

    -- 角色菜单关联表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_role_menu_role ON %I.sys_role_menu(role_id)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_role_menu_menu ON %I.sys_role_menu(menu_id)', tenant_schema_name);
    index_count := index_count + 2;

    -- 会员表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_member_no ON %I.sys_member(member_no)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_member_name ON %I.sys_member(name)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_member_phone ON %I.sys_member(phone)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_member_wechat_openid ON %I.sys_member(wechat_openid)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_member_status ON %I.sys_member(status)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_member_register_time ON %I.sys_member(register_time)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_member_deleted ON %I.sys_member(deleted) WHERE deleted = FALSE', tenant_schema_name);
    index_count := index_count + 7;

    -- 门店表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_store_code ON %I.sys_store(store_code)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_store_name ON %I.sys_store(store_name)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_store_status ON %I.sys_store(status)', tenant_schema_name);
    index_count := index_count + 3;

    -- 产品表索引
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_product_code ON %I.sys_product(product_code)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_product_name ON %I.sys_product(product_name)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_product_category ON %I.sys_product(category_id)', tenant_schema_name);
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_sys_product_status ON %I.sys_product(status)', tenant_schema_name);
    index_count := index_count + 4;

    -- 记录执行结果
    RAISE NOTICE 'Schema % 中成功创建 % 个索引，耗时: %',
                 tenant_schema_name, index_count, NOW() - start_time;

    RETURN TRUE;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION '在Schema % 中创建索引失败: %', tenant_schema_name, SQLERRM;
END $$;

-- 为示例租户创建索引
SELECT public.create_tenant_indexes('tenant_demo');

-- ==============================================
-- 全文搜索索引
-- ==============================================

-- 租户名称全文搜索
CREATE INDEX IF NOT EXISTS idx_pub_tenant_name_gin ON public.pub_tenant USING gin(to_tsvector('chinese', tenant_name));

-- ==============================================
-- 审计表索引
-- ==============================================

-- 切换到审计Schema
SET search_path TO audit, public;

-- Schema操作审计表索引（如果表存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'audit' AND table_name = 'schema_operations') THEN
        CREATE INDEX IF NOT EXISTS idx_audit_schema_tenant ON audit.schema_operations(tenant_code);
        CREATE INDEX IF NOT EXISTS idx_audit_schema_executed_at ON audit.schema_operations(executed_at);
        CREATE INDEX IF NOT EXISTS idx_audit_schema_operation_type ON audit.schema_operations(operation_type);
        CREATE INDEX IF NOT EXISTS idx_audit_schema_executed_by ON audit.schema_operations(executed_by);
    END IF;
END $$;

-- 恢复搜索路径
SET search_path TO public, system_admin, audit, reporting;

-- 提交事务
COMMIT;

-- 记录脚本执行
INSERT INTO public.schema_version (version, description, executed_at) 
VALUES ('1.0.0', '创建性能优化索引', NOW())
ON CONFLICT (version) DO UPDATE SET 
    executed_at = NOW(),
    description = EXCLUDED.description;

RAISE NOTICE '==============================================';
RAISE NOTICE '索引创建脚本执行完成';
RAISE NOTICE '已创建索引类型:';
RAISE NOTICE '- 单列索引：提升单字段查询性能';
RAISE NOTICE '- 复合索引：提升多字段组合查询性能';
RAISE NOTICE '- 部分索引：针对特定条件的优化索引';
RAISE NOTICE '- 全文搜索索引：支持中文全文搜索';
RAISE NOTICE '- 关联表索引：提升JOIN查询性能';
RAISE NOTICE '==============================================';

-- 显示索引统计信息
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname IN ('public', 'tenant_demo', 'audit')
    AND indexname NOT LIKE '%_pkey'  -- 排除主键索引
ORDER BY schemaname, tablename, indexname;
