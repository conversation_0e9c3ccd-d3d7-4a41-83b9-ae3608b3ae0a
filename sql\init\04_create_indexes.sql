-- =====================================================
-- 美姿姿健康管理系统 - 索引创建脚本
-- 版本: 1.0.0
-- 创建时间: 2025-06-27
-- 作者: 美姿姿团队
-- 说明: 创建性能优化索引
-- =====================================================

-- 确保在正确的数据库中执行
\c beautiful_posture

-- 设置客户端编码和时区
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- 开始事务
BEGIN;

-- ==============================================
-- 公共表索引
-- ==============================================

-- 租户表索引
CREATE INDEX IF NOT EXISTS idx_pub_tenant_code ON public.pub_tenant(tenant_code);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_schema ON public.pub_tenant(schema_name);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_status ON public.pub_tenant(status);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_type ON public.pub_tenant(tenant_type);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_expire ON public.pub_tenant(expire_time);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_created ON public.pub_tenant(created_at);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_deleted ON public.pub_tenant(deleted) WHERE deleted = FALSE;

-- 平台用户表索引
CREATE INDEX IF NOT EXISTS idx_pub_platform_username ON public.pub_platform_account(username);
CREATE INDEX IF NOT EXISTS idx_pub_platform_email ON public.pub_platform_account(email);
CREATE INDEX IF NOT EXISTS idx_pub_platform_phone ON public.pub_platform_account(phone);
CREATE INDEX IF NOT EXISTS idx_pub_platform_status ON public.pub_platform_account(status);
CREATE INDEX IF NOT EXISTS idx_pub_platform_permission ON public.pub_platform_account(permission_level);
CREATE INDEX IF NOT EXISTS idx_pub_platform_last_login ON public.pub_platform_account(last_login_time);
CREATE INDEX IF NOT EXISTS idx_pub_platform_deleted ON public.pub_platform_account(deleted) WHERE deleted = FALSE;

-- 租户用户表索引
CREATE INDEX IF NOT EXISTS idx_pub_customer_tenant ON public.pub_customer_account(tenant_code);
CREATE INDEX IF NOT EXISTS idx_pub_customer_username ON public.pub_customer_account(username);
CREATE INDEX IF NOT EXISTS idx_pub_customer_email ON public.pub_customer_account(email);
CREATE INDEX IF NOT EXISTS idx_pub_customer_phone ON public.pub_customer_account(phone);
CREATE INDEX IF NOT EXISTS idx_pub_customer_status ON public.pub_customer_account(status);
CREATE INDEX IF NOT EXISTS idx_pub_customer_type ON public.pub_customer_account(account_type);
CREATE INDEX IF NOT EXISTS idx_pub_customer_last_login ON public.pub_customer_account(last_login_time);
CREATE INDEX IF NOT EXISTS idx_pub_customer_deleted ON public.pub_customer_account(deleted) WHERE deleted = FALSE;
-- 复合索引
CREATE INDEX IF NOT EXISTS idx_pub_customer_tenant_username ON public.pub_customer_account(tenant_code, username);
CREATE INDEX IF NOT EXISTS idx_pub_customer_tenant_status ON public.pub_customer_account(tenant_code, status);
-- 新增关联索引
CREATE INDEX IF NOT EXISTS idx_pub_customer_sys_user ON public.pub_customer_account(sys_user_id);
CREATE INDEX IF NOT EXISTS idx_pub_customer_primary ON public.pub_customer_account(is_primary);

-- 会员账户表索引
CREATE INDEX IF NOT EXISTS idx_pub_member_tenant ON public.pub_member_account(tenant_code);
CREATE INDEX IF NOT EXISTS idx_pub_member_member_id ON public.pub_member_account(member_id);
CREATE INDEX IF NOT EXISTS idx_pub_member_login_type ON public.pub_member_account(login_type);
CREATE INDEX IF NOT EXISTS idx_pub_member_identifier ON public.pub_member_account(login_identifier);
CREATE INDEX IF NOT EXISTS idx_pub_member_status ON public.pub_member_account(status);
CREATE INDEX IF NOT EXISTS idx_pub_member_last_login ON public.pub_member_account(last_login_time);
CREATE INDEX IF NOT EXISTS idx_pub_member_deleted ON public.pub_member_account(deleted) WHERE deleted = FALSE;
-- 复合索引
CREATE INDEX IF NOT EXISTS idx_pub_member_tenant_type ON public.pub_member_account(tenant_code, login_type);
CREATE INDEX IF NOT EXISTS idx_pub_member_tenant_member ON public.pub_member_account(tenant_code, member_id);
-- 新增关联索引
CREATE INDEX IF NOT EXISTS idx_pub_member_user ON public.pub_member_account(member_user_id);

-- ==============================================
-- 租户表索引模板（在tenant_demo schema中创建）
-- ==============================================

-- 切换到示例租户Schema
SET search_path TO tenant_demo, public;

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_sys_user_code ON sys_user(user_code);
CREATE INDEX IF NOT EXISTS idx_sys_user_username ON sys_user(username);
CREATE INDEX IF NOT EXISTS idx_sys_user_email ON sys_user(email);
CREATE INDEX IF NOT EXISTS idx_sys_user_phone ON sys_user(phone);
CREATE INDEX IF NOT EXISTS idx_sys_user_real_name ON sys_user(real_name);
CREATE INDEX IF NOT EXISTS idx_sys_user_org ON sys_user(org_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_position ON sys_user(position_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_employee_no ON sys_user(employee_no);
CREATE INDEX IF NOT EXISTS idx_sys_user_account ON sys_user(account_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_status ON sys_user(status);
CREATE INDEX IF NOT EXISTS idx_sys_user_last_login ON sys_user(last_login_time);
CREATE INDEX IF NOT EXISTS idx_sys_user_created ON sys_user(created_at);
CREATE INDEX IF NOT EXISTS idx_sys_user_deleted ON sys_user(deleted) WHERE deleted = FALSE;

-- 角色表索引
CREATE INDEX IF NOT EXISTS idx_sys_role_name ON sys_role(role_name);
CREATE INDEX IF NOT EXISTS idx_sys_role_code ON sys_role(role_code);
CREATE INDEX IF NOT EXISTS idx_sys_role_sort ON sys_role(role_sort);
CREATE INDEX IF NOT EXISTS idx_sys_role_status ON sys_role(status);
CREATE INDEX IF NOT EXISTS idx_sys_role_data_scope ON sys_role(data_scope);
CREATE INDEX IF NOT EXISTS idx_sys_role_created ON sys_role(created_at);
CREATE INDEX IF NOT EXISTS idx_sys_role_deleted ON sys_role(deleted) WHERE deleted = FALSE;

-- 菜单表索引
CREATE INDEX IF NOT EXISTS idx_sys_menu_name ON sys_menu(menu_name);
CREATE INDEX IF NOT EXISTS idx_sys_menu_parent ON sys_menu(parent_id);
CREATE INDEX IF NOT EXISTS idx_sys_menu_order ON sys_menu(order_num);
CREATE INDEX IF NOT EXISTS idx_sys_menu_path ON sys_menu(path);
CREATE INDEX IF NOT EXISTS idx_sys_menu_type ON sys_menu(menu_type);
CREATE INDEX IF NOT EXISTS idx_sys_menu_status ON sys_menu(status);
CREATE INDEX IF NOT EXISTS idx_sys_menu_visible ON sys_menu(visible);
CREATE INDEX IF NOT EXISTS idx_sys_menu_perms ON sys_menu(perms);
CREATE INDEX IF NOT EXISTS idx_sys_menu_deleted ON sys_menu(deleted) WHERE deleted = FALSE;
-- 复合索引
CREATE INDEX IF NOT EXISTS idx_sys_menu_parent_order ON sys_menu(parent_id, order_num);
CREATE INDEX IF NOT EXISTS idx_sys_menu_type_status ON sys_menu(menu_type, status);

-- 组织机构表索引
CREATE INDEX IF NOT EXISTS idx_sys_org_code ON sys_organization(org_code);
CREATE INDEX IF NOT EXISTS idx_sys_org_name ON sys_organization(org_name);
CREATE INDEX IF NOT EXISTS idx_sys_org_parent ON sys_organization(parent_id);
CREATE INDEX IF NOT EXISTS idx_sys_org_ancestors ON sys_organization(ancestors);
CREATE INDEX IF NOT EXISTS idx_sys_org_type ON sys_organization(org_type);
CREATE INDEX IF NOT EXISTS idx_sys_org_level ON sys_organization(org_level);
CREATE INDEX IF NOT EXISTS idx_sys_org_leader ON sys_organization(leader_id);
CREATE INDEX IF NOT EXISTS idx_sys_org_status ON sys_organization(status);
CREATE INDEX IF NOT EXISTS idx_sys_org_created ON sys_organization(created_at);
CREATE INDEX IF NOT EXISTS idx_sys_org_deleted ON sys_organization(deleted) WHERE deleted = FALSE;
-- 复合索引
CREATE INDEX IF NOT EXISTS idx_sys_org_parent_type ON sys_organization(parent_id, org_type);
CREATE INDEX IF NOT EXISTS idx_sys_org_type_status ON sys_organization(org_type, status);

-- 岗位表索引
CREATE INDEX IF NOT EXISTS idx_sys_position_code ON sys_position(position_code);
CREATE INDEX IF NOT EXISTS idx_sys_position_name ON sys_position(position_name);
CREATE INDEX IF NOT EXISTS idx_sys_position_org ON sys_position(org_id);
CREATE INDEX IF NOT EXISTS idx_sys_position_level ON sys_position(position_level);
CREATE INDEX IF NOT EXISTS idx_sys_position_category ON sys_position(position_category);
CREATE INDEX IF NOT EXISTS idx_sys_position_status ON sys_position(status);
CREATE INDEX IF NOT EXISTS idx_sys_position_created ON sys_position(created_at);
CREATE INDEX IF NOT EXISTS idx_sys_position_deleted ON sys_position(deleted) WHERE deleted = FALSE;

-- 用户组织关联表索引
CREATE INDEX IF NOT EXISTS idx_sys_user_org_user ON sys_user_organization(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_org_org ON sys_user_organization(org_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_org_position ON sys_user_organization(position_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_org_primary ON sys_user_organization(is_primary);
CREATE INDEX IF NOT EXISTS idx_sys_user_org_leader ON sys_user_organization(is_leader);
CREATE INDEX IF NOT EXISTS idx_sys_user_org_status ON sys_user_organization(status);
-- 复合索引
CREATE INDEX IF NOT EXISTS idx_sys_user_org_user_primary ON sys_user_organization(user_id, is_primary);
CREATE INDEX IF NOT EXISTS idx_sys_user_org_org_status ON sys_user_organization(org_id, status);

-- 数据权限表索引
CREATE INDEX IF NOT EXISTS idx_sys_data_perm_code ON sys_data_permission(permission_code);
CREATE INDEX IF NOT EXISTS idx_sys_data_perm_name ON sys_data_permission(permission_name);
CREATE INDEX IF NOT EXISTS idx_sys_data_perm_type ON sys_data_permission(permission_type);
CREATE INDEX IF NOT EXISTS idx_sys_data_perm_resource ON sys_data_permission(resource_type);
CREATE INDEX IF NOT EXISTS idx_sys_data_perm_scope ON sys_data_permission(scope_type);
CREATE INDEX IF NOT EXISTS idx_sys_data_perm_status ON sys_data_permission(status);
CREATE INDEX IF NOT EXISTS idx_sys_data_perm_deleted ON sys_data_permission(deleted) WHERE deleted = FALSE;

-- 角色数据权限关联表索引
CREATE INDEX IF NOT EXISTS idx_sys_role_data_perm_role ON sys_role_data_permission(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_data_perm_data ON sys_role_data_permission(data_permission_id);

-- 用户扩展信息表索引
CREATE INDEX IF NOT EXISTS idx_sys_user_profile_user ON sys_user_profile(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_profile_id_card ON sys_user_profile(id_card);
CREATE INDEX IF NOT EXISTS idx_sys_user_profile_education ON sys_user_profile(education);

-- 用户角色关联表索引
CREATE INDEX IF NOT EXISTS idx_sys_user_role_user ON sys_user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_role_role ON sys_user_role(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_role_created ON sys_user_role(created_at);

-- 角色菜单关联表索引
CREATE INDEX IF NOT EXISTS idx_sys_role_menu_role ON sys_role_menu(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_menu_menu ON sys_role_menu(menu_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_menu_created ON sys_role_menu(created_at);

-- 角色组织关联表索引
CREATE INDEX IF NOT EXISTS idx_sys_role_org_role ON sys_role_organization(role_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_org_org ON sys_role_organization(org_id);
CREATE INDEX IF NOT EXISTS idx_sys_role_org_created ON sys_role_organization(created_at);

-- 会员表索引
CREATE INDEX IF NOT EXISTS idx_sys_member_no ON sys_member(member_no);
CREATE INDEX IF NOT EXISTS idx_sys_member_nickname ON sys_member(nickname);
CREATE INDEX IF NOT EXISTS idx_sys_member_real_name ON sys_member(real_name);
CREATE INDEX IF NOT EXISTS idx_sys_member_phone ON sys_member(phone);
CREATE INDEX IF NOT EXISTS idx_sys_member_email ON sys_member(email);
CREATE INDEX IF NOT EXISTS idx_sys_member_wechat_openid ON sys_member(wechat_openid);
CREATE INDEX IF NOT EXISTS idx_sys_member_wechat_unionid ON sys_member(wechat_unionid);
CREATE INDEX IF NOT EXISTS idx_sys_member_level ON sys_member(member_level);
CREATE INDEX IF NOT EXISTS idx_sys_member_status ON sys_member(status);
CREATE INDEX IF NOT EXISTS idx_sys_member_last_login ON sys_member(last_login_time);
CREATE INDEX IF NOT EXISTS idx_sys_member_register_source ON sys_member(register_source);
CREATE INDEX IF NOT EXISTS idx_sys_member_created ON sys_member(created_at);
CREATE INDEX IF NOT EXISTS idx_sys_member_deleted ON sys_member(deleted) WHERE deleted = FALSE;

-- ==============================================
-- 全文搜索索引
-- ==============================================

-- 租户名称全文搜索
CREATE INDEX IF NOT EXISTS idx_pub_tenant_name_gin ON public.pub_tenant USING gin(to_tsvector('chinese', tenant_name));

-- 用户姓名全文搜索
CREATE INDEX IF NOT EXISTS idx_sys_user_name_gin ON sys_user USING gin(to_tsvector('chinese', real_name));

-- 部门名称全文搜索
CREATE INDEX IF NOT EXISTS idx_sys_dept_name_gin ON sys_department USING gin(to_tsvector('chinese', dept_name));

-- 会员姓名全文搜索
CREATE INDEX IF NOT EXISTS idx_sys_member_name_gin ON sys_member USING gin(to_tsvector('chinese', real_name));

-- ==============================================
-- 审计表索引
-- ==============================================

-- 切换到审计Schema
SET search_path TO audit, public;

-- Schema操作审计表索引（如果表存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'audit' AND table_name = 'schema_operations') THEN
        CREATE INDEX IF NOT EXISTS idx_audit_schema_tenant ON audit.schema_operations(tenant_code);
        CREATE INDEX IF NOT EXISTS idx_audit_schema_executed_at ON audit.schema_operations(executed_at);
        CREATE INDEX IF NOT EXISTS idx_audit_schema_operation_type ON audit.schema_operations(operation_type);
        CREATE INDEX IF NOT EXISTS idx_audit_schema_executed_by ON audit.schema_operations(executed_by);
    END IF;
END $$;

-- 恢复搜索路径
SET search_path TO public, system_admin, audit, reporting;

-- 提交事务
COMMIT;

-- 记录脚本执行
INSERT INTO public.schema_version (version, description, executed_at) 
VALUES ('1.0.0', '创建性能优化索引', NOW())
ON CONFLICT (version) DO UPDATE SET 
    executed_at = NOW(),
    description = EXCLUDED.description;

RAISE NOTICE '==============================================';
RAISE NOTICE '索引创建脚本执行完成';
RAISE NOTICE '已创建索引类型:';
RAISE NOTICE '- 单列索引：提升单字段查询性能';
RAISE NOTICE '- 复合索引：提升多字段组合查询性能';
RAISE NOTICE '- 部分索引：针对特定条件的优化索引';
RAISE NOTICE '- 全文搜索索引：支持中文全文搜索';
RAISE NOTICE '- 关联表索引：提升JOIN查询性能';
RAISE NOTICE '==============================================';

-- 显示索引统计信息
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname IN ('public', 'tenant_demo', 'audit')
    AND indexname NOT LIKE '%_pkey'  -- 排除主键索引
ORDER BY schemaname, tablename, indexname;
