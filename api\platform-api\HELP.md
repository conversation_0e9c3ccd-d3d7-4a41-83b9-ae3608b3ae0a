# Platform API - SaaS平台管理API

## 概述

Platform API 是美姿姿SaaS平台的管理API模块，负责整个平台的管理功能。

## 主要功能

### 1. 租户管理
- 租户注册和审核
- 租户信息管理
- 租户状态控制
- 租户配额管理

### 2. 平台用户管理
- 平台管理员账号管理
- 权限分配
- 操作日志记录

### 3. 系统配置
- 平台参数配置
- 系统功能开关
- 版本管理

### 4. 数据统计
- 租户使用统计
- 系统性能监控
- 业务数据分析

## 技术特点

- **端口**: 8080
- **数据库**: 主要操作public schema的公共表
- **认证**: Sa-Token会话管理
- **权限**: 平台级权限控制

## 数据库表

### 主要操作的表
- `pub_platform_account`: 平台用户账号
- `pub_tenant`: 租户信息
- `pub_customer_account`: 客户账号(只读)
- `pub_member_account`: 会员账号映射(只读)

## API接口

### 健康检查
```
GET /platform-api/platform/health
```

### 平台信息
```
GET /platform-api/platform/info
```

## 配置说明

### 应用配置
- 服务端口: 8080
- 上下文路径: /platform-api
- 数据库schema: public

### 环境变量
- `DB_USERNAME`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `REDIS_HOST`: Redis主机地址
- `REDIS_PORT`: Redis端口
- `REDIS_PASSWORD`: Redis密码

## 部署方式

### Docker部署
```bash
# 单独部署platform-api
docker-compose -f docker/docker-compose.platform-api.yml up -d

# 完整部署
docker-compose -f docker/docker-compose.full.yml up -d
```

### 传统部署
```bash
java -jar platform-api.jar
```

## 开发指南

### 添加新功能
1. 在 `com.beautifulposture.platform.controller` 包下创建控制器
2. 在 `service` 模块中实现业务逻辑
3. 在 `core` 模块中定义实体类

### 权限控制
```java
@RestController
@RequestMapping("/platform/tenant")
public class TenantController {
    
    @GetMapping("/list")
    @SaCheckRole("platform-admin")
    public Result<List<Tenant>> list() {
        // 实现逻辑
    }
}
```

### 数据访问
```java
@Service
public class TenantService {
    
    @Autowired
    private TenantMapper tenantMapper;
    
    public List<Tenant> getAllTenants() {
        // 直接操作public schema的表
        return tenantMapper.selectList(null);
    }
}
```

## 注意事项

1. **数据隔离**: Platform API主要操作public schema，不需要租户切换
2. **权限控制**: 使用平台级权限，权限级别最高
3. **安全性**: 平台管理功能，需要严格的安全控制
4. **监控**: 重要操作需要记录操作日志
5. **性能**: 统计查询可能涉及大量数据，注意性能优化

## 相关文档

- [模块架构指南](../guide/MODULE_ARCHITECTURE_GUIDE.md)
- [多租户架构指南](../guide/MULTITENANT_ARCHITECTURE_GUIDE.md)
- [Docker部署指南](../guide/DOCKER_DEPLOYMENT_GUIDE.md)
