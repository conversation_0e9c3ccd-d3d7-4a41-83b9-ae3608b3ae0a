-- =====================================================
-- 美姿姿健康管理系统 - 初始化完成脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-20
-- 作者: 美姿姿团队
-- 说明: 系统初始化完成验证和标记
-- 数据库连接: *****************************************************/mzz
-- =====================================================

-- 确保在正确的数据库中执行
\c mzz

-- 设置客户端编码和时区
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- ==============================================
-- 初始化验证
-- ==============================================

DO $$
DECLARE
    schema_count INTEGER;
    table_count INTEGER;
    index_count INTEGER;
    data_count INTEGER;
    error_count INTEGER := 0;
    error_messages TEXT := '';
BEGIN
    RAISE NOTICE '==============================================';
    RAISE NOTICE '开始验证系统初始化状态...';
    RAISE NOTICE '==============================================';
    
    -- 1. 验证Schema
    SELECT COUNT(*) INTO schema_count
    FROM information_schema.schemata 
    WHERE schema_name IN ('public', 'system_admin', 'audit', 'reporting', 'temp_data', 'tenant_demo');
    
    IF schema_count < 6 THEN
        error_count := error_count + 1;
        error_messages := error_messages || format('Schema数量不足，期望6个，实际%个; ', schema_count);
    ELSE
        RAISE NOTICE '✅ Schema验证通过: %个', schema_count;
    END IF;
    
    -- 2. 验证公共表
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN ('pub_tenant', 'pub_platform_account', 'pub_user_account', 'pub_member_account', 'schema_version');
    
    IF table_count < 5 THEN
        error_count := error_count + 1;
        error_messages := error_messages || format('公共表数量不足，期望5个，实际%个; ', table_count);
    ELSE
        RAISE NOTICE '✅ 公共表验证通过: %个', table_count;
    END IF;
    
    -- 3. 验证租户表模板
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'tenant_demo'
    AND table_name IN ('sys_user', 'sys_role', 'sys_menu', 'sys_permission', 'sys_member', 'sys_user_role', 'sys_role_menu', 'sys_role_permission', 'sys_store', 'sys_product');
    
    IF table_count < 10 THEN
        error_count := error_count + 1;
        error_messages := error_messages || format('租户表模板数量不足，期望10个，实际%个; ', table_count);
    ELSE
        RAISE NOTICE '✅ 租户表模板验证通过: %个', table_count;
    END IF;
    
    -- 4. 验证索引
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE schemaname IN ('public', 'tenant_demo')
    AND indexname NOT LIKE '%_pkey';
    
    IF index_count < 20 THEN
        error_count := error_count + 1;
        error_messages := error_messages || format('索引数量不足，期望至少20个，实际%个; ', index_count);
    ELSE
        RAISE NOTICE '✅ 索引验证通过: %个', index_count;
    END IF;
    
    -- 5. 验证初始数据
    SELECT COUNT(*) INTO data_count FROM public.pub_platform_account;
    IF data_count < 1 THEN
        error_count := error_count + 1;
        error_messages := error_messages || '平台用户数据缺失; ';
    END IF;
    
    SELECT COUNT(*) INTO data_count FROM public.pub_tenant;
    IF data_count < 1 THEN
        error_count := error_count + 1;
        error_messages := error_messages || '租户数据缺失; ';
    END IF;
    
    SELECT COUNT(*) INTO data_count FROM tenant_demo.sys_user;
    IF data_count < 1 THEN
        error_count := error_count + 1;
        error_messages := error_messages || '示例用户数据缺失; ';
    END IF;
    
    SELECT COUNT(*) INTO data_count FROM tenant_demo.sys_menu;
    IF data_count < 10 THEN
        error_count := error_count + 1;
        error_messages := error_messages || '菜单数据不足; ';
    END IF;
    
    IF error_count = 0 THEN
        RAISE NOTICE '✅ 初始数据验证通过';
    ELSE
        RAISE NOTICE '❌ 初始数据验证失败: %', error_messages;
    END IF;
    
    -- 6. 验证函数
    SELECT COUNT(*) INTO data_count
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_name IN ('create_tenant_schema', 'drop_tenant_schema', 'create_tenant_tables', 'create_tenant_indexes');
    
    IF data_count < 4 THEN
        error_count := error_count + 1;
        error_messages := error_messages || '租户管理函数缺失; ';
    ELSE
        RAISE NOTICE '✅ 函数验证通过: %个', data_count;
    END IF;
    
    -- 总结验证结果
    RAISE NOTICE '==============================================';
    IF error_count = 0 THEN
        RAISE NOTICE '🎉 系统初始化验证完全通过！';
    ELSE
        RAISE NOTICE '❌ 发现 % 个问题: %', error_count, error_messages;
        RAISE EXCEPTION '系统初始化验证失败，请检查错误信息';
    END IF;
    RAISE NOTICE '==============================================';
END $$;

-- ==============================================
-- 创建系统状态表
-- ==============================================

CREATE TABLE IF NOT EXISTS public.system_status (
    id SERIAL PRIMARY KEY,
    status_key VARCHAR(50) NOT NULL UNIQUE,
    status_value TEXT,
    status_type VARCHAR(20) DEFAULT 'string',
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE public.system_status IS '系统状态信息表';

-- 插入初始化完成标记
INSERT INTO public.system_status (status_key, status_value, status_type, description) VALUES
('system.initialized', 'true', 'boolean', '系统是否已完成初始化'),
('system.init_time', NOW()::text, 'timestamp', '系统初始化完成时间'),
('system.init_version', '1.0.0', 'string', '初始化时的系统版本'),
('database.schema_version', '1.0.0', 'string', '数据库Schema版本'),
('database.last_migration', '1.0.0', 'string', '最后执行的迁移版本')
ON CONFLICT (status_key) DO UPDATE SET
    status_value = EXCLUDED.status_value,
    updated_at = NOW();

-- ==============================================
-- 创建系统信息视图
-- ==============================================

CREATE OR REPLACE VIEW public.system_info AS
SELECT 
    'beautiful_posture' as database_name,
    current_database() as current_db,
    version() as postgresql_version,
    current_user as current_user,
    NOW() as current_time,
    (SELECT status_value FROM public.system_status WHERE status_key = 'system.init_time') as init_time,
    (SELECT status_value FROM public.system_status WHERE status_key = 'system.init_version') as init_version,
    (SELECT COUNT(*) FROM public.pub_tenant) as tenant_count,
    (SELECT COUNT(*) FROM public.pub_platform_account) as platform_user_count,
    (SELECT COUNT(*) FROM public.pub_customer_account) as customer_user_count,
    (SELECT COUNT(*) FROM public.pub_member_account) as member_account_count;

COMMENT ON VIEW public.system_info IS '系统信息概览视图';

-- ==============================================
-- 创建健康检查函数
-- ==============================================

CREATE OR REPLACE FUNCTION public.health_check()
RETURNS JSON
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
    result JSON;
    db_size TEXT;
    connection_count INTEGER;
BEGIN
    -- 获取数据库大小
    SELECT pg_size_pretty(pg_database_size(current_database())) INTO db_size;
    
    -- 获取连接数
    SELECT COUNT(*) INTO connection_count
    FROM pg_stat_activity 
    WHERE datname = current_database();
    
    -- 构建结果
    SELECT json_build_object(
        'status', 'healthy',
        'timestamp', NOW(),
        'database', json_build_object(
            'name', current_database(),
            'size', db_size,
            'connections', connection_count,
            'version', version()
        ),
        'system', json_build_object(
            'initialized', (SELECT status_value FROM public.system_status WHERE status_key = 'system.initialized'),
            'init_time', (SELECT status_value FROM public.system_status WHERE status_key = 'system.init_time'),
            'version', (SELECT status_value FROM public.system_status WHERE status_key = 'system.init_version')
        ),
        'statistics', json_build_object(
            'tenants', (SELECT COUNT(*) FROM public.pub_tenant),
            'platform_users', (SELECT COUNT(*) FROM public.pub_platform_account),
            'customer_users', (SELECT COUNT(*) FROM public.pub_customer_account),
            'member_accounts', (SELECT COUNT(*) FROM public.pub_member_account)
        )
    ) INTO result;
    
    RETURN result;
END $$;

-- ==============================================
-- 权限设置
-- ==============================================

-- 授予权限
GRANT SELECT ON public.system_status TO beautiful_posture_user, beautiful_posture_readonly;
GRANT SELECT ON public.system_info TO beautiful_posture_user, beautiful_posture_readonly;
GRANT EXECUTE ON FUNCTION public.health_check() TO beautiful_posture_user, beautiful_posture_readonly;

-- ==============================================
-- 最终记录和报告
-- ==============================================

-- 记录初始化完成
INSERT INTO public.schema_version (version, description, executed_at) 
VALUES ('1.0.0', '系统初始化完成', NOW())
ON CONFLICT (version) DO UPDATE SET 
    executed_at = NOW(),
    description = EXCLUDED.description;

-- 显示系统信息
RAISE NOTICE '==============================================';
RAISE NOTICE '🎉 美姿姿健康管理系统初始化完成！';
RAISE NOTICE '==============================================';
RAISE NOTICE '系统信息:';
SELECT * FROM public.system_info;

RAISE NOTICE '==============================================';
RAISE NOTICE '健康检查:';
SELECT public.health_check();

RAISE NOTICE '==============================================';
RAISE NOTICE '默认登录信息:';
RAISE NOTICE '平台管理员:';
RAISE NOTICE '  用户名: admin';
RAISE NOTICE '  密码: admin123';
RAISE NOTICE '  权限: 超级管理员';
RAISE NOTICE '';
RAISE NOTICE '租户管理员 (demo租户):';
RAISE NOTICE '  用户名: demo_admin';
RAISE NOTICE '  密码: admin123';
RAISE NOTICE '  权限: 租户管理员';
RAISE NOTICE '==============================================';
RAISE NOTICE '下一步操作:';
RAISE NOTICE '1. 启动应用服务';
RAISE NOTICE '2. 访问管理后台';
RAISE NOTICE '3. 修改默认密码';
RAISE NOTICE '4. 配置系统参数';
RAISE NOTICE '5. 创建正式租户';
RAISE NOTICE '==============================================';
RAISE NOTICE '技术支持: 美姿姿团队';
RAISE NOTICE '官网: https://www.deepaic.com';
RAISE NOTICE '==============================================';

-- 显示版本历史
SELECT 
    version,
    description,
    executed_at,
    CASE 
        WHEN executed_at > NOW() - INTERVAL '1 minute' THEN '刚刚完成'
        ELSE '已完成'
    END as status
FROM public.schema_version 
ORDER BY executed_at DESC;
