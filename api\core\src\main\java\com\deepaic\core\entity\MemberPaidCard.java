package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <p>
 * 会员权益-储值卡
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_paid_card")
public class MemberPaidCard extends BaseEntity {

    private Long memberId;

    private Long rechargeId;

    private Long cardId;

    private String cardCode;

    private String cardName;

    private BigDecimal actualPrice;

    private BigDecimal salePrice;

    private BigDecimal projectDiscount;

    private BigDecimal productDiscount;

    /**
     * 1=未用完，2=已用完
     */
    private Short status;
}
