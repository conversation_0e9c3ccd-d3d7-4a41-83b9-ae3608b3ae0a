package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_store")
public class Store extends BaseEntity {

    private String code;

    private String name;

    private Short level;

    private String phone;

    private String address;

    private Short status;

    private String remark;

    // 状态常量
    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_NORMAL = 1;   // 正常
}
