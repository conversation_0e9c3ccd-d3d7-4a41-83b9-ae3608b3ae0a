package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_coupon")
public class Coupon extends BaseEntity {

    private String code;

    private String name;

    /**
     * 1现金券，2优惠券
     */
    private Short type;

    /**
     * 面值
     */
    private BigDecimal facePrice;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 销售开始日期
     */
    private LocalDate saleStartDate;

    /**
     * 销售结束日期
     */
    private LocalDate saleEndDate;

    /**
     * 有效期开始日期
     */
    private LocalDate validStartDate;

    /**
     * 有效期结束日期
     */
    private LocalDate validEndDate;

    /**
     * 是否单次使用限制
     */
    private Boolean isSingleUseLimit;

    /**
     * 单次使用限制个数
     */
    private Integer singleUseLimit;

    /**
     * 是否有门槛
     */
    private Boolean isUsageThreshold;

    /**
     * 使用门槛
     */
    private BigDecimal usageThreshold;

    /**
     * 1上架，0下架
     */
    private Short status;

    // 状态常量
    public static final int STATUS_DISABLED = 0; // 禁用
    public static final int STATUS_NORMAL = 1;   // 正常
}
