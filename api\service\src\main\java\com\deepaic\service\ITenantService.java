package com.deepaic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.deepaic.core.entity.Tenant;
import com.deepaic.core.dto.TenantDTO;
import com.deepaic.core.dto.TenantQueryDTO;

import java.util.List;

/**
 * 租户服务接口
 * 
 * <AUTHOR>
 */
public interface ITenantService extends IService<Tenant> {

    /**
     * 创建租户
     */
    Long createTenant(TenantDTO tenantDTO);

    /**
     * 更新租户
     */
    boolean updateTenant(Long id, TenantDTO tenantDTO);

    /**
     * 根据ID查询租户详情
     */
    TenantDTO getTenantById(Long id);

    /**
     * 根据租户代码查询租户
     */
    Tenant getTenantByCode(String tenantCode);

    /**
     * 根据Schema名称查询租户
     */
    Tenant getTenantBySchemaName(String schemaName);

    /**
     * 分页查询租户列表
     */
    IPage<TenantDTO> getTenantPage(Page<TenantDTO> page, TenantQueryDTO query);

    /**
     * 查询所有启用的租户
     */
    List<Tenant> getEnabledTenants();

    /**
     * 删除租户
     */
    boolean deleteTenant(Long id);

    /**
     * 批量删除租户
     */
    boolean deleteTenants(List<Long> ids);

    /**
     * 启用租户
     */
    boolean enableTenant(Long id);

    /**
     * 禁用租户
     */
    boolean disableTenant(Long id);

    /**
     * 暂停租户
     */
    boolean suspendTenant(Long id);

    /**
     * 设置租户过期
     */
    boolean expireTenant(Long id);

    /**
     * 检查租户代码是否存在
     */
    boolean existsByTenantCode(String tenantCode);

    /**
     * 检查Schema名称是否存在
     */
    boolean existsBySchemaName(String schemaName);

    /**
     * 检查租户名称是否存在
     */
    boolean existsByTenantName(String tenantName);

    /**
     * 统计各状态的租户数量
     */
    List<Object> countByStatus();

    /**
     * 统计各类型的租户数量
     */
    List<Object> countByType();

    /**
     * 查询即将到期的租户
     */
    List<Tenant> getExpiringSoon(int days);

    /**
     * 查询试用期即将结束的租户
     */
    List<Tenant> getTrialExpiringSoon(int days);

    /**
     * 更新租户用户数量
     */
    boolean updateUserCount(String tenantCode, Integer userCount);

    /**
     * 更新租户存储使用量
     */
    boolean updateStorageUsed(String tenantCode, Long storageUsed);

    /**
     * 查询租户统计信息
     */
    Object getTenantStats();

    /**
     * 查询超出用户限制的租户
     */
    List<Tenant> getUserLimitExceededTenants();

    /**
     * 查询超出存储限制的租户
     */
    List<Tenant> getStorageLimitExceededTenants();

    /**
     * 根据自定义域名查询租户
     */
    Tenant getTenantByCustomDomain(String domain);

    /**
     * 初始化租户数据库Schema
     */
    boolean initializeTenantSchema(String tenantCode);

    /**
     * 删除租户数据库Schema
     */
    boolean dropTenantSchema(String tenantCode);

    /**
     * 检查租户是否可以创建新用户
     */
    boolean canCreateUser(String tenantCode);

    /**
     * 检查租户存储空间是否充足
     */
    boolean hasEnoughStorage(String tenantCode, Long requiredStorage);

    /**
     * 续费租户服务
     */
    boolean renewTenantService(Long tenantId, int months);

    /**
     * 升级租户类型
     */
    boolean upgradeTenantType(Long tenantId, Short newType);

    /**
     * 发送租户到期提醒
     */
    boolean sendExpirationReminder(Long tenantId);

    /**
     * 自动处理过期租户
     */
    void processExpiredTenants();

    /**
     * 同步租户用户数量
     */
    boolean syncTenantUserCount(String tenantCode);

    /**
     * 同步租户存储使用量
     */
    boolean syncTenantStorageUsage(String tenantCode);

    /**
     * 获取活跃租户列表
     */
    List<Tenant> getActiveTenants();
}
