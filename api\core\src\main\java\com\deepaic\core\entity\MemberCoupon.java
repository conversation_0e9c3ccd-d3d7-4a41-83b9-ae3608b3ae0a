package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 会员权益 - 优惠券（优惠券使用时，必须校验原优惠券状态，为了方便让商家可以控制优惠券的使用）
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_coupon")
public class MemberCoupon extends BaseEntity {

    private Long memberId;

    private Long couponId;

    private String name;

    /**
     * 1现金券，2优惠券
     */
    private Short type;

    /**
     * 面值
     */
    private BigDecimal facePrice;

    /**
     * 1=正常，2=已过期，3=已下架
     */
    private Short status;

    // 状态常量
    public static final short STATUS_UNUSED = 0;   // 未使用
    public static final short STATUS_USED = 1;     // 已使用
    public static final short STATUS_EXPIRED = 2;  // 已过期
}
