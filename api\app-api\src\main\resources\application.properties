# Beautiful Posture App API Configuration
spring.application.name=app-api
server.port=8081

# Database Configuration - PostgreSQL
spring.datasource.url=*************************************************************
spring.datasource.username=postgres
spring.datasource.password=deepaic!2025
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection Pool Configuration - HikariCP (App API usually needs more connections)
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# MyBatis-Plus Configuration
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# Redis Configuration - 使用不同的数据库
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.database=1
spring.redis.timeout=5000ms
spring.redis.lettuce.pool.max-active=30
spring.redis.lettuce.pool.max-idle=15
spring.redis.lettuce.pool.min-idle=8
spring.redis.lettuce.pool.max-wait=-1ms

# Logging Configuration
logging.level.root=INFO
logging.level.com.deepaic=DEBUG
logging.level.com.baomidou.mybatisplus=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Monitoring Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when_authorized
management.metrics.export.prometheus.enabled=true

# Multi-tenant Configuration - Based on Sa-Token Session
multitenant.enabled=true
multitenant.default-schema=public
multitenant.auto-context-setup=true

# WeChat Mini Program Configuration
wechat.mini.appid=${WECHAT_MINI_APPID:}
wechat.mini.secret=${WECHAT_MINI_SECRET:}

# Member Authentication Configuration
member.auth.token-timeout=86400
member.auth.refresh-token-timeout=2592000

# 文件上传配置 - App通常需要处理更多文件
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=100MB

# 移动端特定配置
app.mobile.push.enabled=true
app.mobile.push.provider=firebase
app.mobile.offline-sync.enabled=true
