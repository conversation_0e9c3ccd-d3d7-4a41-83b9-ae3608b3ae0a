#!/bin/bash

# =====================================================
# 美姿姿健康管理系统 - 数据库一键初始化脚本
# 版本: 1.0.0
# 创建时间: 2025-06-27
# 作者: 美姿姿团队
# 说明: 一键执行数据库初始化
# =====================================================

# 设置脚本参数
set -e  # 遇到错误立即退出
set -u  # 使用未定义变量时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_HOST="localhost"
DEFAULT_PORT="5432"
DEFAULT_USER="postgres"
DEFAULT_DB="postgres"
DEFAULT_TARGET_DB="beautiful_posture"

# 读取配置或使用默认值
DB_HOST=${DB_HOST:-$DEFAULT_HOST}
DB_PORT=${DB_PORT:-$DEFAULT_PORT}
DB_USER=${DB_USER:-$DEFAULT_USER}
DB_NAME=${DB_NAME:-$DEFAULT_DB}
TARGET_DB=${TARGET_DB:-$DEFAULT_TARGET_DB}

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INIT_DIR="$SCRIPT_DIR/init"

# 日志文件
LOG_FILE="$SCRIPT_DIR/init_$(date +%Y%m%d_%H%M%S).log"

# 打印函数
print_header() {
    echo -e "${BLUE}=============================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}=============================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查依赖
check_dependencies() {
    print_header "检查依赖环境"
    
    # 检查 psql
    if ! command -v psql &> /dev/null; then
        print_error "psql 命令未找到，请安装 PostgreSQL 客户端"
        exit 1
    fi
    print_success "PostgreSQL 客户端已安装"
    
    # 检查初始化脚本
    local required_files=(
        "01_create_database.sql"
        "02_create_schemas.sql"
        "03_create_tables.sql"
        "04_create_indexes.sql"
        "05_insert_data.sql"
        "99_init_complete.sql"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$INIT_DIR/$file" ]]; then
            print_error "初始化脚本不存在: $INIT_DIR/$file"
            exit 1
        fi
    done
    print_success "所有初始化脚本文件存在"
}

# 测试数据库连接
test_connection() {
    print_header "测试数据库连接"
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" &> /dev/null; then
        print_success "数据库连接成功"
        
        # 显示数据库版本
        local version=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT version();" 2>/dev/null | head -1 | xargs)
        print_info "PostgreSQL 版本: $version"
    else
        print_error "无法连接到数据库"
        print_error "请检查连接参数: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
        exit 1
    fi
}

# 检查目标数据库是否存在
check_target_database() {
    print_header "检查目标数据库"
    
    local db_exists=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT 1 FROM pg_database WHERE datname='$TARGET_DB';" 2>/dev/null | xargs)
    
    if [[ "$db_exists" == "1" ]]; then
        print_warning "数据库 $TARGET_DB 已存在"
        
        # 检查是否已初始化
        local initialized=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" -t -c "SELECT status_value FROM system_status WHERE status_key='system.initialized';" 2>/dev/null | xargs || echo "")
        
        if [[ "$initialized" == "true" ]]; then
            print_warning "数据库已经初始化过了"
            read -p "是否要重新初始化？这将删除所有现有数据 (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_info "取消初始化"
                exit 0
            fi
            
            print_warning "将重新初始化数据库..."
            # 删除现有数据库
            psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "DROP DATABASE IF EXISTS $TARGET_DB;" >> "$LOG_FILE" 2>&1
            print_success "已删除现有数据库"
        fi
    else
        print_info "目标数据库不存在，将创建新数据库"
    fi
}

# 执行SQL脚本
execute_sql_script() {
    local script_file="$1"
    local description="$2"
    local database="$3"
    
    print_info "执行: $description"
    echo "执行时间: $(date)" >> "$LOG_FILE"
    echo "脚本: $script_file" >> "$LOG_FILE"
    echo "数据库: $database" >> "$LOG_FILE"
    echo "----------------------------------------" >> "$LOG_FILE"
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$database" -f "$script_file" >> "$LOG_FILE" 2>&1; then
        print_success "$description 完成"
    else
        print_error "$description 失败"
        print_error "详细错误信息请查看日志文件: $LOG_FILE"
        exit 1
    fi
    
    echo "" >> "$LOG_FILE"
}

# 主初始化流程
main_initialization() {
    print_header "开始数据库初始化"
    
    # 1. 创建数据库
    print_info "步骤 1/6: 创建数据库和用户"
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "CREATE DATABASE $TARGET_DB WITH ENCODING = 'UTF8' LC_COLLATE = 'zh_CN.UTF-8' LC_CTYPE = 'zh_CN.UTF-8' TEMPLATE = template0;" >> "$LOG_FILE" 2>&1 || true
    
    # 创建用户
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "CREATE USER beautiful_posture_user WITH PASSWORD 'bp_user_2025!';" >> "$LOG_FILE" 2>&1 || true
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "CREATE USER beautiful_posture_readonly WITH PASSWORD 'bp_readonly_2025!';" >> "$LOG_FILE" 2>&1 || true
    
    # 授予权限
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "GRANT CONNECT ON DATABASE $TARGET_DB TO beautiful_posture_user;" >> "$LOG_FILE" 2>&1 || true
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "GRANT CONNECT ON DATABASE $TARGET_DB TO beautiful_posture_readonly;" >> "$LOG_FILE" 2>&1 || true
    
    print_success "数据库和用户创建完成"
    
    # 2. 创建Schema
    execute_sql_script "$INIT_DIR/02_create_schemas.sql" "步骤 2/6: 创建Schema结构" "$TARGET_DB"
    
    # 3. 创建表结构
    execute_sql_script "$INIT_DIR/03_create_tables.sql" "步骤 3/6: 创建表结构" "$TARGET_DB"
    
    # 4. 创建索引
    execute_sql_script "$INIT_DIR/04_create_indexes.sql" "步骤 4/6: 创建索引" "$TARGET_DB"
    
    # 5. 插入初始数据
    execute_sql_script "$INIT_DIR/05_insert_data.sql" "步骤 5/6: 插入初始数据" "$TARGET_DB"
    
    # 6. 完成初始化
    execute_sql_script "$INIT_DIR/99_init_complete.sql" "步骤 6/6: 完成初始化验证" "$TARGET_DB"
}

# 显示初始化结果
show_result() {
    print_header "初始化完成"
    
    # 获取系统信息
    local system_info=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$TARGET_DB" -t -c "SELECT public.health_check();" 2>/dev/null | jq -r '.system.version' 2>/dev/null || echo "1.0.0")
    
    print_success "美姿姿健康管理系统初始化成功！"
    echo
    print_info "系统信息:"
    echo "  - 数据库: $TARGET_DB"
    echo "  - 版本: $system_info"
    echo "  - 主机: $DB_HOST:$DB_PORT"
    echo
    print_info "默认登录信息:"
    echo "  平台管理员:"
    echo "    用户名: admin"
    echo "    密码: admin123"
    echo
    echo "  租户管理员 (demo):"
    echo "    用户名: demo_admin"
    echo "    密码: admin123"
    echo
    print_info "下一步操作:"
    echo "  1. 启动应用服务"
    echo "  2. 访问管理后台"
    echo "  3. 修改默认密码"
    echo "  4. 配置系统参数"
    echo
    print_info "日志文件: $LOG_FILE"
}

# 显示帮助信息
show_help() {
    echo "美姿姿健康管理系统 - 数据库初始化脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -H, --host HOST         数据库主机 (默认: localhost)"
    echo "  -p, --port PORT         数据库端口 (默认: 5432)"
    echo "  -U, --user USER         数据库用户 (默认: postgres)"
    echo "  -d, --database DB       连接数据库 (默认: postgres)"
    echo "  -t, --target TARGET     目标数据库 (默认: beautiful_posture)"
    echo
    echo "环境变量:"
    echo "  DB_HOST                 数据库主机"
    echo "  DB_PORT                 数据库端口"
    echo "  DB_USER                 数据库用户"
    echo "  DB_NAME                 连接数据库"
    echo "  TARGET_DB               目标数据库"
    echo "  PGPASSWORD              数据库密码"
    echo
    echo "示例:"
    echo "  $0                                    # 使用默认配置"
    echo "  $0 -H localhost -p 5432 -U postgres  # 指定连接参数"
    echo "  PGPASSWORD=password $0                # 使用环境变量设置密码"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -H|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -p|--port)
            DB_PORT="$2"
            shift 2
            ;;
        -U|--user)
            DB_USER="$2"
            shift 2
            ;;
        -d|--database)
            DB_NAME="$2"
            shift 2
            ;;
        -t|--target)
            TARGET_DB="$2"
            shift 2
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主流程
main() {
    print_header "美姿姿健康管理系统 - 数据库初始化"
    
    echo "配置信息:"
    echo "  数据库主机: $DB_HOST:$DB_PORT"
    echo "  连接用户: $DB_USER"
    echo "  连接数据库: $DB_NAME"
    echo "  目标数据库: $TARGET_DB"
    echo "  日志文件: $LOG_FILE"
    echo
    
    # 执行初始化步骤
    check_dependencies
    test_connection
    check_target_database
    main_initialization
    show_result
    
    print_success "数据库初始化完成！"
}

# 执行主流程
main "$@"
