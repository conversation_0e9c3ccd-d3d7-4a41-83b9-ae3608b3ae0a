package com.deepaic.core.auth.services;

import com.deepaic.core.auth.interfaces.AuthenticationService;
import com.deepaic.core.auth.interfaces.TokenService;
import com.deepaic.core.auth.models.AuthRequest;
import com.deepaic.core.auth.models.AuthResponse;
import com.deepaic.core.auth.models.UserPrincipal;
import com.deepaic.core.constant.SystemConstant;
import com.deepaic.core.entity.UserAccount;
import com.deepaic.core.exception.BussinessException;
import com.deepaic.core.entity.Tenant;
import com.deepaic.core.mapper.UserAccountMapper;
import com.deepaic.core.mapper.TenantMapper;
import com.deepaic.core.tenant.TenantContextService;
import com.deepaic.core.util.PasswordUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户账户认证服务
 * 处理租户管理员、租户用户等的认证
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerAuthService implements AuthenticationService {

    private final UserAccountMapper userAccountMapper;
    private final TenantMapper tenantMapper;
    private final TokenService tokenService;
    private final TenantContextService tenantContextService;

    @Value("${system.salt}")
    private String salt;

    @Override
    @Transactional
    public AuthResponse authenticate(AuthRequest request) throws BussinessException {
        UserAccount account = null;
        switch (request.getAuthenticationType()) {
            case USERNAME_PASSWORD:
            case PHONE_PASSWORD:
                account = authenticateByPassword(request);
                break;
            case PHONE_CODE:
                account = authenticateByPhoneCode(request);
                break;
            default:
                throw new BussinessException("不支持的认证类型");
        }

        Tenant tenant = tenantMapper.selectByTenantCode(account.getTenantCode());
        // 创建用户主体
        UserPrincipal userPrincipal = this.createUserPrincipal(account, tenant);

        // 生成令牌
        String accessToken = tokenService.generateAccessToken(userPrincipal);
        Long expiresIn = tokenService.getTokenExpiration(accessToken);

        // 设置租户上下文
        tenantContextService.setupTenantContext(tenant.getTenantCode(), account.getId(), account.getUsername());

        // 更新最后登录信息
        userAccountMapper.updateLastLoginInfo(account.getId(), LocalDateTime.now(), request.getClientIp());

        log.info("客户账户登录成功: username={}, tenantCode={}, accountId={}",
                request.getUsername(), tenant.getTenantCode(), account.getId());

        return AuthResponse.success(accessToken, expiresIn, userPrincipal);

    }

    private void checkAccounHealth(UserAccount account) throws BussinessException {
        if (!account.isEnabled()) {
            throw new BussinessException("账户已被禁用");
        }
        if (account.isLocked()) {
            throw new BussinessException("账户已被锁定");
        }
    }

    private UserAccount authenticateByPassword(AuthRequest request) throws BussinessException {
        List<UserAccount> accounts = null;
        if (request.getAuthenticationType() == AuthRequest.AuthenticationType.USERNAME_PASSWORD) {
            accounts = userAccountMapper.selectByUsername(request.getUsername());
        } else if (request.getAuthenticationType() == AuthRequest.AuthenticationType.PHONE_PASSWORD) {
            accounts = userAccountMapper.selectByPhone(request.getPhone());
        } else {
            throw new BussinessException("不支持的认证类型");
        }

        if (accounts.isEmpty()) {
            throw new BussinessException("用户名或密码错误");
        }
        if (accounts.size() > 1) {
            // TODO 用户在多个租户下存在，需要选择一个租户
        }

        UserAccount account = accounts.get(0);
        this.checkAccounHealth(account);
        // 验证密码
        if (!PasswordUtil.matches(request.getPassword(), account.getPassword(),salt)) {
            // 增加登录失败次数
            userAccountMapper.incrementLoginFailCount(account.getId());
            // 检查是否需要锁定账户
            if (account.getLoginFailCount() != null
                    && account.getLoginFailCount() >= SystemConstant.MAX_LOGIN_FAIL_COUNT) {
                userAccountMapper.lockAccount(account.getId(), LocalDateTime.now());
                throw new BussinessException("登录失败次数过多，账户已被锁定");
            }
            throw new BussinessException("用户名或密码错误");
        }
        return account;
    }

    private UserAccount authenticateByPhoneCode(AuthRequest request) throws BussinessException {
        List<UserAccount> accounts = userAccountMapper.selectByPhone(request.getPhone());
        if (accounts.isEmpty()) {
            throw new BussinessException("用户名或密码错误");
        }
        if (accounts.size() > 1) {
            // TODO 用户在多个租户下存在，需要选择一个租户
        }
        UserAccount account = accounts.get(0);
        this.checkAccounHealth(account);
        // TODO 验证短信验证码

        return account;
    }

    @Override
    public boolean logout(String userId) {
        try {
            tokenService.revokeAllTokens(userId);
            log.info("客户账户登出成功: userId={}", userId);
            return true;
        } catch (Exception e) {
            log.error("客户账户登出失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    public AuthResponse refreshToken(String refreshToken) {
        try {
            String newAccessToken = tokenService.refreshAccessToken(refreshToken);
            Long expiresIn = tokenService.getTokenExpiration(newAccessToken);
            return AuthResponse.refreshSuccess(newAccessToken, expiresIn);
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            return AuthResponse.failure("刷新令牌失败", AuthResponse.ERROR_TOKEN_INVALID);
        }
    }

    @Override
    public UserPrincipal validateToken(String token) {
        if (!tokenService.validateToken(token)) {
            return null;
        }
        return tokenService.parseToken(token);
    }

    @Override
    public UserPrincipal getCurrentUser() {
        // 这里需要从当前上下文获取令牌，然后解析
        // 具体实现依赖于如何传递当前令牌
        return null;
    }

    @Override
    public boolean isAuthenticated() {
        UserPrincipal currentUser = getCurrentUser();
        return currentUser != null && currentUser.isCustomerAccount();
    }

    /**
     * 创建用户主体
     */
    private UserPrincipal createUserPrincipal(UserAccount account, Tenant tenant) {
        return UserPrincipal.builder()
                .userId(account.getId().toString())
                .userType(UserPrincipal.UserType.SASS_CLIENT_ACCOUNT)
                .username(account.getUsername())
                .realName(null)
                .phone(account.getPhone())
                .tenantCode(account.getTenantCode())
                .tenantName(tenant.getTenantName())
                .lastLoginTime(account.getLastLoginTime())
                .lastLoginIp(account.getLastLoginIp())
                .build();
    }

   

    @Override
    public UserPrincipal.UserType getUserType() {
        return UserPrincipal.UserType.SASS_CLIENT_ACCOUNT;
    }
}
