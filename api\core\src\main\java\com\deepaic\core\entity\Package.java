package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_package")
public class Package extends BaseEntity {

    private String code;

    private String name;

    /**
     * 实际价值
     */
    private BigDecimal actualPrice;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    /**
     * 1启用，0禁用
     */
    private Short status;


    // 状态常量
    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_NORMAL = 1;   // 正常
}
