package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_bussiness")
public class MemberBussiness extends BaseEntity {

    private Long memberId;

    private Integer point;

    private LocalDate lastConsumptionDate;

    /**
     * 负责顾问
     */
    private Long consultantId;

    /**
     * 上一次服务美容师
     */
    private Long lastBeauticianId;

    /**
     * 客户画像标签
     */
    private String tag;

    private Boolean isEnablePassword;

    private String consumptionPassword;

    /**
     * 启用短信或者微信验证
     */
    private Boolean isEnableSms;

    private String remark;

    /**
     * 推荐人
     */
    private Long referrerUser;
}
