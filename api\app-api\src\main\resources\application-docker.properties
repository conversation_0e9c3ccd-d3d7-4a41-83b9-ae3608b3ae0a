# 美姿姿 App API Docker环境配置

# 数据库配置 - Docker环境
spring.datasource.url=*************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres123

# Redis配置 - Docker环境
spring.redis.host=redis
spring.redis.port=6379
spring.redis.database=1

# 日志配置 - Docker环境
logging.level.root=INFO
logging.level.com.deepaic=INFO
logging.level.com.baomidou.mybatisplus=WARN

# 监控配置
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
