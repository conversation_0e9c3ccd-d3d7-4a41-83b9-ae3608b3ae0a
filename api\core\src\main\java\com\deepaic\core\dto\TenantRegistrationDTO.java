package com.deepaic.core.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 租户注册请求DTO
 * 包含租户基本信息和管理员账户信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantRegistrationDTO {

    // ========== 租户基本信息 ==========
    
    /**
     * 租户名称
     */
    @NotBlank(message = "租户名称不能为空")
    @Size(min = 2, max = 100, message = "租户名称长度必须在2-100个字符之间")
    private String tenantName;

    /**
     * 租户编码（可选，系统自动生成）
     */
    @Size(max = 50, message = "租户编码长度不能超过50个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_-]*$", message = "租户编码只能包含字母、数字、下划线和横线")
    private String tenantCode;

    /**
     * 企业全称
     */
    @NotBlank(message = "企业全称不能为空")
    @Size(max = 200, message = "企业全称长度不能超过200个字符")
    private String companyName;

    /**
     * 统一社会信用代码
     */
    @Size(max = 50, message = "统一社会信用代码长度不能超过50个字符")
    private String creditCode;

    /**
     * 行业类型
     */
    @NotNull(message = "行业类型不能为空")
    private Integer industryType;

    /**
     * 企业规模
     */
    private Integer companySize;

    /**
     * 企业地址
     */
    @Size(max = 500, message = "企业地址长度不能超过500个字符")
    private String address;

    /**
     * 企业官网
     */
    @Size(max = 200, message = "企业官网长度不能超过200个字符")
    @Pattern(regexp = "^(https?://)?[\\w\\.-]+\\.[a-zA-Z]{2,}.*$", message = "企业官网格式不正确")
    private String website;

    /**
     * 企业描述
     */
    @Size(max = 1000, message = "企业描述长度不能超过1000个字符")
    private String description;

    // ========== 联系人信息 ==========

    /**
     * 联系人姓名
     */
    @NotBlank(message = "联系人姓名不能为空")
    @Size(max = 50, message = "联系人姓名长度不能超过50个字符")
    private String contactName;

    /**
     * 联系人手机号
     */
    @NotBlank(message = "联系人手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @NotBlank(message = "联系人邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String contactEmail;

    /**
     * 联系人职位
     */
    @Size(max = 50, message = "联系人职位长度不能超过50个字符")
    private String contactPosition;

    // ========== 管理员账户信息 ==========

    /**
     * 管理员用户名
     */
    @NotBlank(message = "管理员用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "用户名只能包含字母、数字、下划线和横线")
    private String adminUsername;

    /**
     * 管理员密码
     */
    @NotBlank(message = "管理员密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,}$", 
             message = "密码必须包含大小写字母和数字")
    private String adminPassword;

    /**
     * 管理员真实姓名
     */
    @NotBlank(message = "管理员真实姓名不能为空")
    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String adminRealName;

    /**
     * 管理员邮箱（可与联系人邮箱相同）
     */
    @NotBlank(message = "管理员邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String adminEmail;

    /**
     * 管理员手机号（可与联系人手机号相同）
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String adminPhone;

    // ========== 业务配置 ==========

    /**
     * 预期用户数量
     */
    private Integer expectedUserCount;

    /**
     * 套餐类型
     */
    private String packageType;

    /**
     * 试用期天数
     */
    private Integer trialDays;

    /**
     * 备注信息
     */
    @Size(max = 500, message = "备注信息长度不能超过500个字符")
    private String remark;

    // ========== 验证信息 ==========

    /**
     * 短信验证码
     */
    @NotBlank(message = "短信验证码不能为空")
    @Size(min = 4, max = 6, message = "验证码长度不正确")
    private String smsCode;

    /**
     * 邮箱验证码
     */
    private String emailCode;

    /**
     * 图形验证码
     */
    private String captcha;

    /**
     * 同意服务条款
     */
    @NotNull(message = "必须同意服务条款")
    @AssertTrue(message = "必须同意服务条款")
    private Boolean agreeTerms;

    /**
     * 同意隐私政策
     */
    @NotNull(message = "必须同意隐私政策")
    @AssertTrue(message = "必须同意隐私政策")
    private Boolean agreePrivacy;

    // ========== 系统字段 ==========

    /**
     * 注册来源
     */
    private String registrationSource;

    /**
     * 推荐人编码
     */
    private String referralCode;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    // 行业类型常量
    public static final int INDUSTRY_RETAIL = 1;           // 零售业
    public static final int INDUSTRY_MANUFACTURING = 2;    // 制造业
    public static final int INDUSTRY_FINANCE = 3;          // 金融业
    public static final int INDUSTRY_EDUCATION = 4;        // 教育行业
    public static final int INDUSTRY_HEALTHCARE = 5;       // 医疗健康
    public static final int INDUSTRY_TECHNOLOGY = 6;       // 科技互联网
    public static final int INDUSTRY_REAL_ESTATE = 7;      // 房地产
    public static final int INDUSTRY_LOGISTICS = 8;        // 物流运输
    public static final int INDUSTRY_CATERING = 9;         // 餐饮服务
    public static final int INDUSTRY_OTHER = 99;           // 其他

    // 企业规模常量
    public static final int SIZE_MICRO = 1;        // 微型企业（1-10人）
    public static final int SIZE_SMALL = 2;        // 小型企业（11-50人）
    public static final int SIZE_MEDIUM = 3;       // 中型企业（51-200人）
    public static final int SIZE_LARGE = 4;        // 大型企业（200人以上）

    /**
     * 获取行业类型描述
     */
    public String getIndustryTypeDesc() {
        return switch (industryType) {
            case INDUSTRY_RETAIL -> "零售业";
            case INDUSTRY_MANUFACTURING -> "制造业";
            case INDUSTRY_FINANCE -> "金融业";
            case INDUSTRY_EDUCATION -> "教育行业";
            case INDUSTRY_HEALTHCARE -> "医疗健康";
            case INDUSTRY_TECHNOLOGY -> "科技互联网";
            case INDUSTRY_REAL_ESTATE -> "房地产";
            case INDUSTRY_LOGISTICS -> "物流运输";
            case INDUSTRY_CATERING -> "餐饮服务";
            case INDUSTRY_OTHER -> "其他";
            default -> "未知";
        };
    }

    /**
     * 获取企业规模描述
     */
    public String getCompanySizeDesc() {
        return switch (companySize) {
            case SIZE_MICRO -> "微型企业（1-10人）";
            case SIZE_SMALL -> "小型企业（11-50人）";
            case SIZE_MEDIUM -> "中型企业（51-200人）";
            case SIZE_LARGE -> "大型企业（200人以上）";
            default -> "未知";
        };
    }
}
