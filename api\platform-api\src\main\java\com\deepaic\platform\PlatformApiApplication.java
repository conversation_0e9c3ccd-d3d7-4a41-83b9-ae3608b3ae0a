package com.deepaic.platform;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * SaaS平台管理API启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {
    "com.deepaic.core",
    "com.deepaic.service",
    "com.deepaic.platform"
})
@MapperScan(basePackages = {
    "com.deepaic.core.mapper",
    "com.deepaic.service.mapper"
})
public class PlatformApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(PlatformApiApplication.class, args);
    }
}
