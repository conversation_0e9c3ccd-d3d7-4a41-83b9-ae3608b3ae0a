-- 美姿姿数据库初始化脚本
-- 创建数据库和基础表结构

-- 设置字符编码
SET client_encoding = 'UTF8';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    avatar VARCHAR(255),
    status INTEGER DEFAULT 1,
    tenant_id VARCHAR(50) DEFAULT 'default',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted INTEGER DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_user_username ON sys_user(username);
CREATE INDEX IF NOT EXISTS idx_sys_user_email ON sys_user(email);
CREATE INDEX IF NOT EXISTS idx_sys_user_tenant_id ON sys_user(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sys_user_deleted ON sys_user(deleted);

-- 插入默认管理员用户
INSERT INTO sys_user (username, password, email, status, tenant_id) 
VALUES ('admin', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMqcpAy5.Fule9nh.lklXjjQCYQG.', '<EMAIL>', 1, 'default')
ON CONFLICT (username) DO NOTHING;

-- 创建角色表
CREATE TABLE IF NOT EXISTS sys_role (
    id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL,
    role_code VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    status INTEGER DEFAULT 1,
    tenant_id VARCHAR(50) DEFAULT 'default',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted INTEGER DEFAULT 0
);

-- 创建用户角色关联表
CREATE TABLE IF NOT EXISTS sys_user_role (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    tenant_id VARCHAR(50) DEFAULT 'default',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    deleted INTEGER DEFAULT 0,
    UNIQUE(user_id, role_id, tenant_id)
);

-- 创建权限表
CREATE TABLE IF NOT EXISTS sys_permission (
    id BIGSERIAL PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL,
    permission_code VARCHAR(100) NOT NULL UNIQUE,
    resource_type VARCHAR(20) DEFAULT 'menu',
    parent_id BIGINT DEFAULT 0,
    path VARCHAR(255),
    component VARCHAR(255),
    icon VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    tenant_id VARCHAR(50) DEFAULT 'default',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted INTEGER DEFAULT 0
);

-- 创建角色权限关联表
CREATE TABLE IF NOT EXISTS sys_role_permission (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    tenant_id VARCHAR(50) DEFAULT 'default',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    deleted INTEGER DEFAULT 0,
    UNIQUE(role_id, permission_id, tenant_id)
);

-- 插入默认角色
INSERT INTO sys_role (role_name, role_code, description, tenant_id) 
VALUES 
    ('超级管理员', 'SUPER_ADMIN', '系统超级管理员', 'default'),
    ('管理员', 'ADMIN', '系统管理员', 'default'),
    ('普通用户', 'USER', '普通用户', 'default')
ON CONFLICT (role_code) DO NOTHING;

-- 为管理员用户分配超级管理员角色
INSERT INTO sys_user_role (user_id, role_id, tenant_id)
SELECT u.id, r.id, 'default'
FROM sys_user u, sys_role r
WHERE u.username = 'admin' AND r.role_code = 'SUPER_ADMIN'
ON CONFLICT (user_id, role_id, tenant_id) DO NOTHING;

-- 创建租户表
CREATE TABLE IF NOT EXISTS sys_tenant (
    id BIGSERIAL PRIMARY KEY,
    tenant_code VARCHAR(50) NOT NULL UNIQUE,
    tenant_name VARCHAR(100) NOT NULL,
    schema_name VARCHAR(50) NOT NULL,
    domain VARCHAR(100),
    status INTEGER DEFAULT 1,
    expire_time TIMESTAMP,
    contact_name VARCHAR(50),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted INTEGER DEFAULT 0
);

-- 插入默认租户
INSERT INTO sys_tenant (tenant_code, tenant_name, schema_name, status) 
VALUES ('default', '默认租户', 'public', 1)
ON CONFLICT (tenant_code) DO NOTHING;

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS sys_config (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string',
    description VARCHAR(255),
    tenant_id VARCHAR(50) DEFAULT 'default',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted INTEGER DEFAULT 0,
    UNIQUE(config_key, tenant_id)
);

-- 插入默认配置
INSERT INTO sys_config (config_key, config_value, config_type, description, tenant_id) 
VALUES 
    ('system.name', '美姿姿健康管理系统', 'string', '系统名称', 'default'),
    ('system.version', '1.0.0', 'string', '系统版本', 'default'),
    ('system.copyright', '美姿姿团队', 'string', '版权信息', 'default')
ON CONFLICT (config_key, tenant_id) DO NOTHING;

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS sys_operation_log (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT,
    username VARCHAR(50),
    operation VARCHAR(100),
    method VARCHAR(200),
    params TEXT,
    result TEXT,
    ip_address VARCHAR(50),
    user_agent VARCHAR(500),
    execution_time BIGINT,
    status INTEGER DEFAULT 1,
    error_message TEXT,
    tenant_id VARCHAR(50) DEFAULT 'default',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_operation_log_user_id ON sys_operation_log(user_id);
CREATE INDEX IF NOT EXISTS idx_operation_log_created_time ON sys_operation_log(created_time);
CREATE INDEX IF NOT EXISTS idx_operation_log_tenant_id ON sys_operation_log(tenant_id);

-- 创建登录日志表
CREATE TABLE IF NOT EXISTS sys_login_log (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT,
    username VARCHAR(50),
    login_type VARCHAR(20) DEFAULT 'web',
    ip_address VARCHAR(50),
    user_agent VARCHAR(500),
    browser VARCHAR(50),
    os VARCHAR(50),
    status INTEGER DEFAULT 1,
    message VARCHAR(255),
    tenant_id VARCHAR(50) DEFAULT 'default',
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_login_log_user_id ON sys_login_log(user_id);
CREATE INDEX IF NOT EXISTS idx_login_log_login_time ON sys_login_log(login_time);
CREATE INDEX IF NOT EXISTS idx_login_log_tenant_id ON sys_login_log(tenant_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_sys_user_updated_time BEFORE UPDATE ON sys_user FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_sys_role_updated_time BEFORE UPDATE ON sys_role FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_sys_permission_updated_time BEFORE UPDATE ON sys_permission FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_sys_tenant_updated_time BEFORE UPDATE ON sys_tenant FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
CREATE TRIGGER update_sys_config_updated_time BEFORE UPDATE ON sys_config FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
