# 美姿姿 - 基础设施部署配置
# 包含数据库、缓存等基础组件，适用于开发环境或作为生产环境的基础设施层
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:17-alpine
    container_name: beautiful-posture-postgres
    environment:
      POSTGRES_DB: beautiful_posture
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - beautiful-posture-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: beautiful-posture-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - beautiful-posture-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  beautiful-posture-network:
    driver: bridge
