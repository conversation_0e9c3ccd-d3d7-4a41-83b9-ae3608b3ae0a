# 租户注册功能设计文档

## 🎯 功能概述

租户注册功能是SaaS平台的核心功能之一，允许企业用户自主注册成为平台租户，并自动创建租户环境和管理员账户。

### 核心特性
1. **完整的注册流程** - 从信息填写到环境初始化
2. **多重验证机制** - 短信、邮箱、图形验证码
3. **自动环境初始化** - 创建租户专属数据库Schema
4. **灵活的审核机制** - 支持自动审核或人工审核
5. **智能编码生成** - 自动生成唯一的租户编码
6. **完善的通知系统** - 短信和邮件通知

## 🏗️ 架构设计

### 核心组件

```
租户注册功能架构
├── 数据模型层
│   ├── TenantRegistrationDTO.java      # 注册请求模型
│   └── TenantRegistrationResult.java   # 注册结果模型
├── 服务层
│   ├── TenantRegistrationService.java  # 租户注册服务
│   ├── SmsService.java                 # 短信服务接口
│   ├── EmailService.java               # 邮件服务接口
│   └── TenantSchemaManager.java        # 租户Schema管理
├── 工具层
│   └── TenantCodeGenerator.java        # 租户编码生成器
└── 控制器层
    └── TenantRegistrationController.java # 注册API控制器
```

### 数据库设计

#### 租户表扩展字段
```sql
-- 新增注册相关字段
ALTER TABLE pub_tenant ADD COLUMN company_name VARCHAR(200);        -- 企业全称
ALTER TABLE pub_tenant ADD COLUMN credit_code VARCHAR(50);          -- 统一社会信用代码
ALTER TABLE pub_tenant ADD COLUMN industry_type INTEGER;            -- 行业类型
ALTER TABLE pub_tenant ADD COLUMN company_size INTEGER;             -- 企业规模
ALTER TABLE pub_tenant ADD COLUMN description TEXT;                 -- 企业描述
ALTER TABLE pub_tenant ADD COLUMN contact_position VARCHAR(50);     -- 联系人职位
ALTER TABLE pub_tenant ADD COLUMN expected_user_count INTEGER;      -- 预期用户数量
ALTER TABLE pub_tenant ADD COLUMN package_type VARCHAR(50);         -- 套餐类型
ALTER TABLE pub_tenant ADD COLUMN registration_source VARCHAR(50);  -- 注册来源
ALTER TABLE pub_tenant ADD COLUMN referral_code VARCHAR(50);        -- 推荐人编码
ALTER TABLE pub_tenant ADD COLUMN audit_status INTEGER DEFAULT 0;   -- 审核状态
ALTER TABLE pub_tenant ADD COLUMN audit_time TIMESTAMP;             -- 审核时间
ALTER TABLE pub_tenant ADD COLUMN audit_by BIGINT;                  -- 审核人
ALTER TABLE pub_tenant ADD COLUMN audit_remark VARCHAR(500);        -- 审核备注
```

## 🔄 注册流程

### 1. 完整注册流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端页面
    participant API as 注册API
    participant Service as 注册服务
    participant SMS as 短信服务
    participant Email as 邮件服务
    participant DB as 数据库
    participant Schema as Schema管理器
    
    User->>Frontend: 填写注册信息
    Frontend->>API: 发送短信验证码
    API->>SMS: 发送验证码
    SMS-->>User: 收到短信验证码
    
    User->>Frontend: 提交注册表单
    Frontend->>API: 租户注册请求
    API->>Service: 处理注册
    
    Service->>Service: 验证注册信息
    Service->>DB: 创建租户记录
    Service->>DB: 创建管理员账户
    Service->>Schema: 初始化租户环境
    Service->>Email: 发送注册成功邮件
    Service->>SMS: 发送注册成功短信
    
    Service-->>API: 返回注册结果
    API-->>Frontend: 返回结果
    Frontend-->>User: 显示注册成功
```

### 2. 注册步骤详解

#### 步骤1: 信息收集
```javascript
// 前端表单数据结构
const registrationData = {
  // 租户基本信息
  tenantName: "美姿姿科技",
  companyName: "美姿姿科技有限公司",
  industryType: 6, // 科技互联网
  companySize: 2,  // 小型企业
  
  // 联系人信息
  contactName: "张三",
  contactPhone: "***********",
  contactEmail: "<EMAIL>",
  
  // 管理员账户
  adminUsername: "admin",
  adminPassword: "Admin123!",
  adminRealName: "张三",
  adminEmail: "<EMAIL>",
  
  // 验证信息
  smsCode: "123456",
  agreeTerms: true,
  agreePrivacy: true
};
```

#### 步骤2: 服务端处理
```java
@PostMapping("/register")
public Map<String, Object> registerTenant(@Valid @RequestBody TenantRegistrationDTO dto) {
    // 1. 验证短信验证码
    if (!smsService.validateSmsCode(dto.getContactPhone(), dto.getSmsCode())) {
        return error("短信验证码无效");
    }
    
    // 2. 检查重复性
    if (tenantMapper.existsByCompanyName(dto.getCompanyName())) {
        return error("企业名称已存在");
    }
    
    // 3. 执行注册
    TenantRegistrationResult result = tenantRegistrationService.registerTenant(dto);
    
    return success(result);
}
```

#### 步骤3: 环境初始化
```java
private void initializeTenantEnvironment(Tenant tenant) {
    // 创建租户专属Schema
    tenantSchemaManager.createTenantSchema(tenant.getTenantCode());
    
    // 初始化基础表结构
    tenantSchemaManager.initializeTenantTables(tenant.getTenantCode());
    
    // 初始化基础数据
    tenantSchemaManager.initializeTenantData(tenant.getTenantCode());
}
```

## 📋 API接口设计

### 1. 租户注册接口

#### 请求
```http
POST /api/public/tenant/register
Content-Type: application/json

{
  "tenantName": "美姿姿科技",
  "companyName": "美姿姿科技有限公司",
  "industryType": 6,
  "companySize": 2,
  "contactName": "张三",
  "contactPhone": "***********",
  "contactEmail": "<EMAIL>",
  "adminUsername": "admin",
  "adminPassword": "Admin123!",
  "adminRealName": "张三",
  "adminEmail": "<EMAIL>",
  "smsCode": "123456",
  "agreeTerms": true,
  "agreePrivacy": true
}
```

#### 响应
```json
{
  "code": 200,
  "message": "租户注册成功",
  "data": {
    "success": true,
    "tenantId": 1001,
    "tenantCode": "mzzkj001",
    "tenantName": "美姿姿科技",
    "adminAccountId": 2001,
    "adminUsername": "admin",
    "auditStatus": 3,
    "auditStatusDesc": "自动通过",
    "trialExpireTime": "2024-07-21T10:30:00",
    "loginUrl": "http://localhost:8080/admin",
    "registrationTime": "2024-06-21T10:30:00",
    "nextStepTip": "请使用管理员账户登录系统"
  }
}
```

### 2. 辅助接口

#### 发送短信验证码
```http
POST /api/public/tenant/send-sms-code?phone=***********
```

#### 检查租户编码可用性
```http
GET /api/public/tenant/check-tenant-code?tenantCode=mzzkj001
```

#### 建议租户编码
```http
GET /api/public/tenant/suggest-tenant-code?tenantName=美姿姿科技
```

#### 获取行业类型列表
```http
GET /api/public/tenant/industry-types
```

## 🔧 租户编码生成策略

### 生成规则
1. **优先使用拼音**: 中文名称转换为拼音首字母
2. **保留英文**: 英文字符直接使用
3. **添加数字后缀**: 确保唯一性
4. **长度限制**: 3-50个字符
5. **字符限制**: 只允许字母、数字、下划线、横线

### 生成示例
```java
// 输入: "美姿姿科技"
// 输出: "mzzkj001"

// 输入: "TechCorp"
// 输出: "techcorp001"

// 输入: "ABC公司"
// 输出: "abcgs001"
```

## 🔐 验证机制

### 1. 短信验证码
- **发送频率限制**: 60秒内只能发送一次
- **有效期**: 5分钟
- **验证次数限制**: 最多验证3次
- **IP限制**: 同一IP每小时最多发送10次

### 2. 数据验证
- **必填字段验证**: 使用JSR-303注解
- **格式验证**: 邮箱、手机号、密码强度
- **业务验证**: 重复性检查、黑名单检查

### 3. 安全防护
- **图形验证码**: 防止机器人注册
- **IP白名单**: 可配置允许注册的IP段
- **注册频率限制**: 防止恶意注册

## 📧 通知系统

### 1. 短信通知
```java
// 注册成功短信
String message = String.format(
    "恭喜您成功注册美姿姿平台！租户：%s，管理员账户：%s，请及时登录系统。",
    tenantName, username
);
smsService.send(phone, message);
```

### 2. 邮件通知
```java
// 注册成功邮件
EmailTemplate template = EmailTemplate.builder()
    .to(email)
    .subject("欢迎加入美姿姿平台")
    .template("registration-success")
    .variables(Map.of(
        "tenantName", tenantName,
        "username", username,
        "loginUrl", loginUrl
    ))
    .build();
emailService.send(template);
```

## ⚙️ 配置选项

### 应用配置
```yaml
app:
  tenant:
    auto-audit: true              # 是否自动审核
    trial-days: 30               # 默认试用天数
    admin-login-url: http://localhost:8080/admin
    
  sms:
    provider: aliyun             # 短信服务商
    template-id: SMS_123456789   # 短信模板ID
    
  email:
    provider: smtp               # 邮件服务商
    from: <EMAIL>       # 发件人邮箱
```

### 业务配置
```java
// 行业类型配置
public static final Map<Integer, String> INDUSTRY_TYPES = Map.of(
    1, "零售业",
    2, "制造业", 
    3, "金融业",
    4, "教育行业",
    5, "医疗健康",
    6, "科技互联网"
);

// 企业规模配置
public static final Map<Integer, String> COMPANY_SIZES = Map.of(
    1, "微型企业（1-10人）",
    2, "小型企业（11-50人）",
    3, "中型企业（51-200人）",
    4, "大型企业（200人以上）"
);
```

## 🎯 使用示例

### 前端集成示例
```javascript
// 1. 发送短信验证码
async function sendSmsCode(phone) {
  const response = await fetch('/api/public/tenant/send-sms-code', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: `phone=${phone}`
  });
  return response.json();
}

// 2. 提交注册表单
async function registerTenant(formData) {
  const response = await fetch('/api/public/tenant/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(formData)
  });
  return response.json();
}

// 3. 检查租户编码
async function checkTenantCode(tenantCode) {
  const response = await fetch(`/api/public/tenant/check-tenant-code?tenantCode=${tenantCode}`);
  return response.json();
}
```

## ✅ 测试用例

### 1. 正常注册流程测试
```java
@Test
public void testNormalRegistration() {
    TenantRegistrationDTO dto = TenantRegistrationDTO.builder()
        .tenantName("测试租户")
        .companyName("测试公司")
        .contactPhone("***********")
        .smsCode("123456")
        .build();
        
    TenantRegistrationResult result = tenantRegistrationService.registerTenant(dto);
    
    assertTrue(result.isSuccess());
    assertNotNull(result.getTenantCode());
}
```

### 2. 重复注册测试
```java
@Test
public void testDuplicateRegistration() {
    // 第一次注册
    registerTenant("测试公司");
    
    // 第二次注册相同公司名
    TenantRegistrationResult result = registerTenant("测试公司");
    
    assertFalse(result.isSuccess());
    assertEquals("COMPANY_EXISTS", result.getErrorCode());
}
```

## 🎉 总结

租户注册功能为SaaS平台提供了完整的自助注册能力：

1. **用户友好**: 简单直观的注册流程
2. **安全可靠**: 多重验证和安全防护
3. **自动化**: 自动环境初始化和通知
4. **可扩展**: 灵活的配置和扩展机制
5. **易维护**: 清晰的架构和完善的文档

通过这套租户注册功能，企业用户可以快速便捷地加入平台，开始使用SaaS服务！🚀
