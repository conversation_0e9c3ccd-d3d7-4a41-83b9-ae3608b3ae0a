package com.deepaic.core.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ArrayUtil;

import com.deepaic.core.exception.BussinessException;

import java.util.Map;
import java.util.function.Supplier;

/**
 * 断言工具类
 */
public class AssertUtil {

    private static final String LENGTH_FORMATTER_STR = "Length must be between {} and {}.";

    private AssertUtil() {

    }

    public static <X extends Throwable> void isTrue(boolean expression, Supplier<? extends X> supplier) throws X {
        if (!expression) {
            throw supplier.get();
        }
    }

    public static void isTrue(boolean expression, String errorMsgTemplate, Object... params) throws BussinessException {
        isTrue(expression, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static void isTrue(boolean expression) throws BussinessException {
        isTrue(expression, "[Assertion failed] - this expression must be true");
    }

    public static <X extends Throwable> void isFalse(boolean expression, Supplier<X> errorSupplier) throws X {
        if (expression) {
            throw errorSupplier.get();
        }
    }

    public static void isFalse(boolean expression, String errorMsgTemplate, Object... params) throws BussinessException {
        isFalse(expression, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static void isFalse(boolean expression) throws BussinessException {
        isFalse(expression, "[Assertion failed] - this expression must be false");
    }

    public static <X extends Throwable> void isNull(Object object, Supplier<X> errorSupplier) throws X {
        if (null != object) {
            throw errorSupplier.get();
        }
    }

    public static void isNull(Object object, String errorMsgTemplate, Object... params) throws BussinessException {
        isNull(object, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static void isNull(Object object) throws BussinessException {
        isNull(object, "[Assertion failed] - the object argument must be null");
    }

    // ----------------------------------------------------------------------------------------------------------- Check not null

    public static <T, X extends Throwable> T notNull(T object, Supplier<X> errorSupplier) throws X {
        if (null == object) {
            throw errorSupplier.get();
        }
        return object;
    }

    public static <T> T notNull(T object, String errorMsgTemplate, Object... params) throws BussinessException {
        return notNull(object, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static <T> T notNull(T object) throws BussinessException {
        return notNull(object, "[Assertion failed] - this argument is required; it must not be null");
    }

    // ----------------------------------------------------------------------------------------------------------- Check empty

    public static <T extends CharSequence, X extends Throwable> T notEmpty(T text, Supplier<X> errorSupplier) throws X {
        if (CharSequenceUtil.isBlank(text)) {
            throw errorSupplier.get();
        }
        return text;
    }

    public static <T extends CharSequence> T notEmpty(T text, String errorMsgTemplate, Object... params) throws BussinessException {
        return notEmpty(text, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static <T extends CharSequence> T notEmpty(T text) throws BussinessException {
        return notEmpty(text, "[Assertion failed] - this String argument must have length; it must not be null or empty");
    }

    public static <T extends CharSequence, X extends Throwable> T notBlank(T text, Supplier<X> errorMsgSupplier) throws X {
        if (CharSequenceUtil.isBlank(text)) {
            throw errorMsgSupplier.get();
        }
        return text;
    }

    public static <T extends CharSequence> T notBlank(T text, String errorMsgTemplate, Object... params) throws BussinessException {
        return notBlank(text, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static <T extends CharSequence> T notBlank(T text) throws BussinessException {
        return notBlank(text, "[Assertion failed] - this String argument must have text; it must not be null, empty, or blank");
    }

    public static <T extends CharSequence, X extends Throwable> T notContain(CharSequence textToSearch, T substring, Supplier<X> errorSupplier) throws X {
        if (CharSequenceUtil.contains(textToSearch, substring)) {
            throw errorSupplier.get();
        }
        return substring;
    }

    public static String notContain(String textToSearch, String substring, String errorMsgTemplate, Object... params) throws BussinessException {
        return notContain(textToSearch, substring, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static String notContain(String textToSearch, String substring) throws BussinessException {
        return notContain(textToSearch, substring, "[Assertion failed] - this String argument must not contain the substring [{}]", substring);
    }

    public static <T, X extends Throwable> T[] notEmpty(T[] array, Supplier<X> errorSupplier) throws X {
        if (ArrayUtil.isEmpty(array)) {
            throw errorSupplier.get();
        }
        return array;
    }

    public static <T> T[] notEmpty(T[] array, String errorMsgTemplate, Object... params) throws BussinessException {
        return notEmpty(array, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static <T> T[] notEmpty(T[] array) throws BussinessException {
        return notEmpty(array, "[Assertion failed] - this array must not be empty: it must contain at least 1 element");
    }

    public static <T, X extends Throwable> T[] noNullElements(T[] array, Supplier<X> errorSupplier) throws X {
        if (ArrayUtil.hasNull(array)) {
            throw errorSupplier.get();
        }
        return array;
    }

    public static <T> T[] noNullElements(T[] array, String errorMsgTemplate, Object... params) throws BussinessException {
        return noNullElements(array, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static <T> T[] noNullElements(T[] array) throws BussinessException {
        return noNullElements(array, "[Assertion failed] - this array must not contain any null elements");
    }

    public static <E, T extends Iterable<E>, X extends Throwable> T notEmpty(T collection, Supplier<X> errorSupplier) throws X {
        if (CollUtil.isEmpty(collection)) {
            throw errorSupplier.get();
        }
        return collection;
    }

    public static <E, T extends Iterable<E>> T notEmpty(T collection, String errorMsgTemplate, Object... params) throws BussinessException {
        return notEmpty(collection, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static <E, T extends Iterable<E>> T notEmpty(T collection) throws BussinessException {
        return notEmpty(collection, "[Assertion failed] - this collection must not be empty: it must contain at least 1 element");
    }

    public static <K, V, T extends Map<K, V>, X extends Throwable> T notEmpty(T map, Supplier<X> errorSupplier) throws X {
        if (MapUtil.isEmpty(map)) {
            throw errorSupplier.get();
        }
        return map;
    }

    public static <K, V, T extends Map<K, V>> T notEmpty(T map, String errorMsgTemplate, Object... params) throws BussinessException {
        return notEmpty(map, () -> new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params)));
    }

    public static <K, V, T extends Map<K, V>> T notEmpty(T map) throws BussinessException {
        return notEmpty(map, "[Assertion failed] - this map must not be empty; it must contain at least one entry");
    }

    public static <T> T isInstanceOf(Class<?> type, T obj) throws BussinessException {
        return isInstanceOf(type, obj, "Object [{}] is not instanceof [{}]", obj, type);
    }

    public static <T> T isInstanceOf(Class<?> type, T obj, String errorMsgTemplate, Object... params) throws BussinessException {
        notNull(type, "Type to check against must not be null");
        if (!type.isInstance(obj)) {
            throw new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params));
        }
        return obj;
    }

    public static void isAssignable(Class<?> superType, Class<?> subType) throws BussinessException {
        isAssignable(superType, subType, "{} is not assignable to {})", subType, superType);
    }

    public static void isAssignable(Class<?> superType, Class<?> subType, String errorMsgTemplate, Object... params) throws BussinessException {
        notNull(superType, "Type to check against must not be null");
        if (subType == null || !superType.isAssignableFrom(subType)) {
            throw new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params));
        }
    }

    public static void state(boolean expression, Supplier<String> errorMsgSupplier) throws BussinessException {
        if (!expression) {
            throw new BussinessException(errorMsgSupplier.get());
        }
    }

    public static void state(boolean expression, String errorMsgTemplate, Object... params) throws BussinessException {
        if (!expression) {
            throw new BussinessException(CharSequenceUtil.format(errorMsgTemplate, params));
        }
    }

    public static void state(boolean expression) throws BussinessException {
        state(expression, "[Assertion failed] - this state invariant must be true");
    }

    public static int checkIndex(int index, int size) throws BussinessException, IndexOutOfBoundsException {
        return checkIndex(index, size, "[Assertion failed]");
    }

    public static int checkIndex(int index, int size, String errorMsgTemplate, Object... params) throws BussinessException, IndexOutOfBoundsException {
        if (index < 0 || index >= size) {
            throw new IndexOutOfBoundsException(badIndexMsg(index, size, errorMsgTemplate, params));
        }
        return index;
    }

    public static int checkBetween(int value, int min, int max) throws BussinessException {
        if (value < min || value > max) {
            throw new BussinessException(CharSequenceUtil.format(LENGTH_FORMATTER_STR, min, max));
        }
        return value;
    }

    public static long checkBetween(long value, long min, long max) throws BussinessException {
        if (value < min || value > max) {
            throw new BussinessException(CharSequenceUtil.format(LENGTH_FORMATTER_STR, min, max));
        }
        return value;
    }

    public static double checkBetween(double value, double min, double max) throws BussinessException {
        if (value < min || value > max) {
            throw new BussinessException(CharSequenceUtil.format(LENGTH_FORMATTER_STR, min, max));
        }
        return value;
    }

    public static Number checkBetween(Number value, Number min, Number max) throws BussinessException {
        notNull(value);
        notNull(min);
        notNull(max);
        double valueDouble = value.doubleValue();
        double minDouble = min.doubleValue();
        double maxDouble = max.doubleValue();
        if (valueDouble < minDouble || valueDouble > maxDouble) {
            throw new BussinessException(CharSequenceUtil.format(LENGTH_FORMATTER_STR, min, max));
        }
        return value;
    }

    // -------------------------------------------------------------------------------------------------------------------------------------------- Private method start

    private static String badIndexMsg(int index, int size, String desc, Object... params) throws BussinessException {
        if (index < 0) {
            return CharSequenceUtil.format("{} ({}) must not be negative", CharSequenceUtil.format(desc, params), index);
        } else if (size < 0) {
            throw new BussinessException("negative size: " + size);
        } else { // index >= size
            return CharSequenceUtil.format("{} ({}) must be less than size ({})", CharSequenceUtil.format(desc, params), index, size);
        }
    }
}
