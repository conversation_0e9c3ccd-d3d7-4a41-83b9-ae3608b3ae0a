# 美姿姿 App API 生产环境配置

# 数据库配置 - 生产环境（通过环境变量配置）
spring.datasource.url=${DATABASE_URL:**************************************************}
spring.datasource.username=${DATABASE_USERNAME:postgres}
spring.datasource.password=${DATABASE_PASSWORD:postgres123}

# 连接池配置 - App API通常需要更多连接
spring.datasource.hikari.maximum-pool-size=${DB_POOL_SIZE:30}
spring.datasource.hikari.minimum-idle=${DB_MIN_IDLE:10}
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.leak-detection-threshold=60000

# Redis配置 - 生产环境
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.database=${REDIS_DATABASE:1}
spring.redis.timeout=5000ms

# 日志配置 - 生产环境
logging.level.root=${LOG_LEVEL:WARN}
logging.level.com.deepaic=${APP_LOG_LEVEL:INFO}
logging.level.com.baomidou.mybatisplus=WARN
logging.file.name=${LOGGING_FILE_NAME:/app/logs/app-api.log}
logging.logback.rollingpolicy.max-file-size=100MB
logging.logback.rollingpolicy.max-history=30

# 监控配置 - 生产环境
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when_authorized
management.metrics.export.prometheus.enabled=true

# 安全配置
security.jwt.secret=${JWT_SECRET:your-secret-key-change-in-production}
security.jwt.expiration=${JWT_EXPIRATION:86400}

# 多租户配置 - 基于Sa-Token Session
multitenant.enabled=${MULTITENANT_ENABLED:true}
multitenant.default-schema=${DEFAULT_TENANT_SCHEMA:public}
multitenant.auto-context-setup=${AUTO_CONTEXT_SETUP:true}

# 文件上传配置 - App通常需要处理更多文件
spring.servlet.multipart.max-file-size=${MAX_FILE_SIZE:50MB}
spring.servlet.multipart.max-request-size=${MAX_REQUEST_SIZE:100MB}

# 移动端特定配置
app.mobile.push.enabled=${PUSH_ENABLED:true}
app.mobile.push.provider=${PUSH_PROVIDER:firebase}
app.mobile.offline-sync.enabled=${OFFLINE_SYNC:true}
