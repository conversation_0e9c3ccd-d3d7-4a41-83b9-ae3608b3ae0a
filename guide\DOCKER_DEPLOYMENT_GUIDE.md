# 美姿姿 - Docker分布式部署完整指南

## 📋 概述

美姿姿项目现已支持灵活的Docker部署方案，包括：

- **单机部署**: 适用于开发环境和小规模部署
- **分布式部署**: 适用于生产环境，支持将组件部署到不同服务器

## 🏗️ 部署架构设计

### 组件分离策略

1. **基础设施层** (`docker-compose.infrastructure.yml`)
   - PostgreSQL 数据库
   - Redis 缓存
   - Nginx 负载均衡
   - 监控系统 (Prometheus + Grafana)
   - 日志系统 (ELK Stack)

2. **应用服务层**
   - Admin API (`docker-compose.admin-api.yml`) - 管理后台
   - App API (`docker-compose.app-api.yml`) - 移动应用
   - Client API (`docker-compose.client-api.yml`) - Web客户端

### 网络架构

```
Internet
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                    Nginx (负载均衡)                          │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Admin API     │  │    App API      │  │   Client API    │
│   (Port 8080)   │  │   (Port 8081)   │  │   (Port 8082)   │
└─────────────────┘  └─────────────────┘  └─────────────────┘
    │                    │                    │
    └────────────────────┼────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────┐
│                  共享基础设施                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ PostgreSQL  │  │    Redis    │  │   监控 & 日志系统    │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 📁 新增文件说明

### Docker Compose 配置文件

| 文件名 | 用途 | 部署场景 |
|--------|------|----------|
| `docker-compose.yml` | 完整部署配置 | 单机开发环境 |
| `docker-compose.infrastructure.yml` | 基础设施组件 | 基础设施服务器 |
| `docker-compose.admin-api.yml` | 管理后台API | 管理服务器 |
| `docker-compose.app-api.yml` | 移动应用API | 应用服务器 |
| `docker-compose.client-api.yml` | 客户端API | 客户端服务器 |

### 配置和脚本文件

| 文件名 | 用途 |
|--------|------|
| `.env.example` | 环境变量配置模板 |
| `deploy.sh` | Linux/Mac部署脚本 |
| `deploy.bat` | Windows部署脚本 |

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd beautiful-posture/docker

# 配置环境变量
cp .env.example .env
vim .env  # 编辑配置
```

### 2. 单机部署（开发环境）

```bash
# 一键部署所有服务
./deploy.sh all

# 查看服务状态
./deploy.sh --status all

# 查看日志
./deploy.sh --logs all
```

### 3. 分布式部署（生产环境）

#### 基础设施服务器

```bash
# 部署基础组件
./deploy.sh infrastructure

# 启用监控系统
./deploy.sh infrastructure -p monitoring

# 启用日志系统
./deploy.sh infrastructure -p logging
```

#### API服务器

```bash
# 服务器A: 部署Admin API
./deploy.sh admin-api

# 服务器B: 部署App API
./deploy.sh app-api

# 服务器C: 部署Client API
./deploy.sh client-api
```

## ⚙️ 环境变量配置

### 核心配置项

```bash
# 数据库配置
DATABASE_URL=********************************************************
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your-secure-password

# Redis配置
REDIS_HOST=redis-server
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# 服务端口
ADMIN_API_PORT=8080
APP_API_PORT=8081
CLIENT_API_PORT=8082

# 安全配置
JWT_SECRET=your-jwt-secret-key-change-in-production
```

### 高级配置

```bash
# JVM调优
JAVA_OPTS=-Xms1024m -Xmx2048m -XX:+UseG1GC

# 数据库连接池
DB_POOL_SIZE=30
DB_MIN_IDLE=10

# 文件上传限制
MAX_FILE_SIZE=50MB
MAX_REQUEST_SIZE=100MB

# CORS配置（Client API）
CORS_ORIGINS=https://your-domain.com
```

## 🔧 运维操作

### 服务管理

```bash
# 启动服务
./deploy.sh <service>

# 停止服务
./deploy.sh --stop <service>

# 重启服务
./deploy.sh --restart <service>

# 查看状态
./deploy.sh --status <service>

# 查看日志
./deploy.sh --logs <service>
```

### 镜像管理

```bash
# 强制重新构建
./deploy.sh <service> -f

# 清理未使用的镜像
docker image prune -f

# 查看镜像大小
docker images | grep beautiful-posture
```

### 数据备份

```bash
# 备份PostgreSQL数据
docker exec beautiful-posture-postgres pg_dump -U postgres beautiful_posture > backup.sql

# 备份Redis数据
docker exec beautiful-posture-redis redis-cli BGSAVE
```

## 📊 监控和日志

### 监控系统

启用监控profile后可访问：

- **Prometheus**: http://server:9090
- **Grafana**: http://server:3000 (admin/admin123)

### 日志系统

启用logging profile后可访问：

- **Kibana**: http://server:5601

### 健康检查

所有API服务都提供健康检查端点：

```bash
# Admin API健康检查
curl http://server:8080/actuator/health

# App API健康检查
curl http://server:8081/actuator/health

# Client API健康检查
curl http://server:8082/actuator/health
```

## 🔒 安全建议

### 生产环境配置

1. **修改默认密码**
   ```bash
   POSTGRES_PASSWORD=strong-password-here
   REDIS_PASSWORD=strong-redis-password
   JWT_SECRET=very-long-random-secret-key
   ```

2. **网络安全**
   - 使用防火墙限制端口访问
   - 配置SSL/TLS证书
   - 启用Redis密码认证

3. **数据安全**
   - 定期备份数据库
   - 启用数据库连接加密
   - 配置日志轮转

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8080
   
   # 检查Docker网络
   docker network ls
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker exec beautiful-posture-postgres pg_isready
   
   # 查看数据库日志
   docker logs beautiful-posture-postgres
   ```

3. **Redis连接失败**
   ```bash
   # 测试Redis连接
   docker exec beautiful-posture-redis redis-cli ping
   ```

### 日志查看

```bash
# 查看特定服务日志
./deploy.sh --logs admin-api

# 查看最近100行日志
docker logs --tail 100 beautiful-posture-admin-api

# 实时跟踪日志
docker logs -f beautiful-posture-admin-api
```

## 📈 性能优化

### JVM调优

根据服务器配置调整JVM参数：

```bash
# 4GB内存服务器
JAVA_OPTS=-Xms1024m -Xmx2048m -XX:+UseG1GC

# 8GB内存服务器
JAVA_OPTS=-Xms2048m -Xmx4096m -XX:+UseG1GC
```

### 数据库优化

```bash
# 调整连接池大小
DB_POOL_SIZE=50
DB_MIN_IDLE=20
```

### Redis优化

```bash
# 启用Redis持久化
REDIS_SAVE_ENABLED=true
```

---

**美姿姿团队** - 让健康管理更美好 💪
