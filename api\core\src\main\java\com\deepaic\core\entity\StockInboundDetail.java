package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_stock_inbound_detail")
public class StockInboundDetail extends BaseEntity {

    /**
     * 主表主键
     */
    private Long stockInboundId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 有效期
     */
    private LocalDate expiryDate;

    private Short receiveQuantity;

    private String receiveUnitName;

    /**
     * 进货单位id
     */
    private Long receiveUnitId;

    /**
     * 标准单位数量
     */
    private Short standardQuantity;

    /**
     * 标准单位id
     */
    private Long standartUnitId;

    /**
     * 标准单位名称
     */
    private String standartUnitName;

    /**
     * 消费单位id
     */
    private Long consumeUnitId;

    /**
     * 消费单位名称
     */
    private String consumeUnitName;

    /**
     * 消费单位数量
     */
    private Short consumeQuantity;
}
