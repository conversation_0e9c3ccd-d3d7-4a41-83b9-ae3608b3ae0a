# 多租户会员登录解决方案

## 🎯 问题背景

在多租户架构中，会员信息存储在各租户的独立schema中，但在登录时我们还不知道用户属于哪个租户，这形成了"鸡生蛋、蛋生鸡"的循环依赖问题：

- **问题**: 需要知道租户才能查询会员信息
- **困境**: 需要查询会员信息才能知道租户
- **挑战**: 如何在不知道租户的情况下确定用户身份

## 💡 解决方案架构

### 核心思路：公共Schema登录映射表

我们在公共schema中创建一个登录标识映射表，存储登录标识（如微信OpenID、手机号）与租户的对应关系。

```
登录流程：
1. 用户提供登录标识（OpenID/手机号）
2. 在公共schema中查询登录映射表
3. 确定用户所属租户
4. 切换到租户schema查询完整会员信息
5. 完成登录认证
```

## 🗄️ 数据库设计

### 1. 公共Schema - 登录映射表

```sql
-- 会员登录标识映射表 - 存储在公共schema中
CREATE TABLE public.pub_member_login_mapping (
    id BIGSERIAL PRIMARY KEY,
    login_type INTEGER NOT NULL,           -- 登录类型(1:微信OpenID 2:手机号 3:邮箱)
    login_identifier VARCHAR(200) NOT NULL, -- 登录标识
    tenant_code VARCHAR(50) NOT NULL,      -- 租户编码
    member_id BIGINT NOT NULL,             -- 租户内会员ID
    member_no VARCHAR(50) NOT NULL,        -- 会员编号
    status INTEGER DEFAULT 1,              -- 状态
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);

-- 唯一索引：确保登录标识全局唯一
CREATE UNIQUE INDEX uk_pub_member_login_mapping 
ON public.pub_member_login_mapping(login_type, login_identifier) 
WHERE deleted = 0;
```

### 2. 租户Schema - 会员表

```sql
-- 会员表 - 存储在各租户schema中
CREATE TABLE {tenant_schema}.sys_member (
    id BIGSERIAL PRIMARY KEY,
    member_no VARCHAR(50) NOT NULL,        -- 会员编号
    wx_openid VARCHAR(100),                -- 微信OpenID
    phone VARCHAR(20),                     -- 手机号
    nickname VARCHAR(50),                  -- 昵称
    -- 其他会员字段...
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);
```

## 🔧 核心组件实现

### 1. 登录映射实体

```java
@TableName("pub_member_login_mapping")
public class MemberLoginMapping extends BaseEntity {
    private Integer loginType;           // 登录类型
    private String loginIdentifier;      // 登录标识
    private String tenantCode;          // 租户编码
    private Long memberId;              // 会员ID
    private String memberNo;            // 会员编号
    private Integer status;             // 状态
    
    // 登录类型常量
    public static final int LOGIN_TYPE_WECHAT_OPENID = 1; // 微信OpenID
    public static final int LOGIN_TYPE_PHONE = 2;         // 手机号
    public static final int LOGIN_TYPE_EMAIL = 3;         // 邮箱
}
```

### 2. 租户确定策略

```java
public interface TenantDeterminationStrategy {
    /**
     * 根据登录信息确定租户
     */
    String determineTenant(MemberDTO.MemberLoginDTO loginDTO, HttpServletRequest request);
    
    int getPriority();
}

@Component
public class DefaultTenantDeterminationStrategy implements TenantDeterminationStrategy {
    @Override
    public String determineTenant(MemberDTO.MemberLoginDTO loginDTO, HttpServletRequest request) {
        // 策略1: 从请求参数获取
        String tenant = request.getParameter("tenant");
        if (StringUtils.hasText(tenant)) return tenant;
        
        // 策略2: 从域名解析
        String domain = request.getServerName();
        if (domain.contains(".")) {
            String subdomain = domain.split("\\.")[0];
            if (!isSystemSubdomain(subdomain)) return subdomain;
        }
        
        // 策略3: 从推荐码获取
        if (StringUtils.hasText(loginDTO.getReferrerNo())) {
            // 从推荐码中解析租户信息
            return extractTenantFromReferrer(loginDTO.getReferrerNo());
        }
        
        // 默认租户
        return "default";
    }
}
```

### 3. 会员认证服务

```java
@Service
public class MemberAuthService {
    
    /**
     * 微信小程序登录
     */
    @Transactional
    public MemberDTO.MemberLoginResponse wechatMiniLogin(
            MemberDTO.MemberLoginDTO loginDTO, HttpServletRequest request) {
        
        // 1. 获取微信OpenID
        WechatAuthResult authResult = wechatMiniProgramService.getOpenidByCode(loginDTO.getCode());
        
        // 2. 在公共schema中查询登录映射
        MemberLoginMapping loginMapping = findLoginMappingInPublicSchema(
            MemberLoginMapping.LOGIN_TYPE_WECHAT_OPENID, 
            authResult.getOpenid()
        );
        
        MemberDTO member;
        boolean isNewUser = false;
        
        if (loginMapping != null) {
            // 3a. 已存在用户，切换到租户schema查询详细信息
            member = getMemberFromTenantSchema(loginMapping);
        } else {
            // 3b. 新用户，确定租户后创建
            member = createNewMemberWithMapping(authResult, loginDTO, request);
            isNewUser = true;
        }
        
        // 4. 执行登录
        return performLogin(member, MemberLoginLog.LOGIN_TYPE_WECHAT, 
                          loginDTO.getPlatform(), request, isNewUser);
    }
    
    /**
     * 在公共Schema中查询登录映射
     */
    private MemberLoginMapping findLoginMappingInPublicSchema(Integer loginType, String loginIdentifier) {
        return tenantContextService.runInPublicSchema(() -> {
            return memberLoginMappingMapper.selectByLoginIdentifier(loginType, loginIdentifier);
        });
    }
    
    /**
     * 从租户Schema中获取会员详细信息
     */
    private MemberDTO getMemberFromTenantSchema(MemberLoginMapping loginMapping) {
        return tenantContextService.runInTenantSchema(loginMapping.getTenantCode(), () -> {
            return memberMapper.selectMemberById(loginMapping.getMemberId());
        });
    }
}
```

### 4. 租户上下文服务

```java
@Service
public class TenantContextService {
    
    // ThreadLocal用于临时存储schema信息
    private static final ThreadLocal<String> TEMP_SCHEMA = new ThreadLocal<>();
    
    /**
     * 在公共Schema中执行操作（不依赖登录状态）
     */
    public <T> T runInPublicSchema(Supplier<T> operation) {
        String originalSchema = TEMP_SCHEMA.get();
        
        try {
            TEMP_SCHEMA.set("public");
            return operation.get();
        } finally {
            if (originalSchema != null) {
                TEMP_SCHEMA.set(originalSchema);
            } else {
                TEMP_SCHEMA.remove();
            }
        }
    }
    
    /**
     * 在指定租户Schema中执行操作
     */
    public <T> T runInTenantSchema(String tenantCode, Supplier<T> operation) {
        String originalSchema = TEMP_SCHEMA.get();
        
        try {
            Tenant tenant = tenantMapper.selectByTenantCode(tenantCode);
            if (tenant == null) {
                throw new RuntimeException("租户不存在: " + tenantCode);
            }
            
            TEMP_SCHEMA.set(tenant.getSchemaName());
            return operation.get();
        } finally {
            if (originalSchema != null) {
                TEMP_SCHEMA.set(originalSchema);
            } else {
                TEMP_SCHEMA.remove();
            }
        }
    }
    
    /**
     * 获取当前有效的schema
     */
    public static String getCurrentEffectiveSchema() {
        // 优先使用ThreadLocal中的临时schema
        String tempSchema = TEMP_SCHEMA.get();
        if (tempSchema != null) {
            return tempSchema;
        }
        
        // 然后使用SaToken中的schema
        return SaTokenTenantContext.getTenantSchema();
    }
}
```

## 🔄 完整登录流程

### 微信小程序登录流程

```mermaid
sequenceDiagram
    participant 小程序 as 小程序
    participant API as app-api
    participant 微信 as 微信API
    participant 公共DB as 公共Schema
    participant 租户DB as 租户Schema
    
    小程序->>API: 发送授权码
    API->>微信: 获取OpenID
    微信-->>API: 返回OpenID
    
    API->>公共DB: 查询登录映射表
    alt 用户已存在
        公共DB-->>API: 返回租户信息
        API->>租户DB: 查询会员详情
        租户DB-->>API: 返回会员信息
    else 新用户
        API->>API: 确定租户
        API->>租户DB: 创建会员
        API->>公共DB: 创建登录映射
    end
    
    API->>API: 生成访问令牌
    API-->>小程序: 返回登录结果
```

### 手机号登录流程

```mermaid
sequenceDiagram
    participant 小程序 as 小程序
    participant API as app-api
    participant 短信 as 短信服务
    participant 公共DB as 公共Schema
    participant 租户DB as 租户Schema
    
    小程序->>API: 发送手机号+验证码
    API->>短信: 验证短信验证码
    短信-->>API: 验证结果
    
    API->>公共DB: 查询登录映射表
    alt 用户已存在
        公共DB-->>API: 返回租户信息
        API->>租户DB: 查询会员详情
        租户DB-->>API: 返回会员信息
    else 新用户
        API->>API: 确定租户
        API->>租户DB: 创建会员
        API->>公共DB: 创建登录映射
    end
    
    API->>API: 生成访问令牌
    API-->>小程序: 返回登录结果
```

## 🎯 租户确定策略

### 支持的确定方式

1. **请求参数**: `?tenant=abc` 或 `?tenantCode=abc`
2. **子域名**: `abc.domain.com` → 租户 `abc`
3. **请求头**: `X-Tenant-Code: abc`
4. **推荐码**: 从推荐人信息中获取租户
5. **默认租户**: 使用系统默认租户

### 策略优先级

```java
// 优先级从高到低
1. 请求参数中的租户标识
2. 域名中的子域名
3. 推荐码中的租户信息
4. 请求头中的租户标识
5. 默认租户
```

## ✅ 方案优势

### 1. **解决核心问题**
- 彻底解决多租户环境下的会员登录问题
- 支持跨租户的登录标识唯一性

### 2. **架构清晰**
- 公共schema存储映射关系
- 租户schema存储详细信息
- 职责分离明确

### 3. **扩展性强**
- 支持多种登录方式（微信、手机号、邮箱等）
- 支持多种租户确定策略
- 易于添加新的登录方式

### 4. **性能优化**
- 登录映射表索引优化
- ThreadLocal避免重复查询
- 最小化跨schema操作

### 5. **安全可靠**
- 完整的数据隔离
- 登录标识全局唯一
- 支持软删除和状态管理

## 🚀 使用示例

### 小程序登录调用

```javascript
// 微信小程序登录
wx.login({
  success: (res) => {
    wx.request({
      url: 'https://api.example.com/api/app/auth/wechat-mini-login',
      method: 'POST',
      data: {
        code: res.code,
        platform: 1,
        userInfo: {
          nickName: '用户昵称',
          avatarUrl: '头像URL'
        }
      },
      header: {
        'X-Tenant-Code': 'abc' // 可选：指定租户
      },
      success: (response) => {
        if (response.data.code === 200) {
          // 保存令牌
          wx.setStorageSync('accessToken', response.data.data.accessToken);
        }
      }
    });
  }
});
```

### API响应格式

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refreshToken": "refresh_123456_1640995200000",
    "expiresIn": 86400,
    "memberInfo": {
      "id": 1,
      "memberNo": "M20231201001",
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "levelName": "普通会员",
      "points": 0,
      "balance": 0
    },
    "isNewUser": true
  }
}
```

## 🎉 总结

这个解决方案完美解决了多租户环境下的会员登录问题：

1. **技术创新**: 使用公共schema登录映射表突破循环依赖
2. **架构优雅**: 分层设计，职责清晰，易于维护
3. **功能完整**: 支持多种登录方式和租户确定策略
4. **性能优秀**: 最小化数据库操作，优化查询性能
5. **安全可靠**: 完整的数据隔离和权限控制

现在系统可以完美支持多租户环境下的会员登录，每个租户的会员都可以通过小程序顺利登录并使用服务！🎊
