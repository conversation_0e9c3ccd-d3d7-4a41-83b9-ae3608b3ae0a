package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.deepaic.core.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("pub_user_account")
public class UserAccount extends BaseEntity {

    private String tenantCode;

    private String username;

    private String password;

    private String phone;

    private String wechatId;

    private LocalDateTime lastLoginTime;

    private String lastLoginIp;

    private Integer loginFailCount;

    private Boolean locked;

    private LocalDateTime lockTime;

    private Long userId;

    private Short status;




    // 账户类型常量
    public static final int TYPE_ADMIN = 1;           // 管理员
    public static final int TYPE_NORMAL_USER = 2;     // 普通用户

    // 状态常量
    public static enum Status {
        DISABLED((short)0),
        ENABLED((short)1);

        private final Short value;

        Status(short value) {
            this.value = value;
        }
    }



    public boolean canLogin() {
        return Status.ENABLED.equals(this.status) && Boolean.FALSE.equals(this.locked);
    }

    public boolean isEnabled() {
        return Status.ENABLED.value.equals(this.status);
    }

    public boolean isLocked() {
        return Boolean.TRUE.equals(this.locked);
    }
    
}
