package com.deepaic.core.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@RestControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    protected static final Logger LOG = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception e) {
        LOG.error(e.getMessage(), e);
        return ResponseEntity.internalServerError().body(e.getMessage());
    }

    @ExceptionHandler(BussinessException.class)
    public ResponseEntity<String> handleCommonException(BussinessException e) {
        LOG.error(e.getMessage(), e);
        return ResponseEntity.badRequest().body(e.getMessage());
    }

    /**
     * 处理Sa-Token的NotLoginException异常
     */
    @ExceptionHandler(NotLoginException.class)
    public ResponseEntity<String> handleNotLoginException(NotLoginException e) {
        LOG.warn("用户未登录: {}", e.getMessage());
        return new ResponseEntity<>(e.getMessage(), HttpStatus.UNAUTHORIZED);
    }

    /**
     * 处理Sa-Token的NotRoleException异常
     */
    @ExceptionHandler(NotRoleException.class)
    public ResponseEntity<String> handleNotRoleException(NotRoleException e) {
        LOG.warn("角色权限不足: {}", e.getMessage());
        return new ResponseEntity<>(e.getMessage(), HttpStatus.FORBIDDEN);
    }

    /**
     * 处理Sa-Token的NotPermissionException异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public ResponseEntity<String> handleNotPermissionException(NotPermissionException e) {
        LOG.warn("权限不足: {}", e.getMessage());
        return new ResponseEntity<>(e.getMessage(), HttpStatus.FORBIDDEN);
    }
}
