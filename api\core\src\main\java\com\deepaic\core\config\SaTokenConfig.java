package com.deepaic.core.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器，打开注解式鉴权功能
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，定义详细认证规则
        registry.addInterceptor(new SaInterceptor(handler -> {
            // 指定一条 match 规则
            SaRouter
                .match("/**")    // 拦截的 path 列表，可以写多个 */
                .notMatch("/public/**")    // 排除掉的 path 列表，可以写多个
                .notMatch("/api/auth/captcha")
                .notMatch("/api/public/**")
                .notMatch("/doc.html")          // 排除 Swagger 文档
                .notMatch("/swagger-ui/**")
                .notMatch("/swagger-resources/**")
                .notMatch("/v3/api-docs/**")
                .notMatch("/webjars/**")
                .notMatch("/favicon.ico")
                .notMatch("/error")
                .notMatch("/actuator/**")       // 排除监控端点
                .check(r -> StpUtil.checkLogin());        // 要执行的校验动作，可以写完整的 lambda 表达式

        })).addPathPatterns("/**");
    }
}
