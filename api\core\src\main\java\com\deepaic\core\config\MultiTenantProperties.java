package com.deepaic.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 多租户配置属性
 * 统一管理多租户相关的配置项
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "multitenant")
public class MultiTenantProperties {

    /**
     * 是否启用多租户功能
     */
    private boolean enabled = true;

    /**
     * 默认Schema名称
     */
    private String defaultSchema = "public";

    /**
     * 是否自动设置租户上下文
     */
    private boolean autoContextSetup = true;

    /**
     * 租户信息缓存配置
     */
    private Cache cache = new Cache();

    /**
     * Schema管理配置
     */
    private Schema schema = new Schema();

    /**
     * 监控配置
     */
    private Monitor monitor = new Monitor();

    @Data
    public static class Cache {
        /**
         * 是否启用缓存
         */
        private boolean enabled = true;

        /**
         * 缓存过期时间（秒）
         */
        private long expireTime = 3600;

        /**
         * 最大缓存数量
         */
        private int maxSize = 1000;
    }

    @Data
    public static class Schema {
        /**
         * 是否自动创建租户Schema
         */
        private boolean autoCreate = false;

        /**
         * 是否自动删除租户Schema
         */
        private boolean autoDelete = false;

        /**
         * Schema命名前缀
         */
        private String prefix = "tenant_";

        /**
         * 初始化SQL脚本路径
         */
        private String initScriptPath = "classpath:sql/tenant-init.sql";
    }

    @Data
    public static class Monitor {
        /**
         * 是否启用监控
         */
        private boolean enabled = true;

        /**
         * 是否记录Schema切换日志
         */
        private boolean logSchemaSwitch = true;

        /**
         * 是否记录性能指标
         */
        private boolean logPerformance = false;

        /**
         * 慢查询阈值（毫秒）
         */
        private long slowQueryThreshold = 1000;
    }
}
