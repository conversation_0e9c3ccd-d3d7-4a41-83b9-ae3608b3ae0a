package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@TableName("pub_user_account")
public class PubUserAccount extends BaseEntity {

    private String tenantCode;

    private Integer loginType;

    private String loginIdentifier;

    private String username;

    private String password;

    private String email;

    private String phone;

    private String realName;

    private String avatar;

    private String wechatOpenid;

    private String wechatUnionid;

    private Integer accountType;

    private Integer status;

    private LocalDateTime lastLoginTime;

    private Object lastLoginIp;

    private Integer loginFailCount;

    private LocalDateTime lockTime;

    private LocalDateTime passwordExpireTime;

    private LocalDateTime bindTime;

    private Long sysUserId;

    private Boolean isPrimary;

    private String remark;
}
