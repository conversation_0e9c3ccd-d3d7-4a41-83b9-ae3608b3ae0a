package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("pub_tenant")
public class Tenant extends BaseEntity {

    private String tenantCode;

    private String tenantName;

    private String schemaName;

    private Short tenantType;

    private Short status;

    private String contactName;

    private String contactPhone;

    private String contactEmail;

    private String address;

    private LocalDateTime expireTime;

    private Integer maxUsers;

    private Long maxStorage;

    private Object features;

    private Object settings;

    private String remark;



    // 租户类型常量
    public static final short TYPE_TRIAL = 1;       // 试用
    public static final short TYPE_STANDARD = 2;    // 标准
    public static final short TYPE_PROFESSIONAL = 3; // 专业
    public static final short TYPE_ENTERPRISE = 4;  // 企业

    // 租户状态常量
    public static final short STATUS_DISABLED = 0;  // 禁用
    public static final short STATUS_ENABLED = 1;   // 启用
    public static final short STATUS_SUSPENDED = 2; // 暂停
    public static final short STATUS_EXPIRED = 3;   // 过期

    /**
     * 检查租户是否启用
     */
    public boolean isEnabled() {
        return Short.valueOf(STATUS_ENABLED).equals(this.status);
    }

    /**
     * 检查租户是否过期
     */
    public boolean isExpired() {
        return Short.valueOf(STATUS_EXPIRED).equals(this.status) ||
                (expireTime != null && expireTime.isBefore(LocalDateTime.now()));
    }

    /**
     * 检查是否在试用期
     */
    public boolean isInTrial() {
        return Short.valueOf(TYPE_TRIAL).equals(this.tenantType);
    }

    /**
     * 检查用户数是否超限
     */
    public boolean isUserLimitExceeded() {
        // 由于当前表结构中没有currentUsers字段，这里返回false
        // 实际使用时需要通过查询用户表来获取当前用户数
        return false;
    }

    /**
     * 检查存储空间是否超限
     */
    public boolean isStorageLimitExceeded() {
        // 由于当前表结构中没有storageUsed字段，这里返回false
        // 实际使用时需要通过其他方式获取已使用存储空间
        return false;
    }
}
