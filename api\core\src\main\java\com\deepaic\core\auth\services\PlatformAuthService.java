package com.deepaic.core.auth.services;

import com.deepaic.core.auth.interfaces.AuthenticationService;
import com.deepaic.core.auth.interfaces.TokenService;
import com.deepaic.core.auth.models.AuthRequest;
import com.deepaic.core.auth.models.AuthResponse;
import com.deepaic.core.auth.models.UserPrincipal;
import com.deepaic.core.entity.PlatformAccount;
import com.deepaic.core.exception.BussinessException;
import com.deepaic.core.mapper.PlatformAccountMapper;
import com.deepaic.core.tenant.TenantContextService;
import com.deepaic.core.util.PasswordUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 平台用户认证服务
 * 处理SaaS平台管理员的登录认证
 * 平台用户使用public schema，不需要租户上下文
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformAuthService implements AuthenticationService {

    private final PlatformAccountMapper platformAccountMapper;
    private final TokenService tokenService;
    private final TenantContextService tenantContextService;
    private final PasswordUtil passwordUtil;

    @Override
    public AuthResponse authenticate(AuthRequest request) throws BussinessException {
        log.info("平台用户认证开始: username={}", request.getUsername());
        
        PlatformAccount account = null;
        switch (request.getAuthenticationType()) {
            case USERNAME_PASSWORD:
                account = authenticateByUsernamePassword(request);
                break;
            case EMAIL_PASSWORD:
                account = authenticateByEmailPassword(request);
                break;
            default:
                throw new BussinessException("平台用户不支持的认证类型: " + request.getAuthenticationType());
        }

        // 检查账户状态
        checkAccountHealth(account);

        // 设置平台用户上下文（使用新的方法）
        tenantContextService.setTenantContextForUser(account.getId(), UserPrincipal.UserType.PLATFORM_ACCOUNT);

        // 创建用户主体
        UserPrincipal userPrincipal = createUserPrincipal(account);

        // 生成令牌
        String accessToken = tokenService.generateAccessToken(userPrincipal);
        Long expiresIn = tokenService.getTokenExpiration(accessToken);

        // 更新最后登录信息
        updateLastLoginInfo(account, request.getClientIp());

        log.info("平台用户登录成功: username={}, accountId={}", 
                request.getUsername(), account.getId());

        return AuthResponse.success(accessToken, expiresIn, userPrincipal);
    }

    /**
     * 用户名密码认证
     */
    private PlatformAccount authenticateByUsernamePassword(AuthRequest request) throws BussinessException {
        PlatformAccount account = platformAccountMapper.selectByUsername(request.getUsername());
        if (account == null) {
            throw new BussinessException("用户名或密码错误");
        }

        // 验证密码
        if (!passwordUtil.matches(request.getPassword(), account.getPassword())) {
            // 增加登录失败次数
            incrementLoginFailCount(account);
            throw new BussinessException("用户名或密码错误");
        }

        return account;
    }

    /**
     * 邮箱密码认证
     */
    private PlatformAccount authenticateByEmailPassword(AuthRequest request) throws BussinessException {
        PlatformAccount account = platformAccountMapper.selectByEmail(request.getUsername()); // username字段存储邮箱
        if (account == null) {
            throw new BussinessException("邮箱或密码错误");
        }

        // 验证密码
        if (!passwordUtil.matches(request.getPassword(), account.getPassword())) {
            // 增加登录失败次数
            incrementLoginFailCount(account);
            throw new BussinessException("邮箱或密码错误");
        }

        return account;
    }

    /**
     * 检查账户健康状态
     */
    private void checkAccountHealth(PlatformAccount account) throws BussinessException {
        if (account.getStatus() != 1) {
            throw new BussinessException("账户已被禁用");
        }

        // 检查密码是否过期
        if (account.getPasswordExpireTime() != null && 
            account.getPasswordExpireTime().isBefore(LocalDateTime.now())) {
            throw new BussinessException("密码已过期，请联系管理员重置");
        }
    }

    /**
     * 增加登录失败次数
     */
    private void incrementLoginFailCount(PlatformAccount account) {
        account.setLoginFailCount((account.getLoginFailCount() == null ? 0 : account.getLoginFailCount()) + 1);
        account.setUpdatedAt(LocalDateTime.now());
        platformAccountMapper.updateById(account);
        
        // 检查是否需要锁定账户
        if (account.getLoginFailCount() >= 5) {
            account.setLockTime(LocalDateTime.now());
            platformAccountMapper.updateById(account);
        }
    }

    /**
     * 更新最后登录信息
     */
    private void updateLastLoginInfo(PlatformAccount account, String clientIp) {
        account.setLastLoginTime(LocalDateTime.now());
        account.setLastLoginIp(clientIp);
        account.setLoginFailCount(0); // 重置失败次数
        account.setUpdatedAt(LocalDateTime.now());
        platformAccountMapper.updateById(account);
    }

    /**
     * 创建用户主体
     */
    private UserPrincipal createUserPrincipal(PlatformAccount account) {
        return UserPrincipal.builder()
                .accountId(account.getId())
                .userId(account.getId()) // 平台用户的userId就是accountId
                .userType(UserPrincipal.UserType.PLATFORM_ACCOUNT)
                .username(account.getUsername())
                .realName(account.getRealName())
                .phone(account.getPhone())
                .avatar(account.getAvatar())
                .tenantCode(null) // 平台用户不属于任何租户
                .tenantName("SaaS平台")
                .authorities(buildAuthorities(account))
                .roles(buildRoles(account))
                .status(UserPrincipal.AccountStatus.ENABLED)
                .lastLoginTime(account.getLastLoginTime())
                .build();
    }

    /**
     * 构建权限列表
     */
    private List<String> buildAuthorities(PlatformAccount account) {
        List<String> authorities = new ArrayList<>();
        
        // 根据权限级别设置权限
        Integer permissionLevel = account.getPermissionLevel();
        if (permissionLevel != null) {
            switch (permissionLevel) {
                case 3: // 超级管理员
                    authorities.add("platform:super:admin");
                    authorities.add("platform:tenant:manage");
                    authorities.add("platform:user:manage");
                    authorities.add("platform:system:config");
                    break;
                case 2: // 高级管理员
                    authorities.add("platform:tenant:manage");
                    authorities.add("platform:user:manage");
                    break;
                case 1: // 普通管理员
                    authorities.add("platform:tenant:view");
                    authorities.add("platform:user:view");
                    break;
            }
        }
        
        return authorities;
    }

    /**
     * 构建角色列表
     */
    private List<String> buildRoles(PlatformAccount account) {
        List<String> roles = new ArrayList<>();
        
        Integer permissionLevel = account.getPermissionLevel();
        if (permissionLevel != null) {
            switch (permissionLevel) {
                case 3:
                    roles.add("SUPER_ADMIN");
                    break;
                case 2:
                    roles.add("PLATFORM_ADMIN");
                    break;
                case 1:
                    roles.add("PLATFORM_USER");
                    break;
            }
        }
        
        return roles;
    }

    @Override
    public boolean logout(String userId) {
        try {
            // 清除租户上下文
            tenantContextService.clearTenantContext();
            return true;
        } catch (Exception e) {
            log.error("平台用户登出失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    public AuthResponse refreshToken(String refreshToken) {
        throw new UnsupportedOperationException("平台用户暂不支持刷新令牌");
    }

    @Override
    public UserPrincipal validateToken(String token) {
        return tokenService.validateToken(token);
    }

    @Override
    public UserPrincipal getCurrentUser() {
        return tokenService.getCurrentUser();
    }

    @Override
    public boolean isAuthenticated() {
        return tokenService.isAuthenticated();
    }

    @Override
    public UserPrincipal.UserType getUserType() {
        return UserPrincipal.UserType.PLATFORM_ACCOUNT;
    }
}
