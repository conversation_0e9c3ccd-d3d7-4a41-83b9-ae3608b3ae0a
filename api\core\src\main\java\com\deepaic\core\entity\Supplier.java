package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 供应商
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_supplier")
public class Supplier extends BaseEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系人电话
     */
    private String contractPhone;

    /**
     * 联系人地址
     */
    private String contractAddress;

    private Short sort;

    private Short status;

    // 状态常量
    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_NORMAL = 1;   // 正常
}
