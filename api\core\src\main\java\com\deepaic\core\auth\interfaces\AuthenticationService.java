package com.deepaic.core.auth.interfaces;

import com.deepaic.core.auth.models.AuthRequest;
import com.deepaic.core.auth.models.AuthResponse;
import com.deepaic.core.auth.models.UserPrincipal;
import com.deepaic.core.exception.BussinessException;


/**
 * 统一认证服务接口
 * 定义所有认证服务的通用方法
 *
 * <AUTHOR>
 */
public interface AuthenticationService {

    /**
     * 用户认证
     *
     * @param request 认证请求
     * @param httpRequest HTTP请求
     * @return 认证响应
     * @throws BussinessException 
     */
    AuthResponse authenticate(AuthRequest request) throws BussinessException;

    /**
     * 用户登出
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean logout(String userId);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 认证响应
     */
    AuthResponse refreshToken(String refreshToken);

    /**
     * 验证令牌
     *
     * @param token 访问令牌
     * @return 用户主体信息
     */
    UserPrincipal validateToken(String token);

    /**
     * 获取当前用户
     *
     * @return 用户主体信息
     */
    UserPrincipal getCurrentUser();

    /**
     * 检查是否已登录
     *
     * @return 是否已登录
     */
    boolean isAuthenticated();


    UserPrincipal.UserType getUserType();

}
