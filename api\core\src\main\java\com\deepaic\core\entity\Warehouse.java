package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 仓库
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_warehouse")
public class Warehouse extends BaseEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 排序
     */
    private Short sort;

    /**
     * 状态1启用| 0禁用
     */
    private Short status;

    private String address;

    // 状态常量
    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_NORMAL = 1;   // 正常
}
