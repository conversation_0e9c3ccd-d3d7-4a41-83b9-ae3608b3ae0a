# 美姿姿 API 数据库配置指南

## 📋 概述

已为所有API模块配置了完整的PostgreSQL数据库连接，包括开发、Docker和生产环境的配置。

## 🏗️ 配置结构

### 配置文件层次

每个API模块都包含以下配置文件：

```
src/main/resources/
├── application.properties           # 默认配置（开发环境）
├── application-docker.properties    # Docker环境配置
└── application-production.properties # 生产环境配置
```

### 环境配置说明

| 环境 | Profile | 数据库连接 | Redis数据库 | 用途 |
|------|---------|------------|-------------|------|
| 开发 | default | localhost:5432 | 0/1/2 | 本地开发 |
| Docker | docker | postgres:5432 | 0/1/2 | 容器部署 |
| 生产 | production | 环境变量配置 | 0/1/2 | 生产部署 |

## 🔧 各API模块配置详情

### Admin API (端口: 8080)

**数据库配置**:
- 连接池大小: 20 (生产环境可调整)
- Redis数据库: 0
- 特点: 管理后台，相对较少并发

**主要配置**:
```properties
spring.datasource.url=**************************************************
spring.datasource.hikari.maximum-pool-size=20
spring.redis.database=0
```

### App API (端口: 8081)

**数据库配置**:
- 连接池大小: 30 (移动端需要更多连接)
- Redis数据库: 1
- 特点: 移动应用，高并发需求

**主要配置**:
```properties
spring.datasource.hikari.maximum-pool-size=30
spring.redis.database=1
app.mobile.push.enabled=true
```

### Client API (端口: 8082)

**数据库配置**:
- 连接池大小: 25
- Redis数据库: 2
- 特点: Web客户端，支持CORS

**主要配置**:
```properties
spring.datasource.hikari.maximum-pool-size=25
spring.redis.database=2
spring.web.cors.allowed-origins=http://localhost:3000
```

## 🗄️ 数据库初始化

### 初始化脚本

位置: `docker/scripts/init-db.sql`

包含以下表结构:
- `sys_user` - 用户表
- `sys_role` - 角色表
- `sys_permission` - 权限表
- `sys_tenant` - 租户表
- `sys_config` - 系统配置表
- `sys_operation_log` - 操作日志表
- `sys_login_log` - 登录日志表

### 默认数据

- **管理员用户**: admin / admin123
- **默认角色**: 超级管理员、管理员、普通用户
- **默认租户**: default
- **系统配置**: 系统名称、版本等

## 🚀 部署配置

### 开发环境

1. **启动PostgreSQL**:
   ```bash
   docker run -d --name postgres \
     -e POSTGRES_DB=beautiful_posture \
     -e POSTGRES_USER=postgres \
     -e POSTGRES_PASSWORD=postgres123 \
     -p 5432:5432 postgres:15-alpine
   ```

2. **启动Redis**:
   ```bash
   docker run -d --name redis \
     -p 6379:6379 redis:7-alpine
   ```

3. **运行API服务**:
   ```bash
   # Admin API
   ./gradlew :admin-api:bootRun
   
   # App API
   ./gradlew :app-api:bootRun
   
   # Client API
   ./gradlew :client-api:bootRun
   ```

### Docker环境

使用Docker Compose部署:

```bash
# 部署基础设施
./deploy.sh infrastructure

# 分别部署API服务
./deploy.sh admin-api
./deploy.sh app-api
./deploy.sh client-api

# 或完整部署
./deploy.sh full
```

### 生产环境

通过环境变量配置:

```bash
export DATABASE_URL=************************************************
export DATABASE_USERNAME=prod_user
export DATABASE_PASSWORD=secure_password
export REDIS_HOST=prod-redis
export REDIS_PASSWORD=redis_password
```

## 🔒 安全配置

### 数据库安全

1. **连接加密**: 生产环境启用SSL
2. **用户权限**: 使用专用数据库用户
3. **密码策略**: 强密码要求
4. **连接池**: 合理配置连接数

### Redis安全

1. **密码认证**: 生产环境必须设置密码
2. **网络隔离**: 限制访问IP
3. **数据分离**: 不同服务使用不同数据库

## 📊 监控配置

### 健康检查

所有API都提供健康检查端点:

```bash
# Admin API
curl http://localhost:8080/actuator/health

# App API  
curl http://localhost:8081/actuator/health

# Client API
curl http://localhost:8082/actuator/health
```

### 数据库监控

- 连接池状态监控
- 慢查询日志
- 连接数监控
- 事务监控

### 指标收集

- Prometheus指标导出
- 自定义业务指标
- 性能指标收集

## 🛠️ 故障排除

### 常见问题

1. **连接超时**:
   ```properties
   spring.datasource.hikari.connection-timeout=30000
   ```

2. **连接池耗尽**:
   ```properties
   spring.datasource.hikari.maximum-pool-size=30
   spring.datasource.hikari.leak-detection-threshold=60000
   ```

3. **Redis连接失败**:
   ```properties
   spring.redis.timeout=5000ms
   spring.redis.lettuce.pool.max-active=20
   ```

### 日志配置

开发环境详细日志:
```properties
logging.level.com.deepaic=DEBUG
logging.level.com.baomidou.mybatisplus=DEBUG
```

生产环境优化日志:
```properties
logging.level.root=WARN
logging.level.com.deepaic=INFO
```

## 📈 性能优化

### 数据库优化

1. **连接池调优**: 根据并发量调整
2. **索引优化**: 为常用查询添加索引
3. **查询优化**: 使用MyBatis-Plus分页
4. **缓存策略**: Redis缓存热点数据

### 应用优化

1. **JVM调优**: 合理设置堆内存
2. **线程池配置**: 优化Tomcat线程池
3. **缓存配置**: 合理设置缓存TTL
4. **监控告警**: 及时发现性能问题

---

**美姿姿团队** - 让健康管理更美好 💪
