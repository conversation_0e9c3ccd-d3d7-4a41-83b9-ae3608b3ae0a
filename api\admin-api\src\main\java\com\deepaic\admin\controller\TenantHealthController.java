package com.deepaic.admin.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckRole;
import com.deepaic.core.config.MultiTenantProperties;
import com.deepaic.core.tenant.TenantMonitor;
import com.deepaic.core.tenant.TenantSchemaService;
import com.deepaic.core.tenant.SaTokenTenantContext;
import com.deepaic.service.ITenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多租户健康检查控制器
 * 提供多租户系统的健康状态和监控信息
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/tenant-health")
@RequiredArgsConstructor
public class TenantHealthController {

    private final ITenantService tenantService;
    private final TenantSchemaService tenantSchemaService;
    private final TenantMonitor tenantMonitor;
    private final MultiTenantProperties properties;

    /**
     * 获取多租户系统健康状态
     */
    @GetMapping("/status")
    @SaCheckLogin
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 基本状态
            result.put("enabled", properties.isEnabled());
            result.put("defaultSchema", properties.getDefaultSchema());
            result.put("autoContextSetup", properties.isAutoContextSetup());
            
            // 当前用户租户信息
            SaTokenTenantContext.TenantInfo currentTenant = SaTokenTenantContext.getTenantInfo();
            if (currentTenant != null) {
                Map<String, Object> tenantInfo = new HashMap<>();
                tenantInfo.put("tenantCode", currentTenant.getTenantCode());
                tenantInfo.put("tenantName", currentTenant.getTenantName());
                tenantInfo.put("schemaName", currentTenant.getSchemaName());
                tenantInfo.put("userId", currentTenant.getUserId());
                tenantInfo.put("username", currentTenant.getUsername());
                result.put("currentTenant", tenantInfo);
            } else {
                result.put("currentTenant", null);
            }
            
            // 系统统计
            result.put("totalTenants", tenantService.count());
            result.put("activeTenants", tenantService.getActiveTenants().size());
            result.put("totalSchemas", tenantSchemaService.getAllTenantSchemas().size());
            
            result.put("status", "healthy");
            
        } catch (Exception e) {
            log.error("获取多租户健康状态失败", e);
            result.put("status", "unhealthy");
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取多租户监控指标
     */
    @GetMapping("/metrics")
    @SaCheckLogin
    @SaCheckRole("ADMIN")
    public Map<String, Object> getMetrics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 监控指标
            TenantMonitor.MonitorMetrics metrics = tenantMonitor.getMetrics();
            result.put("schemaSwithCount", metrics.getSchemaSwithCount());
            result.put("contextSetupCount", metrics.getContextSetupCount());
            result.put("slowQueryCount", metrics.getSlowQueryCount());
            
            // 配置信息
            Map<String, Object> config = new HashMap<>();
            config.put("monitorEnabled", properties.getMonitor().isEnabled());
            config.put("logSchemaSwitch", properties.getMonitor().isLogSchemaSwitch());
            config.put("logPerformance", properties.getMonitor().isLogPerformance());
            config.put("slowQueryThreshold", properties.getMonitor().getSlowQueryThreshold());
            result.put("config", config);
            
            // 缓存信息
            Map<String, Object> cache = new HashMap<>();
            cache.put("enabled", properties.getCache().isEnabled());
            cache.put("expireTime", properties.getCache().getExpireTime());
            cache.put("maxSize", properties.getCache().getMaxSize());
            result.put("cache", cache);
            
        } catch (Exception e) {
            log.error("获取多租户监控指标失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取所有租户Schema列表
     */
    @GetMapping("/schemas")
    @SaCheckLogin
    @SaCheckRole("ADMIN")
    public Map<String, Object> getSchemas() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<String> schemas = tenantSchemaService.getAllTenantSchemas();
            result.put("schemas", schemas);
            result.put("count", schemas.size());
            result.put("defaultSchema", properties.getDefaultSchema());
            
        } catch (Exception e) {
            log.error("获取租户Schema列表失败", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 验证租户Schema连接
     */
    @GetMapping("/validate-schema")
    @SaCheckLogin
    @SaCheckRole("ADMIN")
    public Map<String, Object> validateSchema() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            SaTokenTenantContext.TenantInfo currentTenant = SaTokenTenantContext.getTenantInfo();
            if (currentTenant == null) {
                result.put("valid", false);
                result.put("message", "当前没有租户上下文");
                return result;
            }
            
            String schemaName = currentTenant.getSchemaName();
            boolean exists = tenantSchemaService.schemaExists(schemaName);
            
            result.put("valid", exists);
            result.put("schemaName", schemaName);
            result.put("tenantCode", currentTenant.getTenantCode());
            
            if (!exists) {
                result.put("message", "Schema不存在: " + schemaName);
            } else {
                result.put("message", "Schema验证通过");
            }
            
        } catch (Exception e) {
            log.error("验证租户Schema失败", e);
            result.put("valid", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
