package com.deepaic.service.impl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepaic.core.entity.BaseEntity;
import com.deepaic.service.IBaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * @title: BaseService
 * <AUTHOR>
 * @Date: 2021/4/27 20:54
 * @Version 1.0
 */
public class BaseServiceImpl<M extends BaseMapper<T>, T extends BaseEntity> extends ServiceImpl<M, T> implements IBaseService<T> {

    protected static Logger LOG = LoggerFactory.getLogger(BaseServiceImpl.class);

}
