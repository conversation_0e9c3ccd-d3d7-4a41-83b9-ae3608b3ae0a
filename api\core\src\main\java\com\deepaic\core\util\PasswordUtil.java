package com.deepaic.core.util;

import cn.hutool.crypto.SecureUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码工具类
 * 提供密码加密和验证功能
 * 
 * <AUTHOR>
 */
@Component
public class PasswordUtil {


    @Value("${system.salt}")
    private String salt;

    /**
     * 加密密码
     * 
     * @param password 原始密码
     * @param salt 盐值
     * @return 加密后的密码
     */
    public  String encrypt(String password) {
        return SecureUtil.hmacMd5(password + this.salt).digestHex(this.salt);
    }


   /* 验证密码 */
    public  boolean matches(String password, String encryptPassword) {
        return encrypt(password).equals(encryptPassword);
    }

}
