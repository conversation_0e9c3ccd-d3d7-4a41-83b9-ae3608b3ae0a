package com.deepaic.core.util;

import cn.hutool.crypto.SecureUtil;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 密码工具类
 * 提供密码加密和验证功能
 * 
 * <AUTHOR>
 */
public class PasswordUtil {


    /**
     * 加密密码
     * 
     * @param password 原始密码
     * @param salt 盐值
     * @return 加密后的密码
     */
    public static String encrypt(String password, String salt) {
        return SecureUtil.hmacMd5(password + salt).digestHex(salt);
    }

   /* 验证密码 */
    public static boolean matches(String password, String encryptPassword, String salt) {
        return encrypt(password, salt).equals(encryptPassword);
    }

}
