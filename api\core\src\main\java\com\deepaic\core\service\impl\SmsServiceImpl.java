package com.deepaic.core.service.impl;

import com.deepaic.core.service.ISmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 短信服务实现类
 * 目前为模拟实现，实际项目中需要集成真实的短信服务商
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmsServiceImpl implements ISmsService {

    // 模拟验证码存储（实际项目中应使用Redis）
    private final Map<String, String> smsCodeCache = new ConcurrentHashMap<>();
    private final Map<String, Long> smsCodeTimeCache = new ConcurrentHashMap<>();

    @Override
    public boolean sendRegistrationSmsCode(String phone, String clientIp) {
        try {
            // 生成6位随机验证码
            String code = String.format("%06d", ThreadLocalRandom.current().nextInt(100000, 999999));
            
            // 模拟发送短信
            log.info("发送注册短信验证码: phone={}, code={}, clientIp={}", phone, code, clientIp);
            
            // 存储验证码（5分钟有效期）
            smsCodeCache.put(phone, code);
            smsCodeTimeCache.put(phone, System.currentTimeMillis());
            
            // 模拟短信发送成功
            return true;
        } catch (Exception e) {
            log.error("发送注册短信验证码失败: phone={}", phone, e);
            return false;
        }
    }

    @Override
    public boolean validateSmsCode(String phone, String code) {
        try {
            String cachedCode = smsCodeCache.get(phone);
            Long sendTime = smsCodeTimeCache.get(phone);
            
            if (cachedCode == null || sendTime == null) {
                log.warn("短信验证码不存在: phone={}", phone);
                return false;
            }
            
            // 检查是否过期（5分钟）
            if (System.currentTimeMillis() - sendTime > 5 * 60 * 1000) {
                log.warn("短信验证码已过期: phone={}", phone);
                smsCodeCache.remove(phone);
                smsCodeTimeCache.remove(phone);
                return false;
            }
            
            // 验证码比较
            boolean valid = cachedCode.equals(code);
            if (valid) {
                // 验证成功后删除验证码
                smsCodeCache.remove(phone);
                smsCodeTimeCache.remove(phone);
                log.info("短信验证码验证成功: phone={}", phone);
            } else {
                log.warn("短信验证码错误: phone={}, inputCode={}, correctCode={}", phone, code, cachedCode);
            }
            
            return valid;
        } catch (Exception e) {
            log.error("验证短信验证码失败: phone={}", phone, e);
            return false;
        }
    }

    @Override
    public boolean sendRegistrationSuccessSms(String phone, String tenantName, String username) {
        try {
            String message = String.format("恭喜您成功注册美姿姿平台！租户：%s，管理员账户：%s，请及时登录系统。", tenantName, username);
            
            // 模拟发送短信
            log.info("发送注册成功短信: phone={}, message={}", phone, message);
            
            // 模拟短信发送成功
            return true;
        } catch (Exception e) {
            log.error("发送注册成功短信失败: phone={}", phone, e);
            return false;
        }
    }

    @Override
    public boolean sendLoginSmsCode(String phone, String clientIp) {
        try {
            // 生成6位随机验证码
            String code = String.format("%06d", ThreadLocalRandom.current().nextInt(100000, 999999));
            
            // 模拟发送短信
            log.info("发送登录短信验证码: phone={}, code={}, clientIp={}", phone, code, clientIp);
            
            // 存储验证码（5分钟有效期）
            smsCodeCache.put("login_" + phone, code);
            smsCodeTimeCache.put("login_" + phone, System.currentTimeMillis());
            
            // 模拟短信发送成功
            return true;
        } catch (Exception e) {
            log.error("发送登录短信验证码失败: phone={}", phone, e);
            return false;
        }
    }

    @Override
    public boolean sendPasswordResetSmsCode(String phone, String clientIp) {
        try {
            // 生成6位随机验证码
            String code = String.format("%06d", ThreadLocalRandom.current().nextInt(100000, 999999));
            
            // 模拟发送短信
            log.info("发送密码重置短信验证码: phone={}, code={}, clientIp={}", phone, code, clientIp);
            
            // 存储验证码（5分钟有效期）
            smsCodeCache.put("reset_" + phone, code);
            smsCodeTimeCache.put("reset_" + phone, System.currentTimeMillis());
            
            // 模拟短信发送成功
            return true;
        } catch (Exception e) {
            log.error("发送密码重置短信验证码失败: phone={}", phone, e);
            return false;
        }
    }
}
