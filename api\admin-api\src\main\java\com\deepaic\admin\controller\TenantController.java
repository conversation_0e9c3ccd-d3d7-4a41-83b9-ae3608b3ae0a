package com.deepaic.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.dto.TenantDTO;
import com.deepaic.core.dto.TenantQueryDTO;
import com.deepaic.core.entity.Tenant;
import com.deepaic.service.ITenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * 租户管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/tenants")
@RequiredArgsConstructor
@Validated
public class TenantController {

    private final ITenantService tenantService;

    /**
     * 创建租户
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createTenant(@Valid @RequestBody TenantDTO tenantDTO) {
        try {
            Long tenantId = tenantService.createTenant(tenantDTO);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "创建租户成功",
                "data", Map.of("id", tenantId)
            ));
        } catch (Exception e) {
            log.error("创建租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 更新租户
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateTenant(
            @PathVariable Long id,
            @Valid @RequestBody TenantDTO tenantDTO) {
        try {
            boolean success = tenantService.updateTenant(id, tenantDTO);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "更新租户成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "更新租户失败"
                ));
            }
        } catch (Exception e) {
            log.error("更新租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 根据ID查询租户详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getTenantById(@PathVariable Long id) {
        try {
            TenantDTO tenant = tenantService.getTenantById(id);
            if (tenant != null) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", tenant
                ));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("查询租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 根据租户代码查询租户
     */
    @GetMapping("/code/{tenantCode}")
    public ResponseEntity<Map<String, Object>> getTenantByCode(@PathVariable String tenantCode) {
        try {
            Tenant tenant = tenantService.getTenantByCode(tenantCode);
            if (tenant != null) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", tenant
                ));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("查询租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 分页查询租户列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getTenantPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            TenantQueryDTO query) {
        try {
            Page<TenantDTO> page = new Page<>(current, size);
            IPage<TenantDTO> result = tenantService.getTenantPage(page, query);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", Map.of(
                    "records", result.getRecords(),
                    "total", result.getTotal(),
                    "current", result.getCurrent(),
                    "size", result.getSize(),
                    "pages", result.getPages()
                )
            ));
        } catch (Exception e) {
            log.error("查询租户列表失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 查询所有启用的租户
     */
    @GetMapping("/enabled")
    public ResponseEntity<Map<String, Object>> getEnabledTenants() {
        try {
            List<Tenant> tenants = tenantService.getEnabledTenants();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", tenants
            ));
        } catch (Exception e) {
            log.error("查询启用租户列表失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 删除租户
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteTenant(@PathVariable Long id) {
        try {
            boolean success = tenantService.deleteTenant(id);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "删除租户成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "删除租户失败"
                ));
            }
        } catch (Exception e) {
            log.error("删除租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 批量删除租户
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Map<String, Object>> deleteTenants(
            @RequestBody @NotEmpty(message = "租户ID列表不能为空") List<Long> ids) {
        try {
            boolean success = tenantService.deleteTenants(ids);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "批量删除租户成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "批量删除租户失败"
                ));
            }
        } catch (Exception e) {
            log.error("批量删除租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 启用租户
     */
    @PutMapping("/{id}/enable")
    public ResponseEntity<Map<String, Object>> enableTenant(@PathVariable Long id) {
        try {
            boolean success = tenantService.enableTenant(id);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "启用租户成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "启用租户失败"
                ));
            }
        } catch (Exception e) {
            log.error("启用租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 禁用租户
     */
    @PutMapping("/{id}/disable")
    public ResponseEntity<Map<String, Object>> disableTenant(@PathVariable Long id) {
        try {
            boolean success = tenantService.disableTenant(id);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "禁用租户成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "禁用租户失败"
                ));
            }
        } catch (Exception e) {
            log.error("禁用租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 暂停租户
     */
    @PutMapping("/{id}/suspend")
    public ResponseEntity<Map<String, Object>> suspendTenant(@PathVariable Long id) {
        try {
            boolean success = tenantService.suspendTenant(id);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "暂停租户成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "暂停租户失败"
                ));
            }
        } catch (Exception e) {
            log.error("暂停租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 设置租户过期
     */
    @PutMapping("/{id}/expire")
    public ResponseEntity<Map<String, Object>> expireTenant(@PathVariable Long id) {
        try {
            boolean success = tenantService.expireTenant(id);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "设置租户过期成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "设置租户过期失败"
                ));
            }
        } catch (Exception e) {
            log.error("设置租户过期失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 续费租户服务
     */
    @PutMapping("/{id}/renew")
    public ResponseEntity<Map<String, Object>> renewTenantService(
            @PathVariable Long id,
            @RequestBody Map<String, Integer> request) {
        try {
            Integer months = request.get("months");
            if (months == null || months <= 0) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "续费月数必须大于0"
                ));
            }

            boolean success = tenantService.renewTenantService(id, months);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "续费租户服务成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "续费租户服务失败"
                ));
            }
        } catch (Exception e) {
            log.error("续费租户服务失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 升级租户类型
     */
    @PutMapping("/{id}/upgrade")
    public ResponseEntity<Map<String, Object>> upgradeTenantType(
            @PathVariable Long id,
            @RequestBody Map<String, Short> request) {
        try {
            Short newType = request.get("newType");
            if (newType == null || newType < 1 || newType > 4) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "租户类型必须在1-4之间"
                ));
            }

            boolean success = tenantService.upgradeTenantType(id, newType);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "升级租户类型成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "升级租户类型失败"
                ));
            }
        } catch (Exception e) {
            log.error("升级租户类型失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 统计租户信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getTenantStats() {
        try {
            Object stats = tenantService.getTenantStats();
            List<Object> statusStats = tenantService.countByStatus();
            List<Object> typeStats = tenantService.countByType();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", Map.of(
                    "stats", stats,
                    "statusStats", statusStats,
                    "typeStats", typeStats
                )
            ));
        } catch (Exception e) {
            log.error("查询租户统计失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 查询即将到期的租户
     */
    @GetMapping("/expiring")
    public ResponseEntity<Map<String, Object>> getExpiringSoon(
            @RequestParam(defaultValue = "30") Integer days) {
        try {
            List<Tenant> tenants = tenantService.getExpiringSoon(days);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", tenants
            ));
        } catch (Exception e) {
            log.error("查询即将到期租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 查询试用期即将结束的租户
     */
    @GetMapping("/trial-expiring")
    public ResponseEntity<Map<String, Object>> getTrialExpiringSoon(
            @RequestParam(defaultValue = "7") Integer days) {
        try {
            List<Tenant> tenants = tenantService.getTrialExpiringSoon(days);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", tenants
            ));
        } catch (Exception e) {
            log.error("查询试用期即将结束租户失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 同步租户用户数量
     */
    @PutMapping("/{tenantCode}/sync-users")
    public ResponseEntity<Map<String, Object>> syncTenantUserCount(@PathVariable String tenantCode) {
        try {
            boolean success = tenantService.syncTenantUserCount(tenantCode);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "success", true,
                    "message", "同步租户用户数量成功"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "同步租户用户数量失败"
                ));
            }
        } catch (Exception e) {
            log.error("同步租户用户数量失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "success", false,
                "message", e.getMessage()
            ));
        }
    }
}
