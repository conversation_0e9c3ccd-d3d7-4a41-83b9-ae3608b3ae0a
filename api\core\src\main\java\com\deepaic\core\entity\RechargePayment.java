package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 支付记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_recharge_payment")
public class RechargePayment extends BaseEntity {

    private Long rechargeId;

    private Long memberId;

    private String totalAmount;

    private BigDecimal discount;

    private BigDecimal actualAmount;

    private BigDecimal alipay;

    private BigDecimal wechat;

    private BigDecimal cash;

    private BigDecimal creditCard;

    private BigDecimal other;
}
