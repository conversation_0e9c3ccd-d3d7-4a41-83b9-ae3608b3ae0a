package com.deepaic.admin.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.dto.MenuDTO;
import com.deepaic.service.IMenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 菜单管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/menu")
@RequiredArgsConstructor
@Validated
public class MenuController {

    private final IMenuService menuService;

    /**
     * 查询菜单列表（分页）
     */
    @GetMapping("/page")
    @SaCheckLogin
    @SaCheckPermission("system:menu:list")
    public Map<String, Object> getMenuPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            MenuDTO.MenuQueryDTO query) {
        
        Page<MenuDTO> page = new Page<>(current, size);
        IPage<MenuDTO> result = menuService.getMenuPage(page, query);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", result);
        return response;
    }

    /**
     * 查询菜单树
     */
    @GetMapping("/tree")
    @SaCheckLogin
    @SaCheckPermission("system:menu:list")
    public Map<String, Object> getMenuTree(MenuDTO.MenuQueryDTO query) {
        List<MenuDTO> menuTree = menuService.getMenuTree(query);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", menuTree);
        return response;
    }

    /**
     * 获取菜单选择树
     */
    @GetMapping("/select-tree")
    @SaCheckLogin
    @SaCheckPermission("system:menu:list")
    public Map<String, Object> getMenuSelectTree() {
        List<MenuDTO> menuTree = menuService.getMenuSelectTree();
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", menuTree);
        return response;
    }

    /**
     * 获取菜单详情
     */
    @GetMapping("/{id}")
    @SaCheckLogin
    @SaCheckPermission("system:menu:query")
    public Map<String, Object> getMenuById(@PathVariable Long id) {
        MenuDTO menu = menuService.getMenuById(id);
        
        Map<String, Object> response = new HashMap<>();
        if (menu != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", menu);
        } else {
            response.put("code", 404);
            response.put("message", "菜单不存在");
        }
        return response;
    }

    /**
     * 创建菜单
     */
    @PostMapping
    @SaCheckLogin
    @SaCheckPermission("system:menu:add")
    public Map<String, Object> createMenu(@Valid @RequestBody MenuDTO menuDTO) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long menuId = menuService.createMenu(menuDTO);
            response.put("code", 200);
            response.put("message", "创建成功");
            response.put("data", menuId);
        } catch (Exception e) {
            log.error("创建菜单失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 更新菜单
     */
    @PutMapping("/{id}")
    @SaCheckLogin
    @SaCheckPermission("system:menu:edit")
    public Map<String, Object> updateMenu(@PathVariable Long id, @Valid @RequestBody MenuDTO menuDTO) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = menuService.updateMenu(id, menuDTO);
            if (success) {
                response.put("code", 200);
                response.put("message", "更新成功");
            } else {
                response.put("code", 500);
                response.put("message", "更新失败");
            }
        } catch (Exception e) {
            log.error("更新菜单失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 删除菜单
     */
    @DeleteMapping("/{id}")
    @SaCheckLogin
    @SaCheckPermission("system:menu:remove")
    public Map<String, Object> deleteMenu(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = menuService.deleteMenu(id);
            if (success) {
                response.put("code", 200);
                response.put("message", "删除成功");
            } else {
                response.put("code", 500);
                response.put("message", "删除失败");
            }
        } catch (Exception e) {
            log.error("删除菜单失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 批量删除菜单
     */
    @DeleteMapping("/batch")
    @SaCheckLogin
    @SaCheckPermission("system:menu:remove")
    public Map<String, Object> deleteMenus(@Valid @RequestBody @NotEmpty List<Long> ids) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = menuService.deleteMenus(ids);
            if (success) {
                response.put("code", 200);
                response.put("message", "批量删除成功");
            } else {
                response.put("code", 500);
                response.put("message", "批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除菜单失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 检查菜单名称是否唯一
     */
    @GetMapping("/check-name")
    @SaCheckLogin
    @SaCheckPermission("system:menu:list")
    public Map<String, Object> checkMenuNameUnique(
            @RequestParam String menuName,
            @RequestParam(required = false) Long parentId,
            @RequestParam(required = false) Long id) {
        
        boolean unique = menuService.checkMenuNameUnique(menuName, parentId, id);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", unique);
        return response;
    }
}
