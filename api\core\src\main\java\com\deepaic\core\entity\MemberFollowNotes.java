package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 会员寄存表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_follow_notes")
public class MemberFollowNotes extends BaseEntity {

    private Long memberId;

    /**
     * 回访员工
     */
    private Long empolyeeId;

    /**
     * 回访内容
     */
    private String remark;

    /**
     * 回访方式：1=电话，2=短信，3=微信，4=上门，5=其它
     */
    private Short type;

    // 跟进类型常量
    public static final short TYPE_PHONE = 1;      // 电话跟进
    public static final short TYPE_VISIT = 2;      // 上门拜访
    public static final short TYPE_WECHAT = 3;     // 微信沟通
    public static final short TYPE_OTHER = 4;      // 其他方式
}
