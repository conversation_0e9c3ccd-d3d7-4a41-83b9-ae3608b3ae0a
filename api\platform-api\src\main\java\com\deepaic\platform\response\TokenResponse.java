package com.deepaic.platform.response;

import com.deepaic.core.auth.models.UserPrincipal;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TokenResponse {
    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌过期时间（秒）
     */
    private Long expiresIn;

    private UserPrincipal userPrincipal;

}
