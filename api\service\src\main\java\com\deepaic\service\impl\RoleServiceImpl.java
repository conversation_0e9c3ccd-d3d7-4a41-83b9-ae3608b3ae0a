package com.deepaic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.entity.Role;
import com.deepaic.core.entity.RoleMenu;
import com.deepaic.core.entity.UserRole;
import com.deepaic.core.dto.RoleDTO;
import com.deepaic.core.mapper.RoleMapper;
import com.deepaic.core.mapper.RoleMenuMapper;
import com.deepaic.core.mapper.UserRoleMapper;
import com.deepaic.service.IRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends BaseServiceImpl<RoleMapper, Role> implements IRoleService {

    private final RoleMapper roleMapper;
    private final RoleMenuMapper roleMenuMapper;
    private final UserRoleMapper userRoleMapper;

    @Override
    public IPage<RoleDTO> getRolePage(Page<RoleDTO> page, RoleDTO.RoleQueryDTO query) {
        return roleMapper.selectRolePage(page, query);
    }

    @Override
    public List<RoleDTO> getRoleList(RoleDTO.RoleQueryDTO query) {
        return roleMapper.selectRoleList(query);
    }

    @Override
    public List<RoleDTO> getRoleListByUserId(Long userId) {
        return roleMapper.selectRoleListByUserId(userId);
    }

    @Override
    @Transactional
    public Long createRole(RoleDTO roleDTO) {
        // 检查角色名称是否唯一
        if (!checkRoleNameUnique(roleDTO.getRoleName(), null)) {
            throw new RuntimeException("角色名称已存在");
        }

        // 检查角色编码是否唯一
        if (!checkRoleCodeUnique(roleDTO.getRoleCode(), null)) {
            throw new RuntimeException("角色编码已存在");
        }

        Role role = new Role();
        BeanUtils.copyProperties(roleDTO, role);

        // 设置默认值
        if (role.getRoleSort() == null) {
            role.setRoleSort(0);
        }
        if (role.getStatus() == null) {
            role.setStatus(Role.STATUS_ENABLED);
        }
        if (role.getDataScope() == null) {
            role.setDataScope(Role.DATA_SCOPE_DEPT);
        }

        boolean success = save(role);
        if (!success) {
            throw new RuntimeException("创建角色失败");
        }

        // 分配菜单权限
        if (roleDTO.getMenuIds() != null && !roleDTO.getMenuIds().isEmpty()) {
            assignRoleMenus(role.getId(), roleDTO.getMenuIds());
        }

        log.info("创建角色成功: roleName={}, roleCode={}, id={}", role.getRoleName(), role.getRoleCode(), role.getId());
        return role.getId();
    }

    @Override
    @Transactional
    public boolean updateRole(Long id, RoleDTO roleDTO) {
        Role existingRole = getById(id);
        if (existingRole == null) {
            throw new RuntimeException("角色不存在: " + id);
        }

        // 检查角色名称是否唯一
        if (!checkRoleNameUnique(roleDTO.getRoleName(), id)) {
            throw new RuntimeException("角色名称已存在");
        }

        // 检查角色编码是否唯一
        if (!checkRoleCodeUnique(roleDTO.getRoleCode(), id)) {
            throw new RuntimeException("角色编码已存在");
        }

        BeanUtils.copyProperties(roleDTO, existingRole, "id", "createTime", "version", "deleted");

        boolean success = updateById(existingRole);
        if (!success) {
            return false;
        }

        // 更新菜单权限
        if (roleDTO.getMenuIds() != null) {
            assignRoleMenus(id, roleDTO.getMenuIds());
        }

        log.info("更新角色成功: id={}, roleName={}", id, existingRole.getRoleName());
        return true;
    }

    @Override
    @Transactional
    public boolean deleteRole(Long id) {
        Role role = getById(id);
        if (role == null) {
            return false;
        }

        // 检查是否有用户使用该角色
        Integer userCount = roleMapper.countUsersByRoleId(id);
        if (userCount != null && userCount > 0) {
            throw new RuntimeException("该角色下还有用户，无法删除");
        }

        // 删除角色菜单关联
        roleMenuMapper.deleteByRoleId(id);

        // 删除用户角色关联
        userRoleMapper.deleteByRoleId(id);

        boolean success = removeById(id);
        if (success) {
            log.info("删除角色成功: id={}, roleName={}", id, role.getRoleName());
        }
        return success;
    }

    @Override
    @Transactional
    public boolean deleteRoles(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }

        for (Long id : ids) {
            deleteRole(id);
        }
        return true;
    }

    @Override
    public RoleDTO getRoleById(Long id) {
        RoleDTO roleDTO = roleMapper.selectRoleById(id);
        if (roleDTO != null) {
            // 查询菜单权限
            roleDTO.setMenuIds(roleMapper.selectMenuIdsByRoleId(id));
        }
        return roleDTO;
    }

    @Override
    public boolean checkRoleNameUnique(String roleName, Long id) {
        Integer count = roleMapper.checkRoleNameUnique(roleName, id);
        return count == null || count == 0;
    }

    @Override
    public boolean checkRoleCodeUnique(String roleCode, Long id) {
        Integer count = roleMapper.checkRoleCodeUnique(roleCode, id);
        return count == null || count == 0;
    }

    @Override
    @Transactional
    public boolean assignRoleMenus(Long roleId, List<Long> menuIds) {
        // 删除原有的角色菜单关联
        roleMenuMapper.deleteByRoleId(roleId);

        if (menuIds != null && !menuIds.isEmpty()) {
            List<RoleMenu> roleMenuList = new ArrayList<>();
            for (Long menuId : menuIds) {
                RoleMenu roleMenu = new RoleMenu();
                roleMenu.setRoleId(roleId);
                roleMenu.setMenuId(menuId);
                roleMenuList.add(roleMenu);
            }

            int result = roleMenuMapper.batchInsert(roleMenuList);
            log.info("分配角色菜单权限: roleId={}, menuCount={}", roleId, menuIds.size());
            return result > 0;
        }

        return true;
    }

    @Override
    @Transactional
    public boolean assignRoleDataScope(Long roleId, Integer dataScope, List<Long> deptIds) {
        // 暂时不实现部门数据权限，因为没有RoleDepartmentMapper
        log.info("分配角色数据权限: roleId={}, dataScope={}", roleId, dataScope);
        return true;
    }

    @Override
    @Transactional
    public boolean assignUserRoles(Long userId, List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return true;
        }

        // 先删除原有的用户角色关联
        userRoleMapper.deleteByUserId(userId);

        List<UserRole> userRoleList = new ArrayList<>();
        for (Long roleId : roleIds) {
            UserRole userRole = new UserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRoleList.add(userRole);
        }

        int result = userRoleMapper.batchInsert(userRoleList);
        log.info("分配用户角色: userId={}, roleCount={}", userId, roleIds.size());
        return result > 0;
    }

    @Override
    @Transactional
    public boolean removeUserRoles(Long userId, List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return true;
        }

        // 删除指定的用户角色关联
        for (Long roleId : roleIds) {
            userRoleMapper.delete(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<UserRole>()
                    .eq(UserRole::getUserId, userId)
                    .eq(UserRole::getRoleId, roleId)
            );
        }

        log.info("取消用户角色: userId={}, roleCount={}", userId, roleIds.size());
        return true;
    }

    @Override
    public List<Long> getMenuIdsByRoleId(Long roleId) {
        return roleMapper.selectMenuIdsByRoleId(roleId);
    }

    @Override
    public List<Long> getDeptIdsByRoleId(Long roleId) {
        // 暂时返回空列表，因为没有实现部门功能
        return new ArrayList<>();
    }
}
