package com.deepaic.app.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 小程序API健康检查控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/app")
public class AppHealthController {

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "app-api");
        result.put("description", "小程序后端API");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }

    /**
     * 服务信息
     */
    @GetMapping("/info")
    public Map<String, Object> info() {
        Map<String, Object> result = new HashMap<>();
        result.put("name", "美姿姿小程序API");
        result.put("version", "1.0.0");
        result.put("description", "小程序后端服务");
        result.put("port", 8082);
        return result;
    }
}
