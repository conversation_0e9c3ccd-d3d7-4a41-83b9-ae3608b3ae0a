package com.deepaic.core.service.impl;

import com.deepaic.core.service.IEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 邮件服务实现类
 * 目前为模拟实现，实际项目中需要集成真实的邮件服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class EmailServiceImpl implements IEmailService {

    // 模拟验证码存储（实际项目中应使用Redis）
    private final Map<String, String> emailCodeCache = new ConcurrentHashMap<>();
    private final Map<String, Long> emailCodeTimeCache = new ConcurrentHashMap<>();

    @Override
    public boolean sendRegistrationEmailCode(String email, String clientIp) {
        try {
            // 生成6位随机验证码
            String code = String.format("%06d", ThreadLocalRandom.current().nextInt(100000, 999999));
            
            // 模拟发送邮件
            log.info("发送注册邮箱验证码: email={}, code={}, clientIp={}", email, code, clientIp);
            
            // 存储验证码（10分钟有效期）
            emailCodeCache.put(email, code);
            emailCodeTimeCache.put(email, System.currentTimeMillis());
            
            // 模拟邮件发送成功
            return true;
        } catch (Exception e) {
            log.error("发送注册邮箱验证码失败: email={}", email, e);
            return false;
        }
    }

    @Override
    public boolean validateEmailCode(String email, String code) {
        try {
            String cachedCode = emailCodeCache.get(email);
            Long sendTime = emailCodeTimeCache.get(email);
            
            if (cachedCode == null || sendTime == null) {
                log.warn("邮箱验证码不存在: email={}", email);
                return false;
            }
            
            // 检查是否过期（10分钟）
            if (System.currentTimeMillis() - sendTime > 10 * 60 * 1000) {
                log.warn("邮箱验证码已过期: email={}", email);
                emailCodeCache.remove(email);
                emailCodeTimeCache.remove(email);
                return false;
            }
            
            // 验证码比较
            boolean valid = cachedCode.equals(code);
            if (valid) {
                // 验证成功后删除验证码
                emailCodeCache.remove(email);
                emailCodeTimeCache.remove(email);
                log.info("邮箱验证码验证成功: email={}", email);
            } else {
                log.warn("邮箱验证码错误: email={}, inputCode={}, correctCode={}", email, code, cachedCode);
            }
            
            return valid;
        } catch (Exception e) {
            log.error("验证邮箱验证码失败: email={}", email, e);
            return false;
        }
    }

    @Override
    public boolean sendRegistrationSuccessEmail(String email, String tenantName, String username, String loginUrl) {
        try {
            String subject = "欢迎加入美姿姿平台";
            String content = String.format("""
                尊敬的用户，您好！
                
                恭喜您成功注册美姿姿平台！
                
                租户名称：%s
                管理员账户：%s
                登录地址：%s
                
                请妥善保管您的账户信息，如有问题请联系客服。
                
                美姿姿团队
                """, tenantName, username, loginUrl);
            
            // 模拟发送邮件
            log.info("发送注册成功邮件: email={}, subject={}", email, subject);
            log.debug("邮件内容: {}", content);
            
            // 模拟邮件发送成功
            return true;
        } catch (Exception e) {
            log.error("发送注册成功邮件失败: email={}", email, e);
            return false;
        }
    }

    @Override
    public boolean sendActivationEmail(String email, String tenantName, String activationUrl) {
        try {
            String subject = "激活您的美姿姿平台账户";
            String content = String.format("""
                尊敬的用户，您好！
                
                感谢您注册美姿姿平台！
                
                租户名称：%s
                
                请点击以下链接激活您的账户：
                %s
                
                如果您无法点击链接，请复制链接到浏览器地址栏访问。
                
                美姿姿团队
                """, tenantName, activationUrl);
            
            // 模拟发送邮件
            log.info("发送激活邮件: email={}, subject={}", email, subject);
            log.debug("邮件内容: {}", content);
            
            // 模拟邮件发送成功
            return true;
        } catch (Exception e) {
            log.error("发送激活邮件失败: email={}", email, e);
            return false;
        }
    }

    @Override
    public boolean sendPasswordResetEmail(String email, String resetUrl) {
        try {
            String subject = "重置您的美姿姿平台密码";
            String content = String.format("""
                尊敬的用户，您好！
                
                您申请重置美姿姿平台的登录密码。
                
                请点击以下链接重置密码：
                %s
                
                如果您没有申请重置密码，请忽略此邮件。
                
                美姿姿团队
                """, resetUrl);
            
            // 模拟发送邮件
            log.info("发送密码重置邮件: email={}, subject={}", email, subject);
            log.debug("邮件内容: {}", content);
            
            // 模拟邮件发送成功
            return true;
        } catch (Exception e) {
            log.error("发送密码重置邮件失败: email={}", email, e);
            return false;
        }
    }

    @Override
    public boolean sendAuditApprovedEmail(String email, String tenantName, String loginUrl) {
        try {
            String subject = "您的租户申请已通过审核";
            String content = String.format("""
                尊敬的用户，您好！
                
                恭喜您！您的租户申请已通过审核。
                
                租户名称：%s
                登录地址：%s
                
                您现在可以正常使用美姿姿平台的所有功能。
                
                美姿姿团队
                """, tenantName, loginUrl);
            
            // 模拟发送邮件
            log.info("发送审核通过邮件: email={}, subject={}", email, subject);
            log.debug("邮件内容: {}", content);
            
            // 模拟邮件发送成功
            return true;
        } catch (Exception e) {
            log.error("发送审核通过邮件失败: email={}", email, e);
            return false;
        }
    }

    @Override
    public boolean sendAuditRejectedEmail(String email, String tenantName, String reason) {
        try {
            String subject = "您的租户申请未通过审核";
            String content = String.format("""
                尊敬的用户，您好！
                
                很抱歉，您的租户申请未通过审核。
                
                租户名称：%s
                拒绝原因：%s
                
                如有疑问，请联系客服。
                
                美姿姿团队
                """, tenantName, reason);
            
            // 模拟发送邮件
            log.info("发送审核拒绝邮件: email={}, subject={}", email, subject);
            log.debug("邮件内容: {}", content);
            
            // 模拟邮件发送成功
            return true;
        } catch (Exception e) {
            log.error("发送审核拒绝邮件失败: email={}", email, e);
            return false;
        }
    }
}
