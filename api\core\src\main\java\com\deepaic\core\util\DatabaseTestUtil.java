package com.deepaic.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据库连接测试和表结构验证工具
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DatabaseTestUtil implements CommandLineRunner {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        testDatabaseConnection();
        validateTableStructure();
    }

    /**
     * 测试数据库连接
     */
    public void testDatabaseConnection() {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            log.info("=== 数据库连接信息 ===");
            log.info("数据库产品名称: {}", metaData.getDatabaseProductName());
            log.info("数据库版本: {}", metaData.getDatabaseProductVersion());
            log.info("驱动名称: {}", metaData.getDriverName());
            log.info("驱动版本: {}", metaData.getDriverVersion());
            log.info("连接URL: {}", metaData.getURL());
            log.info("用户名: {}", metaData.getUserName());
            log.info("数据库连接测试成功！");
        } catch (Exception e) {
            log.error("数据库连接测试失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 验证表结构
     */
    public void validateTableStructure() {
        log.info("=== 开始验证表结构 ===");
        
        // 验证公共表
        validatePublicTables();
        
        // 验证租户表（使用tenant_demo schema）
        validateTenantTables();
        
        log.info("=== 表结构验证完成 ===");
    }

    /**
     * 验证公共表
     */
    private void validatePublicTables() {
        log.info("--- 验证公共表 ---");
        
        String[] publicTables = {
            "pub_tenant",
            "pub_platform_account", 
            "pub_customer_account",
            "pub_member_account"
        };
        
        for (String tableName : publicTables) {
            validateTable("public", tableName);
        }
    }

    /**
     * 验证租户表
     */
    private void validateTenantTables() {
        log.info("--- 验证租户表 ---");
        
        // 首先检查tenant_demo schema是否存在
        try {
            jdbcTemplate.execute("SET search_path TO tenant_demo, public");
            
            String[] tenantTables = {
                "sys_user",
                "sys_role",
                "sys_menu",
                "sys_organization",
                "sys_permission",
                "sys_user_role",
                "sys_role_menu",
                "sys_role_permission",
                "sys_user_organization",
                "sys_role_organization",
                "sys_user_profile",
                "sys_member"
            };
            
            for (String tableName : tenantTables) {
                validateTable("tenant_demo", tableName);
            }
            
        } catch (Exception e) {
            log.warn("tenant_demo schema不存在或无法访问: {}", e.getMessage());
        } finally {
            // 恢复默认search_path
            try {
                jdbcTemplate.execute("SET search_path TO public");
            } catch (Exception e) {
                log.warn("恢复search_path失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 验证单个表
     */
    private void validateTable(String schema, String tableName) {
        try {
            String fullTableName = "public".equals(schema) ? tableName : schema + "." + tableName;
            
            // 检查表是否存在
            String checkTableSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(checkTableSql, Integer.class, schema, tableName);
            
            if (count != null && count > 0) {
                // 获取表的列信息
                List<String> columns = getTableColumns(schema, tableName);
                log.info("✓ 表 {} 存在，包含 {} 个字段: {}", fullTableName, columns.size(), String.join(", ", columns));
                
                // 获取表的记录数
                try {
                    String countSql = "SELECT COUNT(*) FROM " + fullTableName;
                    Integer recordCount = jdbcTemplate.queryForObject(countSql, Integer.class);
                    log.info("  记录数: {}", recordCount);
                } catch (Exception e) {
                    log.warn("  无法获取表 {} 的记录数: {}", fullTableName, e.getMessage());
                }
                
            } else {
                log.warn("✗ 表 {} 不存在", fullTableName);
            }
            
        } catch (Exception e) {
            log.error("验证表 {}.{} 时出错: {}", schema, tableName, e.getMessage());
        }
    }

    /**
     * 获取表的列信息
     */
    private List<String> getTableColumns(String schema, String tableName) {
        List<String> columns = new ArrayList<>();
        try {
            String sql = "SELECT column_name FROM information_schema.columns WHERE table_schema = ? AND table_name = ? ORDER BY ordinal_position";
            columns = jdbcTemplate.queryForList(sql, String.class, schema, tableName);
        } catch (Exception e) {
            log.warn("获取表 {}.{} 的列信息失败: {}", schema, tableName, e.getMessage());
        }
        return columns;
    }

    /**
     * 检查必要的索引
     */
    public void validateIndexes() {
        log.info("=== 开始验证索引 ===");
        
        try {
            // 检查一些重要的索引
            String indexSql = "SELECT schemaname, tablename, indexname FROM pg_indexes WHERE schemaname IN ('public', 'tenant_demo') ORDER BY schemaname, tablename";
            
            jdbcTemplate.query(indexSql, (rs) -> {
                String schema = rs.getString("schemaname");
                String table = rs.getString("tablename");
                String index = rs.getString("indexname");
                log.info("索引: {}.{} -> {}", schema, table, index);
            });
            
        } catch (Exception e) {
            log.error("验证索引时出错: {}", e.getMessage());
        }
        
        log.info("=== 索引验证完成 ===");
    }
}
