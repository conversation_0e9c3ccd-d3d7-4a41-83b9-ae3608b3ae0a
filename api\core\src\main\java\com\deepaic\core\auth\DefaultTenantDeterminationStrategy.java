package com.deepaic.core.auth;

import com.deepaic.core.dto.MemberDTO;
import com.deepaic.core.mapper.MemberMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 默认租户确定策略
 * 支持多种方式确定租户：域名、推荐码、请求参数等
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultTenantDeterminationStrategy implements TenantDeterminationStrategy {

    private final MemberMapper memberMapper;

    @Override
    public String determineTenant(MemberDTO.MemberLoginDTO loginDTO, HttpServletRequest request) {
        
        // 策略1: 从请求参数中获取租户标识
        String tenantFromParam = getTenantFromParameter(request);
        if (StringUtils.hasText(tenantFromParam)) {
            log.info("从请求参数确定租户: {}", tenantFromParam);
            return tenantFromParam;
        }

        // 策略2: 从域名中解析租户
        String tenantFromDomain = getTenantFromDomain(request);
        if (StringUtils.hasText(tenantFromDomain)) {
            log.info("从域名确定租户: {}", tenantFromDomain);
            return tenantFromDomain;
        }

        // 策略3: 从推荐码中获取租户信息
        String tenantFromReferrer = getTenantFromReferrer(loginDTO);
        if (StringUtils.hasText(tenantFromReferrer)) {
            log.info("从推荐人确定租户: {}", tenantFromReferrer);
            return tenantFromReferrer;
        }

        // 策略4: 从请求头中获取租户信息
        String tenantFromHeader = getTenantFromHeader(request);
        if (StringUtils.hasText(tenantFromHeader)) {
            log.info("从请求头确定租户: {}", tenantFromHeader);
            return tenantFromHeader;
        }

        // 默认策略: 使用默认租户
        log.info("使用默认租户: default");
        return "default";
    }

    @Override
    public int getPriority() {
        return 100; // 默认优先级
    }

    /**
     * 从请求参数中获取租户标识
     */
    private String getTenantFromParameter(HttpServletRequest request) {
        // 支持多种参数名
        String[] paramNames = {"tenant", "tenantCode", "tenant_code", "t"};
        
        for (String paramName : paramNames) {
            String tenant = request.getParameter(paramName);
            if (StringUtils.hasText(tenant)) {
                return tenant.trim();
            }
        }
        
        return null;
    }

    /**
     * 从域名中解析租户
     * 支持子域名模式：tenant.domain.com
     */
    private String getTenantFromDomain(HttpServletRequest request) {
        String serverName = request.getServerName();
        if (!StringUtils.hasText(serverName)) {
            return null;
        }

        // 解析子域名
        String[] parts = serverName.split("\\.");
        if (parts.length >= 3) {
            String subdomain = parts[0];
            // 排除常见的非租户子域名
            if (!isSystemSubdomain(subdomain)) {
                return subdomain;
            }
        }

        return null;
    }

    /**
     * 从推荐人信息中获取租户
     */
    private String getTenantFromReferrer(MemberDTO.MemberLoginDTO loginDTO) {
        if (!StringUtils.hasText(loginDTO.getReferrerNo())) {
            return null;
        }

        try {
            // 这里需要在公共schema中查询推荐人的租户信息
            // 由于推荐人信息在租户schema中，需要特殊处理
            // 可以考虑在会员编号中包含租户信息，或者建立额外的映射表
            
            // 临时实现：从会员编号中解析租户信息
            // 假设会员编号格式为：{tenant}_{number}
            String referrerNo = loginDTO.getReferrerNo();
            if (referrerNo.contains("_")) {
                String[] parts = referrerNo.split("_");
                if (parts.length >= 2) {
                    return parts[0];
                }
            }
            
        } catch (Exception e) {
            log.warn("从推荐人信息获取租户失败: referrerNo={}", loginDTO.getReferrerNo(), e);
        }

        return null;
    }

    /**
     * 从请求头中获取租户信息
     */
    private String getTenantFromHeader(HttpServletRequest request) {
        // 支持多种请求头
        String[] headerNames = {"X-Tenant-Code", "Tenant-Code", "X-Tenant", "Tenant"};
        
        for (String headerName : headerNames) {
            String tenant = request.getHeader(headerName);
            if (StringUtils.hasText(tenant)) {
                return tenant.trim();
            }
        }
        
        return null;
    }

    /**
     * 检查是否为系统子域名
     */
    private boolean isSystemSubdomain(String subdomain) {
        String[] systemSubdomains = {"www", "api", "admin", "app", "client", "static", "cdn", "img"};
        
        for (String systemSub : systemSubdomains) {
            if (systemSub.equalsIgnoreCase(subdomain)) {
                return true;
            }
        }
        
        return false;
    }
}
