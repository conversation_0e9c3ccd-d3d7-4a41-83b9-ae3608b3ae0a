package com.deepaic.core.tenant;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * 租户Schema管理服务
 * 提供租户schema的创建、删除、验证等功能
 * 
 * <AUTHOR> Posture Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantSchemaService {

    private final DataSource dataSource;

    /**
     * 创建租户schema
     * 
     * @param tenantSchema 租户schema名称
     * @return 是否创建成功
     */
    public boolean createTenantSchema(String tenantSchema) {
        if (!isValidSchemaName(tenantSchema)) {
            log.error("无效的schema名称: {}", tenantSchema);
            return false;
        }

        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            // 检查schema是否已存在
            if (schemaExists(connection, tenantSchema)) {
                log.warn("Schema已存在: {}", tenantSchema);
                return true;
            }

            // 创建schema
            String createSchemaSql = String.format("CREATE SCHEMA IF NOT EXISTS %s", tenantSchema);
            statement.execute(createSchemaSql);
            
            log.info("成功创建租户schema: {}", tenantSchema);
            return true;
            
        } catch (SQLException e) {
            log.error("创建租户schema失败: {}", tenantSchema, e);
            return false;
        }
    }

    /**
     * 删除租户schema
     * 
     * @param tenantSchema 租户schema名称
     * @param cascade 是否级联删除
     * @return 是否删除成功
     */
    public boolean dropTenantSchema(String tenantSchema, boolean cascade) {
        if (!isValidSchemaName(tenantSchema)) {
            log.error("无效的schema名称: {}", tenantSchema);
            return false;
        }

        // 防止删除系统schema
        if (isSystemSchema(tenantSchema)) {
            log.error("不能删除系统schema: {}", tenantSchema);
            return false;
        }

        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            // 检查schema是否存在
            if (!schemaExists(connection, tenantSchema)) {
                log.warn("Schema不存在: {}", tenantSchema);
                return true;
            }

            // 删除schema
            String dropSchemaSql = String.format("DROP SCHEMA %s %s", 
                                                tenantSchema, 
                                                cascade ? "CASCADE" : "RESTRICT");
            statement.execute(dropSchemaSql);
            
            log.info("成功删除租户schema: {}", tenantSchema);
            return true;
            
        } catch (SQLException e) {
            log.error("删除租户schema失败: {}", tenantSchema, e);
            return false;
        }
    }

    /**
     * 检查schema是否存在
     * 
     * @param tenantSchema 租户schema名称
     * @return 是否存在
     */
    public boolean schemaExists(String tenantSchema) {
        try (Connection connection = dataSource.getConnection()) {
            return schemaExists(connection, tenantSchema);
        } catch (SQLException e) {
            log.error("检查schema存在性失败: {}", tenantSchema, e);
            return false;
        }
    }

    /**
     * 获取所有租户schema列表
     * 
     * @return 租户schema列表
     */
    public List<String> getAllTenantSchemas() {
        List<String> schemas = new ArrayList<>();
        
        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            String sql = "SELECT schema_name FROM information_schema.schemata " +
                        "WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast', 'public') " +
                        "AND schema_name NOT LIKE 'pg_%'";
            
            var resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                schemas.add(resultSet.getString("schema_name"));
            }
            
        } catch (SQLException e) {
            log.error("获取租户schema列表失败", e);
        }
        
        return schemas;
    }

    /**
     * 初始化租户schema的基础表结构
     * 
     * @param tenantSchema 租户schema名称
     * @param initSqlScript 初始化SQL脚本
     * @return 是否初始化成功
     */
    public boolean initializeTenantSchema(String tenantSchema, String initSqlScript) {
        if (!schemaExists(tenantSchema)) {
            log.error("Schema不存在，无法初始化: {}", tenantSchema);
            return false;
        }

        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            // 使用JDBC标准方法设置schema
            connection.setSchema(tenantSchema);
            
            // 执行初始化脚本
            String[] sqlStatements = initSqlScript.split(";");
            for (String sql : sqlStatements) {
                if (sql.trim().length() > 0) {
                    statement.execute(sql.trim());
                }
            }
            
            log.info("成功初始化租户schema: {}", tenantSchema);
            return true;
            
        } catch (SQLException e) {
            log.error("初始化租户schema失败: {}", tenantSchema, e);
            return false;
        }
    }

    /**
     * 检查schema是否存在（内部方法）
     */
    private boolean schemaExists(Connection connection, String tenantSchema) throws SQLException {
        String sql = "SELECT 1 FROM information_schema.schemata WHERE schema_name = ?";
        try (var preparedStatement = connection.prepareStatement(sql)) {
            preparedStatement.setString(1, tenantSchema);
            var resultSet = preparedStatement.executeQuery();
            return resultSet.next();
        }
    }

    /**
     * 验证schema名称是否有效
     */
    private boolean isValidSchemaName(String schema) {
        if (schema == null || schema.trim().isEmpty()) {
            return false;
        }
        
        String trimmedSchema = schema.trim();
        
        // 长度检查
        if (trimmedSchema.length() > 63) {
            return false;
        }
        
        // 正则表达式检查：只允许字母、数字、下划线，且不能以数字开头
        return trimmedSchema.matches("^[a-zA-Z_][a-zA-Z0-9_]*$");
    }

    /**
     * 判断是否为系统schema
     */
    private boolean isSystemSchema(String schema) {
        if (schema == null) {
            return false;
        }
        
        String lowerSchema = schema.toLowerCase();
        return "public".equals(lowerSchema) ||
               "information_schema".equals(lowerSchema) ||
               "pg_catalog".equals(lowerSchema) ||
               "pg_toast".equals(lowerSchema) ||
               lowerSchema.startsWith("pg_");
    }
}
