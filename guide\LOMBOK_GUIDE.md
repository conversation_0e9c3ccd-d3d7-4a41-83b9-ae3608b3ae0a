# Lombok 使用指南

本项目已在core模块中集成了Lombok 1.18.38（最新稳定版），并配置为API依赖，这意味着所有依赖core模块的其他模块都可以直接使用Lombok。

## 版本信息

- **Lombok版本**: 1.18.38 (最新稳定版)
- **配置方式**: 在`gradle/libs.versions.toml`中统一管理版本
- **依赖范围**: core模块使用`api`依赖，其他模块可直接使用

## 常用注解说明

### 1. 基础注解

#### @Data
最常用的注解，相当于以下注解的组合：
- `@Getter` - 生成所有字段的getter方法
- `@Setter` - 生成所有非final字段的setter方法
- `@ToString` - 生成toString方法
- `@EqualsAndHashCode` - 生成equals和hashCode方法
- `@RequiredArgsConstructor` - 生成必需参数的构造函数

```java
@Data
public class User {
    private Long id;
    private String username;
    private String email;
}
```

#### @Value
创建不可变对象，相当于：
- `@Getter`
- `@ToString`
- `@EqualsAndHashCode`
- `@AllArgsConstructor`
- 所有字段都是`private final`

```java
@Value
public class UserInfo {
    Long id;
    String username;
    String email;
}
```

### 2. 构造函数注解

#### @NoArgsConstructor
生成无参构造函数

#### @AllArgsConstructor
生成包含所有字段的构造函数

#### @RequiredArgsConstructor
生成包含所有`final`字段和标记为`@NonNull`字段的构造函数

```java
@RequiredArgsConstructor
public class UserService {
    private final UserRepository userRepository;
    @NonNull
    private final EmailService emailService;
    private String optionalField; // 不会包含在构造函数中
}
```

### 3. Builder模式

#### @Builder
生成建造者模式代码

```java
@Builder
@Data
public class User {
    private Long id;
    private String username;
    @Builder.Default
    private Integer status = 1; // 设置默认值
}

// 使用方式
User user = User.builder()
    .id(1L)
    .username("admin")
    .build();
```

### 4. 链式调用

#### @Accessors(chain = true)
支持链式调用setter方法

```java
@Data
@Accessors(chain = true)
public class User {
    private String username;
    private String email;
}

// 使用方式
User user = new User()
    .setUsername("admin")
    .setEmail("<EMAIL>");
```

### 5. 日志注解

#### @Slf4j
自动生成SLF4J日志对象

```java
@Slf4j
@Service
public class UserService {
    public void createUser(User user) {
        log.info("Creating user: {}", user.getUsername());
        log.debug("User details: {}", user);
    }
}
```

其他日志注解：
- `@Log` - java.util.logging
- `@Log4j` - Apache Log4j
- `@Log4j2` - Apache Log4j2
- `@CommonsLog` - Apache Commons Logging

### 6. 实用注解

#### @NonNull
自动生成null检查代码

```java
public void processUser(@NonNull String username) {
    // 如果username为null，会抛出NullPointerException
    System.out.println(username.toUpperCase());
}
```

#### @SneakyThrows
处理受检异常，无需显式try-catch或throws声明

```java
@SneakyThrows
public void readFile(String filename) {
    Files.readAllLines(Paths.get(filename)); // 不需要处理IOException
}
```

#### @Cleanup
自动关闭资源

```java
@SneakyThrows
public void processFile(String filename) {
    @Cleanup FileInputStream fis = new FileInputStream(filename);
    // 文件流会自动关闭
}
```

#### @UtilityClass
创建工具类，自动生成私有构造函数，所有方法都是静态的

```java
@UtilityClass
public class StringUtils {
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }
}
```

### 7. 高级注解

#### @With
为不可变对象创建"with"方法

```java
@With
@Value
public class User {
    String username;
    String email;
    Integer age;
}

// 使用方式
User user = new User("admin", "<EMAIL>", 25);
User updatedUser = user.withAge(26); // 创建新对象，只修改age
```

#### @ToString.Exclude / @EqualsAndHashCode.Exclude
排除特定字段

```java
@Data
public class User {
    private String username;
    @ToString.Exclude
    private String password; // 不包含在toString中
    @EqualsAndHashCode.Exclude
    private String remark; // 不包含在equals和hashCode中
}
```

## 配置说明

### IDE配置
确保IDE安装了Lombok插件：
- **IntelliJ IDEA**: 安装Lombok插件并启用注解处理
- **Eclipse**: 安装Lombok并重启IDE
- **VS Code**: 安装Java扩展包

### 编译配置
项目已自动配置注解处理器：
```gradle
dependencies {
    api libs.lombok
    annotationProcessor libs.lombok
}
```

## 最佳实践

1. **合理使用@Data**: 对于简单的数据类使用@Data，复杂类考虑单独使用具体注解
2. **Builder模式**: 对于参数较多的类使用@Builder
3. **不可变对象**: 对于值对象使用@Value
4. **日志记录**: 统一使用@Slf4j
5. **空值检查**: 在关键方法参数上使用@NonNull
6. **排除敏感字段**: 在toString中排除密码等敏感信息

## 注意事项

1. Lombok生成的代码在编译时生成，IDE需要插件支持才能正确识别
2. 使用@Data时要注意继承关系，可能需要使用@EqualsAndHashCode(callSuper = true)
3. 在JPA实体类中使用时要小心，避免在toString中包含懒加载字段
4. @Builder与继承一起使用时可能需要额外配置
