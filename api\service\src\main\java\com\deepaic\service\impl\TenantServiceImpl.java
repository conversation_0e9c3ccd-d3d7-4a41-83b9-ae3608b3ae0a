package com.deepaic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.entity.Tenant;
import com.deepaic.core.mapper.TenantMapper;
import com.deepaic.core.dto.TenantDTO;
import com.deepaic.core.dto.TenantQueryDTO;
import com.deepaic.service.ITenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 租户服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantServiceImpl extends BaseServiceImpl<TenantMapper, Tenant> implements ITenantService {

    private final TenantMapper tenantMapper;

    @Override
    @Transactional
    public Long createTenant(TenantDTO tenantDTO) {
        // 检查租户代码是否唯一
        if (existsByTenantCode(tenantDTO.getTenantCode())) {
            throw new RuntimeException("租户代码已存在: " + tenantDTO.getTenantCode());
        }

        // 检查Schema名称是否唯一
        if (existsBySchemaName(tenantDTO.getSchemaName())) {
            throw new RuntimeException("Schema名称已存在: " + tenantDTO.getSchemaName());
        }

        Tenant tenant = new Tenant();
        BeanUtils.copyProperties(tenantDTO, tenant);
        
        // 设置默认值
        if (tenant.getStatus() == null) {
            tenant.setStatus(Tenant.STATUS_ENABLED); // 启用状态
        }
        if (tenant.getTenantType() == null) {
            tenant.setTenantType(Tenant.TYPE_TRIAL); // 默认类型
        }

        boolean success = save(tenant);
        if (!success) {
            throw new RuntimeException("创建租户失败");
        }

        log.info("创建租户成功: id={}, tenantCode={}", tenant.getId(), tenant.getTenantCode());
        return tenant.getId();
    }

    @Override
    @Transactional
    public boolean updateTenant(Long id, TenantDTO tenantDTO) {
        Tenant existingTenant = getById(id);
        if (existingTenant == null) {
            throw new RuntimeException("租户不存在: " + id);
        }

        // 检查租户代码是否唯一（排除当前租户）
        if (!tenantDTO.getTenantCode().equals(existingTenant.getTenantCode()) 
            && existsByTenantCode(tenantDTO.getTenantCode())) {
            throw new RuntimeException("租户代码已存在: " + tenantDTO.getTenantCode());
        }

        // 检查Schema名称是否唯一（排除当前租户）
        if (!tenantDTO.getSchemaName().equals(existingTenant.getSchemaName()) 
            && existsBySchemaName(tenantDTO.getSchemaName())) {
            throw new RuntimeException("Schema名称已存在: " + tenantDTO.getSchemaName());
        }

        BeanUtils.copyProperties(tenantDTO, existingTenant, "id", "createTime", "version", "deleted");

        boolean success = updateById(existingTenant);
        if (success) {
            log.info("更新租户成功: id={}, tenantCode={}", id, existingTenant.getTenantCode());
        }
        return success;
    }

    @Override
    public TenantDTO getTenantById(Long id) {
        Tenant tenant = getById(id);
        if (tenant == null) {
            return null;
        }

        TenantDTO tenantDTO = new TenantDTO();
        BeanUtils.copyProperties(tenant, tenantDTO);
        return tenantDTO;
    }

    @Override
    public Tenant getTenantByCode(String tenantCode) {
        return tenantMapper.selectByTenantCode(tenantCode);
    }

    @Override
    public Tenant getTenantBySchemaName(String schemaName) {
        LambdaQueryWrapper<Tenant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Tenant::getSchemaName, schemaName);
        return getOne(wrapper);
    }

    @Override
    public IPage<TenantDTO> getTenantPage(Page<TenantDTO> page, TenantQueryDTO query) {
        return tenantMapper.selectPageWithStats(page, query);
    }

    @Override
    public List<Tenant> getEnabledTenants() {
        LambdaQueryWrapper<Tenant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Tenant::getStatus, 1);
        return list(wrapper);
    }

    @Override
    @Transactional
    public boolean deleteTenant(Long id) {
        Tenant tenant = getById(id);
        if (tenant == null) {
            return false;
        }

        boolean success = removeById(id);
        if (success) {
            log.info("删除租户成功: id={}, tenantCode={}", id, tenant.getTenantCode());
        }
        return success;
    }

    @Override
    @Transactional
    public boolean deleteTenants(List<Long> ids) {
        boolean success = removeByIds(ids);
        if (success) {
            log.info("批量删除租户成功: count={}", ids.size());
        }
        return success;
    }

    @Override
    public boolean enableTenant(Long id) {
        return updateTenantStatus(id, Tenant.STATUS_ENABLED);
    }

    @Override
    public boolean disableTenant(Long id) {
        return updateTenantStatus(id, Tenant.STATUS_DISABLED);
    }

    @Override
    public boolean suspendTenant(Long id) {
        return updateTenantStatus(id, Tenant.STATUS_SUSPENDED);
    }

    @Override
    public boolean expireTenant(Long id) {
        return updateTenantStatus(id, Tenant.STATUS_EXPIRED);
    }

    private boolean updateTenantStatus(Long id, Short status) {
        Tenant tenant = getById(id);
        if (tenant == null) {
            return false;
        }

        tenant.setStatus(status);
        boolean success = updateById(tenant);
        if (success) {
            log.info("更新租户状态成功: id={}, status={}", id, status);
        }
        return success;
    }

    @Override
    public boolean existsByTenantCode(String tenantCode) {
        LambdaQueryWrapper<Tenant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Tenant::getTenantCode, tenantCode);
        return count(wrapper) > 0;
    }

    @Override
    public boolean existsBySchemaName(String schemaName) {
        LambdaQueryWrapper<Tenant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Tenant::getSchemaName, schemaName);
        return count(wrapper) > 0;
    }

    @Override
    public boolean existsByTenantName(String tenantName) {
        LambdaQueryWrapper<Tenant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Tenant::getTenantName, tenantName);
        return count(wrapper) > 0;
    }

    @Override
    public List<Object> countByStatus() {
        return tenantMapper.countByStatus();
    }

    @Override
    public List<Object> countByType() {
        return tenantMapper.countByType();
    }

    @Override
    public List<Tenant> getExpiringSoon(int days) {
        LocalDateTime expireTime = LocalDateTime.now().plusDays(days);
        return tenantMapper.selectExpiringSoon(expireTime);
    }

    @Override
    public List<Tenant> getTrialExpiringSoon(int days) {
        LocalDateTime expireTime = LocalDateTime.now().plusDays(days);
        return tenantMapper.selectTrialExpiringSoon(expireTime);
    }

    @Override
    public boolean updateUserCount(String tenantCode, Integer userCount) {
        // TODO: 实现用户数量更新逻辑
        // 当前表结构中没有user_count字段，需要通过其他方式实现
        log.info("更新租户用户数量: tenantCode={}, userCount={}", tenantCode, userCount);
        return true;
    }

    @Override
    public boolean updateStorageUsed(String tenantCode, Long storageUsed) {
        // TODO: 实现存储使用量更新逻辑
        // 当前表结构中没有storage_used字段，需要通过其他方式实现
        log.info("更新租户存储使用量: tenantCode={}, storageUsed={}", tenantCode, storageUsed);
        return true;
    }

    @Override
    public Object getTenantStats() {
        return tenantMapper.selectTenantStats();
    }

    @Override
    public List<Tenant> getUserLimitExceededTenants() {
        return tenantMapper.selectUserLimitExceeded();
    }

    @Override
    public List<Tenant> getStorageLimitExceededTenants() {
        return tenantMapper.selectStorageLimitExceeded();
    }

    @Override
    public Tenant getTenantByCustomDomain(String domain) {
        // TODO: 实现自定义域名查询
        // 当前表结构中没有custom_domain字段
        log.info("查询自定义域名租户: domain={}", domain);
        return null;
    }

    @Override
    public boolean initializeTenantSchema(String tenantCode) {
        // TODO: 实现Schema初始化逻辑
        log.info("初始化租户Schema: {}", tenantCode);
        return true;
    }

    @Override
    public boolean dropTenantSchema(String tenantCode) {
        // TODO: 实现Schema删除逻辑
        log.info("删除租户Schema: {}", tenantCode);
        return true;
    }

    @Override
    public boolean canCreateUser(String tenantCode) {
        Tenant tenant = getTenantByCode(tenantCode);
        if (tenant == null) {
            return false;
        }

        // TODO: 需要查询当前用户数量并与maxUsers比较
        // 当前表结构中没有current_users字段，需要通过查询用户表获取
        return tenant.getMaxUsers() == null || tenant.getMaxUsers() > 0;
    }

    @Override
    public boolean hasEnoughStorage(String tenantCode, Long requiredStorage) {
        Tenant tenant = getTenantByCode(tenantCode);
        if (tenant == null) {
            return false;
        }

        if (tenant.getMaxStorage() == null) {
            return true; // 无限制
        }

        // TODO: 需要查询当前存储使用量并与maxStorage比较
        // 当前表结构中没有storage_used字段，需要通过其他方式获取
        return requiredStorage <= tenant.getMaxStorage();
    }

    @Override
    public boolean renewTenantService(Long tenantId, int months) {
        Tenant tenant = getById(tenantId);
        if (tenant == null) {
            return false;
        }

        LocalDateTime newExpireTime = tenant.getExpireTime() != null 
            ? tenant.getExpireTime().plusMonths(months)
            : LocalDateTime.now().plusMonths(months);

        tenant.setExpireTime(newExpireTime);
        return updateById(tenant);
    }

    @Override
    public boolean upgradeTenantType(Long tenantId, Short newType) {
        Tenant tenant = getById(tenantId);
        if (tenant == null) {
            return false;
        }

        tenant.setTenantType(newType);
        return updateById(tenant);
    }

    @Override
    public boolean sendExpirationReminder(Long tenantId) {
        // TODO: 实现到期提醒逻辑
        log.info("发送租户到期提醒: {}", tenantId);
        return true;
    }

    @Override
    public void processExpiredTenants() {
        // TODO: 实现过期租户处理逻辑
        log.info("处理过期租户");
    }

    @Override
    public boolean syncTenantUserCount(String tenantCode) {
        // TODO: 实现用户数量同步逻辑
        log.info("同步租户用户数量: {}", tenantCode);
        return true;
    }

    @Override
    public boolean syncTenantStorageUsage(String tenantCode) {
        // TODO: 实现存储使用量同步逻辑
        log.info("同步租户存储使用量: {}", tenantCode);
        return true;
    }

    @Override
    public List<Tenant> getActiveTenants() {
        LambdaQueryWrapper<Tenant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Tenant::getStatus, 1)
               .isNull(Tenant::getExpireTime)
               .or()
               .gt(Tenant::getExpireTime, LocalDateTime.now());
        return list(wrapper);
    }
}
