package com.deepaic.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.entity.Member;
import com.deepaic.core.dto.MemberDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会员Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberMapper extends BaseMapper<Member> {

    /**
     * 查询会员列表（带分页）
     */
    IPage<MemberDTO> selectMemberPage(Page<MemberDTO> page, @Param("query") MemberDTO.MemberQueryDTO query);

    /**
     * 查询所有会员列表
     */
    List<MemberDTO> selectMemberList(@Param("query") MemberDTO.MemberQueryDTO query);

    /**
     * 根据微信OpenID查询会员
     */
    MemberDTO selectByWxOpenid(@Param("wxOpenid") String wxOpenid);

    /**
     * 根据微信UnionID查询会员
     */
    MemberDTO selectByWxUnionid(@Param("wxUnionid") String wxUnionid);

    /**
     * 根据手机号查询会员
     */
    MemberDTO selectByPhone(@Param("phone") String phone);

    /**
     * 根据会员编号查询会员
     */
    MemberDTO selectByMemberNo(@Param("memberNo") String memberNo);

    /**
     * 查询会员详情
     */
    MemberDTO selectMemberById(@Param("id") Long id);

    /**
     * 检查会员编号是否唯一
     */
    Integer checkMemberNoUnique(@Param("memberNo") String memberNo, @Param("id") Long id);

    /**
     * 检查手机号是否唯一
     */
    Integer checkPhoneUnique(@Param("phone") String phone, @Param("id") Long id);

    /**
     * 检查微信OpenID是否唯一
     */
    Integer checkWxOpenidUnique(@Param("wxOpenid") String wxOpenid, @Param("id") Long id);

    /**
     * 更新会员最后登录信息
     */
    int updateLastLoginInfo(@Param("id") Long id, @Param("loginTime") java.time.LocalDateTime loginTime, @Param("loginIp") String loginIp);

    /**
     * 更新会员积分
     */
    int updateMemberPoints(@Param("id") Long id, @Param("points") Integer points);

    /**
     * 更新会员余额
     */
    int updateMemberBalance(@Param("id") Long id, @Param("balance") Long balance);

    /**
     * 更新会员等级
     */
    int updateMemberLevel(@Param("id") Long id, @Param("levelId") Long levelId, @Param("levelName") String levelName);

    /**
     * 统计会员数量
     */
    Integer countMembers(@Param("query") MemberDTO.MemberQueryDTO query);

    /**
     * 根据推荐人查询下级会员
     */
    List<MemberDTO> selectMembersByReferrerId(@Param("referrerId") Long referrerId);

    /**
     * 统计推荐会员数量
     */
    Integer countReferralMembers(@Param("referrerId") Long referrerId);
}
