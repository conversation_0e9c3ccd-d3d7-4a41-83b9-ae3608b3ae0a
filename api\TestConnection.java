import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class TestConnection {
    public static void main(String[] args) {
        String url = "*************************************************************";
        String username = "postgres";
        String password = "deepaic!2025";
        
        try {
            Class.forName("org.postgresql.Driver");
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("数据库连接成功！");
            connection.close();
        } catch (ClassNotFoundException e) {
            System.out.println("PostgreSQL驱动未找到: " + e.getMessage());
        } catch (SQLException e) {
            System.out.println("数据库连接失败: " + e.getMessage());
        }
    }
}
