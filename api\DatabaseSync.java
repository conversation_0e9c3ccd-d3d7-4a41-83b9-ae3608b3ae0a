import java.sql.*;
import java.util.*;
import java.io.*;

/**
 * 数据库表结构同步工具
 * 连接到实际数据库，获取表结构并生成对应的实体类
 */
public class DatabaseSync {
    
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres";
    private static final String DB_PASSWORD = "deepaic!2025";
    
    public static void main(String[] args) {
        try {
            Class.forName("org.postgresql.Driver");
            
            try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
                System.out.println("成功连接到数据库: " + DB_URL);
                
                // 获取所有schema
                List<String> schemas = getSchemas(conn);
                System.out.println("发现的schemas: " + schemas);
                
                Map<String, List<TableInfo>> allTables = new HashMap<>();
                
                // 检查每个schema中的表
                for (String schema : schemas) {
                    if (!"information_schema".equals(schema) && !schema.startsWith("pg_")) {
                        List<TableInfo> tables = getTablesInSchema(conn, schema);
                        if (!tables.isEmpty()) {
                            allTables.put(schema, tables);
                            System.out.println("\n=== Schema: " + schema + " ===");
                            for (TableInfo table : tables) {
                                System.out.println("表: " + table.tableName);
                                printTableStructure(conn, schema, table.tableName);
                            }
                        }
                    }
                }
                
                // 生成实体类建议
                generateEntitySuggestions(allTables);
                
            }
        } catch (Exception e) {
            System.err.println("错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static List<String> getSchemas(Connection conn) throws SQLException {
        List<String> schemas = new ArrayList<>();
        String sql = "SELECT schema_name FROM information_schema.schemata ORDER BY schema_name";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                schemas.add(rs.getString("schema_name"));
            }
        }
        
        return schemas;
    }
    
    private static List<TableInfo> getTablesInSchema(Connection conn, String schema) throws SQLException {
        List<TableInfo> tables = new ArrayList<>();
        String sql = "SELECT table_name FROM information_schema.tables WHERE table_schema = ? ORDER BY table_name";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, schema);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    TableInfo table = new TableInfo();
                    table.schemaName = schema;
                    table.tableName = rs.getString("table_name");
                    tables.add(table);
                }
            }
        }
        
        return tables;
    }
    
    private static void printTableStructure(Connection conn, String schema, String tableName) throws SQLException {
        String sql = """
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length,
                numeric_precision,
                numeric_scale
            FROM information_schema.columns 
            WHERE table_schema = ? AND table_name = ? 
            ORDER BY ordinal_position
        """;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, schema);
            stmt.setString(2, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String columnName = rs.getString("column_name");
                    String dataType = rs.getString("data_type");
                    String nullable = rs.getString("is_nullable");
                    String defaultValue = rs.getString("column_default");
                    Integer maxLength = rs.getObject("character_maximum_length", Integer.class);
                    
                    System.out.printf("  %-20s %-20s %-8s %s%n", 
                        columnName, 
                        dataType + (maxLength != null ? "(" + maxLength + ")" : ""),
                        "YES".equals(nullable) ? "NULL" : "NOT NULL",
                        defaultValue != null ? "DEFAULT " + defaultValue : ""
                    );
                }
            }
        }
    }
    
    private static void generateEntitySuggestions(Map<String, List<TableInfo>> allTables) {
        System.out.println("\n=== 实体类生成建议 ===");
        
        for (Map.Entry<String, List<TableInfo>> entry : allTables.entrySet()) {
            String schema = entry.getKey();
            List<TableInfo> tables = entry.getValue();
            
            System.out.println("\nSchema: " + schema);
            
            for (TableInfo table : tables) {
                String className = generateClassName(table.tableName);
                System.out.println("  表: " + table.tableName + " -> 实体类: " + className);
                System.out.println("    @TableName(\"" + table.tableName + "\")");
                
                // 检查是否需要创建或删除实体类
                String entityPath = "core/src/main/java/com/deepaic/core/entity/" + className + ".java";
                System.out.println("    文件路径: " + entityPath);
            }
        }
        
        // 列出应该删除的实体类
        System.out.println("\n=== 需要检查的现有实体类 ===");
        System.out.println("请检查以下实体类是否有对应的数据库表，如果没有请删除：");
        
        // 这里列出一些可能存在但没有对应表的实体类
        String[] possibleEntities = {
            "Department", "Position", "DataPermission", "TenantAdmin"
        };
        
        for (String entity : possibleEntities) {
            System.out.println("  " + entity + ".java - 检查是否有对应的数据库表");
        }
    }
    
    private static String generateClassName(String tableName) {
        // 移除前缀并转换为驼峰命名
        String name = tableName.replaceFirst("^(pub_|sys_)", "");
        return toCamelCase(name);
    }
    
    private static String toCamelCase(String str) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;
        
        for (char c : str.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }
        
        return result.toString();
    }
    
    static class TableInfo {
        String schemaName;
        String tableName;
    }
}
