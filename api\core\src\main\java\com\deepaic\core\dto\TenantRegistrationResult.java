package com.deepaic.core.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 租户注册结果DTO
 * 包含注册结果和相关信息
 *
 * <AUTHOR>
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class TenantRegistrationResult {

    /**
     * 注册是否成功
     */
    private boolean success;

    /**
     * 结果消息
     */
    private String message;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 管理员账户ID
     */
    private Long adminAccountId;

    /**
     * 管理员用户名
     */
    private String adminUsername;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 审核状态描述
     */
    private String auditStatusDesc;

    /**
     * 试用到期时间
     */
    private LocalDateTime trialExpireTime;

    /**
     * 登录地址
     */
    private String loginUrl;

    /**
     * 激活链接（如果需要邮箱激活）
     */
    private String activationUrl;

    /**
     * 注册时间
     */
    private LocalDateTime registrationTime;

    /**
     * 下一步操作提示
     */
    private String nextStepTip;

    // 审核状态常量
    public static final int AUDIT_STATUS_PENDING = 0;      // 待审核
    public static final int AUDIT_STATUS_APPROVED = 1;     // 已通过
    public static final int AUDIT_STATUS_REJECTED = 2;     // 已拒绝
    public static final int AUDIT_STATUS_AUTO_APPROVED = 3; // 自动通过

    // 错误码常量
    public static final String ERROR_TENANT_CODE_EXISTS = "TENANT_CODE_EXISTS";
    public static final String ERROR_COMPANY_EXISTS = "COMPANY_EXISTS";
    public static final String ERROR_USERNAME_EXISTS = "USERNAME_EXISTS";
    public static final String ERROR_EMAIL_EXISTS = "EMAIL_EXISTS";
    public static final String ERROR_PHONE_EXISTS = "PHONE_EXISTS";
    public static final String ERROR_SMS_CODE_INVALID = "SMS_CODE_INVALID";
    public static final String ERROR_EMAIL_CODE_INVALID = "EMAIL_CODE_INVALID";
    public static final String ERROR_CAPTCHA_INVALID = "CAPTCHA_INVALID";
    public static final String ERROR_REGISTRATION_LIMIT = "REGISTRATION_LIMIT";
    public static final String ERROR_SYSTEM_ERROR = "SYSTEM_ERROR";

    /**
     * 创建成功结果
     */
    public static TenantRegistrationResult success(Long tenantId, String tenantCode, String tenantName,
                                                  Long adminAccountId, String adminUsername) {
        return TenantRegistrationResult.builder()
                .success(true)
                .message("租户注册成功")
                .tenantId(tenantId)
                .tenantCode(tenantCode)
                .tenantName(tenantName)
                .adminAccountId(adminAccountId)
                .adminUsername(adminUsername)
                .auditStatus(AUDIT_STATUS_AUTO_APPROVED)
                .auditStatusDesc("自动通过")
                .registrationTime(LocalDateTime.now())
                .nextStepTip("请使用管理员账户登录系统")
                .build();
    }

    /**
     * 创建待审核结果
     */
    public static TenantRegistrationResult pending(Long tenantId, String tenantCode, String tenantName) {
        return TenantRegistrationResult.builder()
                .success(true)
                .message("租户注册成功，等待审核")
                .tenantId(tenantId)
                .tenantCode(tenantCode)
                .tenantName(tenantName)
                .auditStatus(AUDIT_STATUS_PENDING)
                .auditStatusDesc("待审核")
                .registrationTime(LocalDateTime.now())
                .nextStepTip("请等待管理员审核，审核通过后将通过邮件或短信通知您")
                .build();
    }

    /**
     * 创建失败结果
     */
    public static TenantRegistrationResult failure(String message, String errorCode) {
        return TenantRegistrationResult.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .registrationTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建需要激活的结果
     */
    public static TenantRegistrationResult needActivation(Long tenantId, String tenantCode, 
                                                         String tenantName, String activationUrl) {
        return TenantRegistrationResult.builder()
                .success(true)
                .message("租户注册成功，请激活邮箱")
                .tenantId(tenantId)
                .tenantCode(tenantCode)
                .tenantName(tenantName)
                .auditStatus(AUDIT_STATUS_PENDING)
                .auditStatusDesc("待激活")
                .activationUrl(activationUrl)
                .registrationTime(LocalDateTime.now())
                .nextStepTip("请查收邮件并点击激活链接完成注册")
                .build();
    }

    /**
     * 获取审核状态描述
     */
    public String getAuditStatusDescription() {
        return switch (auditStatus) {
            case AUDIT_STATUS_PENDING -> "待审核";
            case AUDIT_STATUS_APPROVED -> "已通过";
            case AUDIT_STATUS_REJECTED -> "已拒绝";
            case AUDIT_STATUS_AUTO_APPROVED -> "自动通过";
            default -> "未知状态";
        };
    }

    /**
     * 检查是否需要审核
     */
    public boolean needsAudit() {
        return AUDIT_STATUS_PENDING == auditStatus;
    }

    /**
     * 检查是否已通过审核
     */
    public boolean isApproved() {
        return AUDIT_STATUS_APPROVED == auditStatus || AUDIT_STATUS_AUTO_APPROVED == auditStatus;
    }

    /**
     * 检查是否被拒绝
     */
    public boolean isRejected() {
        return AUDIT_STATUS_REJECTED == auditStatus;
    }
}
