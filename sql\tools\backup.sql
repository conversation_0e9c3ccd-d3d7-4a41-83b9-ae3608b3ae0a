-- =====================================================
-- 美姿姿健康管理系统 - 数据库备份脚本
-- 版本: 1.0.0
-- 创建时间: 2025-06-27
-- 作者: 美姿姿团队
-- 说明: 数据库备份和恢复工具
-- =====================================================

-- 此文件包含备份相关的SQL函数和过程
-- 实际备份需要使用 pg_dump 命令行工具

-- ==============================================
-- 创建备份管理表
-- ==============================================

CREATE SCHEMA IF NOT EXISTS backup;

-- 备份记录表
CREATE TABLE IF NOT EXISTS backup.backup_history (
    id BIGSERIAL PRIMARY KEY,
    backup_name VARCHAR(200) NOT NULL,
    backup_type VARCHAR(50) NOT NULL, -- full, schema, data, tenant
    backup_scope VARCHAR(100), -- 备份范围（如租户代码）
    file_path TEXT,
    file_size BIGINT,
    backup_status VARCHAR(20) DEFAULT 'running', -- running, completed, failed
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTERVAL,
    error_message TEXT,
    created_by VARCHAR(100) DEFAULT current_user,
    remark TEXT
);

COMMENT ON TABLE backup.backup_history IS '备份历史记录表';

-- ==============================================
-- 备份管理函数
-- ==============================================

-- 记录备份开始
CREATE OR REPLACE FUNCTION backup.start_backup(
    p_backup_name VARCHAR(200),
    p_backup_type VARCHAR(50),
    p_backup_scope VARCHAR(100) DEFAULT NULL,
    p_remark TEXT DEFAULT NULL
)
RETURNS BIGINT
LANGUAGE plpgsql
AS $$
DECLARE
    backup_id BIGINT;
BEGIN
    INSERT INTO backup.backup_history (
        backup_name, backup_type, backup_scope, remark
    ) VALUES (
        p_backup_name, p_backup_type, p_backup_scope, p_remark
    ) RETURNING id INTO backup_id;
    
    RAISE NOTICE '备份任务开始: ID=%, 名称=%', backup_id, p_backup_name;
    RETURN backup_id;
END $$;

-- 记录备份完成
CREATE OR REPLACE FUNCTION backup.complete_backup(
    p_backup_id BIGINT,
    p_file_path TEXT,
    p_file_size BIGINT DEFAULT NULL,
    p_error_message TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    start_time TIMESTAMP;
    backup_status VARCHAR(20);
BEGIN
    -- 获取开始时间
    SELECT backup_history.start_time INTO start_time 
    FROM backup.backup_history 
    WHERE id = p_backup_id;
    
    IF start_time IS NULL THEN
        RAISE EXCEPTION '备份任务不存在: %', p_backup_id;
    END IF;
    
    -- 确定状态
    backup_status := CASE WHEN p_error_message IS NULL THEN 'completed' ELSE 'failed' END;
    
    -- 更新记录
    UPDATE backup.backup_history SET
        file_path = p_file_path,
        file_size = p_file_size,
        backup_status = backup_status,
        end_time = NOW(),
        duration = NOW() - start_time,
        error_message = p_error_message
    WHERE id = p_backup_id;
    
    RAISE NOTICE '备份任务完成: ID=%, 状态=%', p_backup_id, backup_status;
    RETURN backup_status = 'completed';
END $$;

-- 清理过期备份记录
CREATE OR REPLACE FUNCTION backup.cleanup_old_backups(
    p_keep_days INTEGER DEFAULT 30
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM backup.backup_history 
    WHERE start_time < NOW() - INTERVAL '1 day' * p_keep_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RAISE NOTICE '清理了 % 条过期备份记录（保留%天）', deleted_count, p_keep_days;
    RETURN deleted_count;
END $$;

-- ==============================================
-- 租户数据备份函数
-- ==============================================

-- 导出租户数据为INSERT语句
CREATE OR REPLACE FUNCTION backup.export_tenant_data(
    p_tenant_code VARCHAR(50)
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    schema_name VARCHAR(100);
    table_record RECORD;
    result_text TEXT := '';
    row_count INTEGER;
BEGIN
    -- 获取租户Schema名称
    SELECT t.schema_name INTO schema_name
    FROM public.pub_tenant t
    WHERE t.tenant_code = p_tenant_code;
    
    IF schema_name IS NULL THEN
        RAISE EXCEPTION '租户不存在: %', p_tenant_code;
    END IF;
    
    result_text := result_text || format('-- 租户 %s 数据导出\n', p_tenant_code);
    result_text := result_text || format('-- 导出时间: %s\n\n', NOW());
    
    -- 遍历租户Schema中的所有表
    FOR table_record IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = schema_name 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
    LOOP
        -- 获取表的行数
        EXECUTE format('SELECT COUNT(*) FROM %I.%I', schema_name, table_record.table_name)
        INTO row_count;
        
        IF row_count > 0 THEN
            result_text := result_text || format('-- 表: %s (行数: %s)\n', table_record.table_name, row_count);
            -- 这里可以添加具体的数据导出逻辑
            result_text := result_text || format('-- TODO: 导出 %I.%I 的数据\n\n', schema_name, table_record.table_name);
        END IF;
    END LOOP;
    
    RETURN result_text;
END $$;

-- ==============================================
-- 备份脚本示例
-- ==============================================

/*
-- 完整数据库备份
pg_dump -h localhost -U postgres -d beautiful_posture -f backup_full_$(date +%Y%m%d_%H%M%S).sql

-- 仅结构备份
pg_dump -h localhost -U postgres -d beautiful_posture -s -f backup_schema_$(date +%Y%m%d_%H%M%S).sql

-- 仅数据备份
pg_dump -h localhost -U postgres -d beautiful_posture -a -f backup_data_$(date +%Y%m%d_%H%M%S).sql

-- 特定Schema备份
pg_dump -h localhost -U postgres -d beautiful_posture -n tenant_demo -f backup_tenant_demo_$(date +%Y%m%d_%H%M%S).sql

-- 压缩备份
pg_dump -h localhost -U postgres -d beautiful_posture -Fc -f backup_compressed_$(date +%Y%m%d_%H%M%S).dump

-- 恢复数据库
psql -h localhost -U postgres -d beautiful_posture -f backup_full_20250627_120000.sql

-- 恢复压缩备份
pg_restore -h localhost -U postgres -d beautiful_posture backup_compressed_20250627_120000.dump
*/

-- ==============================================
-- 备份监控视图
-- ==============================================

-- 创建备份状态视图
CREATE OR REPLACE VIEW backup.backup_status AS
SELECT 
    id,
    backup_name,
    backup_type,
    backup_scope,
    backup_status,
    start_time,
    end_time,
    duration,
    CASE 
        WHEN file_size IS NULL THEN NULL
        WHEN file_size < 1024 THEN file_size || ' B'
        WHEN file_size < 1048576 THEN ROUND(file_size / 1024.0, 2) || ' KB'
        WHEN file_size < 1073741824 THEN ROUND(file_size / 1048576.0, 2) || ' MB'
        ELSE ROUND(file_size / 1073741824.0, 2) || ' GB'
    END AS file_size_formatted,
    created_by,
    error_message
FROM backup.backup_history
ORDER BY start_time DESC;

COMMENT ON VIEW backup.backup_status IS '备份状态监控视图';

-- ==============================================
-- 权限设置
-- ==============================================

-- 授予备份Schema权限
GRANT USAGE ON SCHEMA backup TO beautiful_posture_user;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA backup TO beautiful_posture_user;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA backup TO beautiful_posture_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA backup TO beautiful_posture_user;

-- 只读用户权限
GRANT USAGE ON SCHEMA backup TO beautiful_posture_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA backup TO beautiful_posture_readonly;

RAISE NOTICE '==============================================';
RAISE NOTICE '备份管理脚本执行完成';
RAISE NOTICE '已创建:';
RAISE NOTICE '- backup.backup_history 表';
RAISE NOTICE '- backup.backup_status 视图';
RAISE NOTICE '- 备份管理函数';
RAISE NOTICE '==============================================';
RAISE NOTICE '使用示例:';
RAISE NOTICE '-- 开始备份';
RAISE NOTICE 'SELECT backup.start_backup(''daily_backup'', ''full'');';
RAISE NOTICE '-- 完成备份';
RAISE NOTICE 'SELECT backup.complete_backup(1, ''/path/to/backup.sql'', 1048576);';
RAISE NOTICE '-- 查看备份状态';
RAISE NOTICE 'SELECT * FROM backup.backup_status;';
RAISE NOTICE '==============================================';

-- 显示备份历史
SELECT * FROM backup.backup_status LIMIT 5;
