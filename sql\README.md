# 美姿姿 - SQL脚本管理

## 📋 目录说明

本目录用于管理美姿姿健康管理系统的所有SQL脚本，包括数据库初始化、结构变更和数据迁移。

## 📁 目录结构

```
sql/
├── README.md                    # 本说明文档
├── init/                        # 🚀 系统初始化脚本
│   ├── 01_create_database.sql   # 创建数据库
│   ├── 02_create_schemas.sql    # 创建Schema
│   ├── 03_create_tables.sql     # 创建表结构
│   ├── 04_create_indexes.sql    # 创建索引
│   ├── 05_insert_data.sql       # 初始化数据
│   └── 99_init_complete.sql     # 初始化完成标记
├── migrations/                  # 🔄 数据库迁移脚本
│   ├── v1.0.0/                  # 版本1.0.0的变更
│   ├── v1.0.1/                  # 版本1.0.1的变更
│   └── v1.1.0/                  # 版本1.1.0的变更
├── functions/                   # 📦 存储过程和函数
│   ├── tenant_functions.sql     # 租户相关函数
│   ├── auth_functions.sql       # 认证相关函数
│   └── common_functions.sql     # 通用函数
├── views/                       # 👁️ 视图定义
│   ├── tenant_views.sql         # 租户相关视图
│   └── report_views.sql         # 报表相关视图
├── triggers/                    # ⚡ 触发器定义
│   ├── audit_triggers.sql       # 审计触发器
│   └── validation_triggers.sql  # 验证触发器
├── data/                        # 📊 基础数据
│   ├── system_config.sql        # 系统配置数据
│   ├── menu_data.sql            # 菜单数据
│   └── permission_data.sql      # 权限数据
└── tools/                       # 🛠️ 工具脚本
    ├── backup.sql               # 备份脚本
    ├── cleanup.sql              # 清理脚本
    └── performance.sql          # 性能优化脚本
```

## 🚀 使用方法

### 1. 系统初始化
```bash
# 按顺序执行初始化脚本
psql -h localhost -U postgres -d postgres -f sql/init/01_create_database.sql
psql -h localhost -U postgres -d beautiful_posture -f sql/init/02_create_schemas.sql
psql -h localhost -U postgres -d beautiful_posture -f sql/init/03_create_tables.sql
psql -h localhost -U postgres -d beautiful_posture -f sql/init/04_create_indexes.sql
psql -h localhost -U postgres -d beautiful_posture -f sql/init/05_insert_data.sql
```

### 2. 版本迁移
```bash
# 执行特定版本的迁移脚本
psql -h localhost -U postgres -d beautiful_posture -f sql/migrations/v1.0.1/migration.sql
```

### 3. Docker环境
```bash
# 使用Docker初始化
docker exec -i postgres_container psql -U postgres -d beautiful_posture < sql/init/03_create_tables.sql
```

## 📝 脚本命名规范

### 初始化脚本
- 使用两位数字前缀：`01_`, `02_`, `03_`...
- 描述性名称：`create_tables.sql`, `insert_data.sql`
- 按执行顺序命名

### 迁移脚本
- 版本目录：`v1.0.0/`, `v1.0.1/`, `v1.1.0/`
- 时间戳前缀：`20250627_add_user_table.sql`
- 描述性名称：说明变更内容

### 功能脚本
- 按功能模块分类：`tenant_`, `auth_`, `common_`
- 清晰的功能描述：`functions.sql`, `views.sql`

## 🔄 版本管理

### 版本号规则
- **主版本号**：重大架构变更
- **次版本号**：新功能添加
- **修订号**：Bug修复和小改动

### 迁移脚本规则
1. **向前兼容**：新脚本不能破坏现有功能
2. **可重复执行**：使用`IF NOT EXISTS`等语句
3. **回滚脚本**：提供对应的回滚脚本
4. **测试验证**：每个脚本都要经过测试

## 🛡️ 安全注意事项

### 权限控制
- 初始化脚本需要超级用户权限
- 迁移脚本使用最小权限原则
- 生产环境脚本需要审核

### 数据保护
- 重要操作前先备份
- 使用事务确保原子性
- 测试环境先验证

## 📊 多租户支持

### Schema管理
```sql
-- 创建租户Schema
CREATE SCHEMA IF NOT EXISTS tenant_001;
-- 设置搜索路径
SET search_path TO tenant_001, public;
```

### 数据隔离
- 公共表：`pub_*` (存储在public schema)
- 租户表：`sys_*` (存储在租户schema)
- 权限控制：基于schema的访问控制

## 🔧 工具脚本

### 备份脚本
```bash
# 备份整个数据库
pg_dump -h localhost -U postgres beautiful_posture > backup_$(date +%Y%m%d).sql
```

### 性能监控
```sql
-- 查看慢查询
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;
```

## 📈 最佳实践

### 1. 脚本编写
- 使用事务包装
- 添加错误处理
- 包含回滚逻辑
- 详细注释说明

### 2. 测试流程
- 本地环境测试
- 测试环境验证
- 生产环境部署
- 部署后验证

### 3. 文档维护
- 更新变更日志
- 记录依赖关系
- 维护版本历史

## 🔗 相关文档

- [PostgreSQL多租户架构指南](../guide/POSTGRESQL_MULTITENANT_GUIDE.md)
- [Docker部署指南](../guide/DOCKER_DEPLOYMENT_GUIDE.md)
- [数据库配置指南](../guide/API_DATABASE_CONFIGURATION.md)

---

**注意**: 执行SQL脚本前请确保已备份重要数据，并在测试环境中验证脚本的正确性。
