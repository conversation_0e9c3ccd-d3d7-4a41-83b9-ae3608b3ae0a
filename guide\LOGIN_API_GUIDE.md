# 美姿姿 - 登录接口优化指南

## 📋 概述

根据登录规则，为三个API模块优化了登录接口，实现了统一的认证架构和JWT鉴权机制。

## 🔐 登录规则实现

### Platform API (SaaS平台)
- **用户表**: `pub_platform_account`
- **登录方式**: 用户名 + 密码
- **鉴权方式**: JWT (基于Sa-Token)
- **端口**: 8082

### Admin API (租户管理)
- **用户表**: `pub_customer_account`
- **登录方式**: 
  - 用户名/手机号 + 密码
  - 手机号 + 短信验证码
  - 微信授权登录
- **鉴权方式**: JWT (基于Sa-Token)
- **端口**: 8080

### App API (小程序)
- **用户表**: `pub_member_account`
- **登录方式**: 
  - 微信小程序授权登录
  - 手机号 + 短信验证码
- **鉴权方式**: JWT (基于Sa-Token)
- **端口**: 8081

## 🚀 API接口详情

### 1. Platform API 登录接口

#### 基础路径
```
http://localhost:8082/platform/auth
```

#### 1.1 平台用户登录
```http
POST /platform/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expiresIn": 2592000,
    "userInfo": {
      "userId": "1",
      "username": "admin",
      "userType": "PLATFORM",
      "roles": ["platform-admin"]
    }
  }
}
```

#### 1.2 平台用户登出
```http
POST /platform/auth/logout
Authorization: Bearer {accessToken}
```

#### 1.3 获取当前用户信息
```http
GET /platform/auth/current-user
Authorization: Bearer {accessToken}
```

#### 1.4 检查登录状态
```http
GET /platform/auth/check-login
```

#### 1.5 刷新令牌
```http
POST /platform/auth/refresh-token
Content-Type: application/json

{
  "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 2. Admin API 登录接口

#### 基础路径
```
http://localhost:8080/api/admin/auth
```

#### 2.1 租户用户密码登录
```http
POST /api/admin/auth/login
Content-Type: application/json

{
  "username": "user001",
  "password": "123456",
  "tenantCode": "tenant001"
}
```

#### 2.2 租户用户手机号登录
```http
POST /api/admin/auth/phone-login
Content-Type: application/json

{
  "phone": "13800138000",
  "smsCode": "123456",
  "tenantCode": "tenant001"
}
```

#### 2.3 租户用户微信登录
```http
POST /api/admin/auth/wechat-login
Content-Type: application/json

{
  "code": "wx_auth_code",
  "tenantCode": "tenant001",
  "platform": 1
}
```

#### 2.4 租户用户登出
```http
POST /api/admin/auth/logout
Authorization: Bearer {accessToken}
```

#### 2.5 获取当前用户信息
```http
GET /api/admin/auth/current-user
Authorization: Bearer {accessToken}
```

#### 2.6 检查登录状态
```http
GET /api/admin/auth/check-login
```

#### 2.7 刷新令牌
```http
POST /api/admin/auth/refresh-token
Content-Type: application/json

{
  "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 3. App API 登录接口

#### 基础路径
```
http://localhost:8081/api/app/auth
```

#### 3.1 微信小程序登录
```http
POST /api/app/auth/wechat-mini-login
Content-Type: application/json

{
  "code": "wx_mini_auth_code",
  "tenantCode": "tenant001",
  "referrerNo": "M001"
}
```

#### 3.2 会员手机号登录
```http
POST /api/app/auth/phone-login
Content-Type: application/json

{
  "phone": "13800138000",
  "smsCode": "123456",
  "tenantCode": "tenant001",
  "referrerNo": "M001"
}
```

#### 3.3 会员登出
```http
POST /api/app/auth/logout
Authorization: Bearer {accessToken}
```

#### 3.4 获取当前会员信息
```http
GET /api/app/auth/current-member
Authorization: Bearer {accessToken}
```

#### 3.5 获取登录信息
```http
GET /api/app/auth/login-info
Authorization: Bearer {accessToken}
```

## 🔧 技术实现

### 1. 统一认证架构
- **AuthenticationManager**: 统一认证管理器
- **AuthRequest**: 统一认证请求模型
- **AuthResponse**: 统一认证响应模型
- **UserPrincipal**: 用户主体信息模型

### 2. 认证服务实现
- **PlatformAuthService**: 平台用户认证服务
- **CustomerAuthService**: 租户用户认证服务
- **MemberAuthService**: 会员用户认证服务

### 3. 令牌管理
- **TokenService**: 令牌服务接口
- **SaTokenServiceImpl**: 基于Sa-Token的令牌服务实现

### 4. 认证类型常量
```java
// 平台认证
TYPE_PLATFORM_PASSWORD = "platform_password"

// 租户认证
TYPE_CUSTOMER_PASSWORD = "customer_password"
TYPE_CUSTOMER_PHONE = "customer_phone"
TYPE_CUSTOMER_WECHAT = "customer_wechat"

// 会员认证
TYPE_MEMBER_WECHAT = "member_wechat"
TYPE_MEMBER_PHONE = "member_phone"
```

## 🛡️ 安全特性

### 1. 密码安全
- 密码加密存储
- 登录失败次数限制
- 账户锁定机制

### 2. 令牌安全
- JWT令牌签名验证
- 令牌过期时间控制
- 令牌撤销机制

### 3. 多租户隔离
- 租户代码验证
- 数据库Schema隔离
- 租户上下文管理

## 📝 使用示例

### 前端调用示例 (JavaScript)
```javascript
// 平台用户登录
const platformLogin = async (username, password) => {
  const response = await fetch('http://localhost:8082/platform/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ username, password })
  });
  return response.json();
};

// 租户用户登录
const customerLogin = async (username, password, tenantCode) => {
  const response = await fetch('http://localhost:8080/api/admin/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ username, password, tenantCode })
  });
  return response.json();
};

// 小程序会员登录
const memberLogin = async (code, tenantCode) => {
  const response = await fetch('http://localhost:8081/api/app/auth/wechat-mini-login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ code, tenantCode })
  });
  return response.json();
};
```

## 🔍 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 用户不存在 |
| 423 | 账户被锁定 |
| 500 | 服务器内部错误 |

## 📚 相关文档

- [多租户架构指南](MULTITENANT_ARCHITECTURE_GUIDE.md)
- [Sa-Token使用指南](SATOKEN_GUIDE.md)
- [API接口实现总结](API_INTERFACES_IMPLEMENTATION_SUMMARY.md)
- [认证架构优化](auth-architecture-optimization.md)
