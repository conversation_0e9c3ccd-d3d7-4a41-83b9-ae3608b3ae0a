# 美姿姿 开发指南

本目录包含美姿姿项目的详细开发和使用指南。

## 📚 文档目录

### 🏗️ 构建和部署
- [**Gradle构建指南**](GRADLE_BUILD_GUIDE.md) - 完整的项目构建、打包和部署指南
- [**依赖管理指南**](GRADLE_DEPENDENCY_MANAGEMENT.md) - Gradle统一版本管理说明
- [**Docker部署指南**](DOCKER_DEPLOYMENT_GUIDE.md) - 容器化部署完整指南

### 🛠️ 技术框架
- [**MyBatis-Plus指南**](MYBATIS_PLUS_GUIDE.md) - 数据访问层框架使用说明
- [**PostgreSQL多租户指南**](POSTGRESQL_MULTITENANT_GUIDE.md) - 多租户架构基础实现
- [**Lombok指南**](LOMBOK_GUIDE.md) - 代码简化工具使用指南
- [**API数据库配置**](API_DATABASE_CONFIGURATION.md) - API模块数据库配置说明

### 🏢 多租户架构专题
- [**多租户架构指南**](MULTITENANT_ARCHITECTURE_GUIDE.md) - 多租户架构详细指南
- [**多租户实现总结**](MULTITENANT_IMPLEMENTATION_SUMMARY.md) - 多租户实现过程总结
- [**多租户架构设计**](multi-tenant-architecture.md) - 完整的架构设计文档
- [**租户架构重组**](tenant-architecture-reorganization.md) - 架构重构过程记录
- [**租户清理总结**](tenant-cleanup-summary.md) - 代码清理和优化过程
- [**Schema设置优化**](schema-setting-optimization.md) - 数据库Schema切换优化
- [**架构优化总结**](architecture-optimization-summary.md) - 最终架构优化成果

### 📋 项目管理
- [**项目名称更新**](PROJECT_NAME_UPDATE.md) - 项目名称变更记录

### ☕ Java技术
- [**Java 21特性说明**](JAVA21_FEATURES.md) - 新版本特性介绍和最佳实践

## 🎯 快速导航

### 新手入门
1. 📖 [项目构建](GRADLE_BUILD_GUIDE.md#快速开始) - 如何构建和运行项目
2. 🔧 [环境配置](GRADLE_BUILD_GUIDE.md#配置管理) - 开发环境设置
3. 🐳 [Docker部署](../docker/README.md) - 容器化部署指南

### 开发指南
1. 💾 [数据访问](MYBATIS_PLUS_GUIDE.md) - 使用MyBatis-Plus进行数据操作
2. 🏢 [多租户开发](POSTGRESQL_MULTITENANT_GUIDE.md) - 多租户功能开发
3. 🛠️ [代码优化](LOMBOK_GUIDE.md) - 使用Lombok简化代码

### 高级主题
1. ⚡ [性能优化](JAVA21_FEATURES.md#性能优化建议) - Java 21性能特性
2. 🔄 [依赖管理](GRADLE_DEPENDENCY_MANAGEMENT.md) - 版本控制和依赖管理
3. 📦 [打包部署](GRADLE_BUILD_GUIDE.md#部署方式) - 生产环境部署

## 🏛️ 架构概览

### 技术栈
- **语言**: Java 21 LTS
- **框架**: Spring Boot 3.5.2
- **构建**: Gradle 8.13 + Version Catalogs
- **数据库**: PostgreSQL 15+ (多租户Schema)
- **ORM**: MyBatis-Plus 3.5.12
- **缓存**: Redis 7+ / Redisson
- **容器**: Docker + Docker Compose

### 模块结构
```
beautiful-posture/
├── api/                    # 🚀 API模块目录
│   ├── platform-api/      # SaaS平台管理API
│   ├── admin-api/          # 客户系统管理后台API
│   ├── app-api/            # 小程序后端API
│   ├── core/               # 核心公共模块
│   └── service/            # 业务服务模块
├── docker/                 # Docker部署文件
├── guide/                  # 开发指南文档
└── ui/                     # 前端相关(预留)
```

### 核心特性
- ✅ **多租户支持**: PostgreSQL Schema隔离
- ✅ **统一构建**: Gradle多模块管理
- ✅ **容器化**: Docker一键部署
- ✅ **代码生成**: MyBatis-Plus自动生成
- ✅ **现代Java**: Java 21新特性支持

## 📋 开发流程

### 1. 环境准备
```bash
# 检查Java版本
java -version  # 需要Java 21+

# 检查Gradle版本
./gradlew --version  # 需要Gradle 8.13+
```

### 2. 项目构建
```bash
# 构建所有模块
cd api && ./gradlew build

# 或使用构建脚本
cd docker && ./build.sh
```

### 3. 开发调试
```bash
# 启动开发环境
docker-compose -f docker/docker-compose.yml up -d postgres redis

# 运行特定API模块
cd api && ./gradlew :admin-api:bootRun
```

### 4. 测试验证
```bash
# 运行单元测试
./gradlew test

# 运行集成测试
./gradlew integrationTest
```

## 🔧 常用操作

### 添加新依赖
1. 在 `gradle/libs.versions.toml` 中定义版本
2. 在相应模块的 `build.gradle` 中引用
3. 详见 [依赖管理指南](GRADLE_DEPENDENCY_MANAGEMENT.md)

### 创建新实体
1. 继承 `BaseEntity` 基类
2. 使用 `@TableName` 指定表名
3. 详见 [MyBatis-Plus指南](MYBATIS_PLUS_GUIDE.md)

### 多租户开发
1. 使用 `SaTokenTenantContext` 和 `TenantUtils` 管理租户
2. 自动schema切换
3. 详见 [多租户指南](POSTGRESQL_MULTITENANT_GUIDE.md)
4. 架构设计参考 [多租户架构设计](multi-tenant-architecture.md)

### 代码简化
1. 使用 `@Data` 生成getter/setter
2. 使用 `@Builder` 构建对象
3. 详见 [Lombok指南](LOMBOK_GUIDE.md)

## 🐛 故障排除

### 构建问题
- 检查Java版本是否为21+
- 清理Gradle缓存: `./gradlew clean`
- 详见 [构建指南故障排除](GRADLE_BUILD_GUIDE.md#故障排除)

### 数据库问题
- 检查PostgreSQL连接配置
- 验证租户schema是否存在
- 详见 [多租户指南故障排除](POSTGRESQL_MULTITENANT_GUIDE.md#故障排除)

### Docker问题
- 检查端口冲突
- 清理Docker缓存
- 详见 [Docker指南](../docker/README.md#故障排除)

## 📖 学习路径

### 初级开发者
1. 🏗️ [项目构建基础](GRADLE_BUILD_GUIDE.md)
2. 💾 [数据访问入门](MYBATIS_PLUS_GUIDE.md)
3. 🛠️ [代码工具使用](LOMBOK_GUIDE.md)

### 中级开发者
1. 🏢 [多租户架构](POSTGRESQL_MULTITENANT_GUIDE.md)
2. 📦 [依赖版本管理](GRADLE_DEPENDENCY_MANAGEMENT.md)
3. 🐳 [容器化部署](../docker/README.md)

### 高级开发者
1. ☕ [Java 21新特性](JAVA21_FEATURES.md)
2. ⚡ [性能优化技巧](JAVA21_FEATURES.md#性能优化建议)
3. 🔄 [CI/CD集成](GRADLE_BUILD_GUIDE.md#持续集成)

## 🤝 贡献指南

### 文档贡献
1. 发现文档问题或改进建议
2. 提交Issue或Pull Request
3. 遵循Markdown格式规范

### 代码贡献
1. 阅读相关技术指南
2. 遵循项目代码规范
3. 编写单元测试
4. 更新相关文档

## 📞 获取帮助

- 📖 **文档问题**: 查看对应指南文档
- 🐛 **Bug报告**: 提交GitHub Issue
- 💡 **功能建议**: 参与GitHub Discussions
- 📧 **技术支持**: <EMAIL>

---

**Happy Coding!** 🚀

*美姿姿团队 - 让开发更美好*
