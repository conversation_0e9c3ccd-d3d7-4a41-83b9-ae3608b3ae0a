# 美姿姿项目 - 多租户架构设计

## 概述

本项目采用基于PostgreSQL Schema的多租户架构，结合Sa-Token认证框架，实现了优雅、简便的多租户数据隔离方案。

## 架构特点

### 1. 表结构设计
- **公共表**（存储在 `public` schema）：
  - `pub_account` - 用户登录账户表
  - `pub_tenant` - 租户管理表
- **租户表**（存储在各租户独立的 schema 中）：
  - `sys_user` - 租户用户信息表
  - 其他业务表...

### 2. 自动Schema切换
- 用户登录后，系统自动根据Sa-Token登录状态获取租户信息
- MyBatis Plus拦截器自动切换到对应的租户schema
- 公共表操作强制使用public schema
- 业务表操作自动使用租户schema

### 3. 核心组件

#### TenantSchemaInterceptor（多租户拦截器）
```java
@Component
public class TenantSchemaInterceptor implements InnerInterceptor {
    // 自动根据Sa-Token登录状态获取租户schema
    // 公共表强制使用public schema
    // 业务表自动使用租户schema
}
```

#### TenantWebInterceptor（Web拦截器）
```java
@Component
public class TenantWebInterceptor implements HandlerInterceptor {
    // 请求开始时设置租户上下文
    // 请求结束时清理上下文
    // 支持公共API识别
}
```

#### TenantContext（租户上下文）
```java
public class TenantContext {
    // ThreadLocal存储租户信息
    // 提供租户信息的获取和设置方法
}
```

#### TenantUtils（工具类）
```java
public class TenantUtils {
    // 提供便捷的多租户操作方法
    // 支持在指定租户上下文中执行操作
}
```

## 使用方式

### 1. 用户登录
```java
@Service
public class AuthService {
    public LoginResult login(String username, String password, String tenantCode) {
        // 验证用户
        // 执行Sa-Token登录
        StpUtil.login(account.getId());
        
        // 自动设置租户上下文
        setTenantContextForUser(account);
    }
}
```

### 2. 自动Schema切换
用户登录后，所有数据库操作会自动切换到正确的schema：

```java
@Service
public class UserService {
    
    // 这个查询会自动在租户schema中执行
    public List<User> getUserList() {
        return userMapper.selectList(null);
    }
    
    // 这个查询会强制在public schema中执行
    public Account getAccount(Long id) {
        return accountMapper.selectById(id);
    }
}
```

### 3. 手动上下文切换
```java
// 在公共Schema中执行操作
TenantUtils.runInPublicSchema(() -> {
    // 查询公共表
});

// 在指定租户中执行操作
TenantUtils.runInTenant("demo", "tenant_demo", () -> {
    // 查询租户表
});
```

### 4. 获取当前租户信息
```java
// 获取当前租户代码
String tenantCode = TenantUtils.getCurrentTenantCode();

// 获取当前Schema
String schema = TenantUtils.getCurrentTenantSchema();

// 获取当前用户ID
Long userId = TenantUtils.getCurrentUserId();

// 获取上下文摘要
String summary = TenantUtils.getContextSummary();
```

## 配置说明

### 1. MyBatis Plus配置
```java
@Configuration
public class MyBatisPlusConfig {
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 注册多租户拦截器
        interceptor.addInnerInterceptor(tenantSchemaInterceptor);
        return interceptor;
    }
}
```

### 2. Web拦截器配置
```java
@Configuration
public class TenantWebConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tenantWebInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/actuator/**", "/error", "/public/**");
    }
}
```

### 3. Sa-Token配置
```java
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SaInterceptor(handler -> {
            SaRouter.match("/**")
                .notMatch("/api/auth/login")
                .check(r -> StpUtil.checkLogin());
        }));
    }
}
```

## 数据库初始化

运行 `docker/scripts/init-account-tenant-tables.sql` 脚本：
- 创建公共表（pub_account, pub_tenant）
- 创建默认租户和演示租户
- 创建演示租户的schema和示例表
- 插入测试数据

## 测试API

### 1. 用户登录
```bash
POST /api/auth/login
{
    "username": "demo",
    "password": "admin123",
    "tenantCode": "demo"
}
```

### 2. 获取当前租户信息
```bash
GET /api/tenant-demo/current-tenant
Authorization: Bearer {token}
```

### 3. 测试上下文切换
```bash
GET /api/tenant-demo/test-context-switch
Authorization: Bearer {token}
```

### 4. 系统状态（无需登录）
```bash
GET /api/tenant-demo/system-status
```

## 优势

1. **自动化**：登录后自动设置租户上下文，无需手动指定
2. **透明性**：业务代码无需关心多租户逻辑
3. **安全性**：公共表和租户表严格隔离
4. **灵活性**：支持手动上下文切换
5. **高性能**：使用PostgreSQL原生schema切换，性能优异
6. **易维护**：统一的拦截器处理，代码简洁

## 注意事项

1. 确保每个租户都有独立的schema
2. 公共表必须使用`pub_`前缀
3. 租户表统一使用`sys_`前缀
4. 登录后才能正确设置租户上下文
5. 请求结束后会自动清理上下文
