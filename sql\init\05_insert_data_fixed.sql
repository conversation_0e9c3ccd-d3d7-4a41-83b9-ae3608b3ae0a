-- =====================================================
-- 美姿姿健康管理系统 - 初始数据插入脚本（雪花算法版本）
-- 版本: 1.0.0
-- 创建时间: 2025-07-20
-- 作者: 美姿姿团队
-- 说明: 插入系统初始化数据（使用雪花算法ID）
-- 数据库连接: *****************************************************/mzz
-- =====================================================

-- 确保在正确的数据库中执行
\c mzz

-- 设置客户端编码和时区
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- 开始事务
BEGIN;

-- ==============================================
-- 插入平台管理员账户（使用雪花算法ID）
-- ==============================================

-- 插入超级管理员
INSERT INTO public.pub_platform_account (
    id, username, password, email, real_name, permission_level, status, remark
) VALUES (
    1735123456789012345, -- 雪花算法生成的ID
    'admin', 
    '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', -- 密码: admin123
    '<EMAIL>',
    '系统管理员',
    3, -- 超级管理员
    1, -- 启用
    '系统初始化创建的超级管理员账户'
) ON CONFLICT (id) DO NOTHING;

-- 插入平台管理员
INSERT INTO public.pub_platform_account (
    id, username, password, email, real_name, permission_level, status, remark
) VALUES (
    1735123456789012346, -- 雪花算法生成的ID
    'platform_admin', 
    '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', -- 密码: admin123
    '<EMAIL>',
    '平台管理员',
    2, -- 高级管理员
    1, -- 启用
    '平台日常管理账户'
) ON CONFLICT (id) DO NOTHING;

-- ==============================================
-- 插入示例租户（使用雪花算法ID）
-- ==============================================

-- 插入示例租户
INSERT INTO public.pub_tenant (
    id, tenant_code, tenant_name, schema_name, tenant_type, status,
    contact_name, contact_phone, contact_email, address,
    expire_time, max_users, max_storage, features, settings, remark
) VALUES (
    1735123456789012347, -- 雪花算法生成的ID
    'demo',
    '示例租户',
    'tenant_demo',
    2, -- 标准版
    1, -- 启用
    '张三',
    '***********',
    '<EMAIL>',
    '北京市朝阳区示例大厦',
    '2025-12-31 23:59:59'::timestamp,
    50, -- 最大用户数
    5368709120, -- 5GB存储
    '{"modules": ["user_management", "role_management", "member_management"], "features": ["multi_tenant", "api_access"]}',
    '{"theme": "default", "language": "zh_CN", "timezone": "Asia/Shanghai"}',
    '系统示例租户，用于演示和测试'
) ON CONFLICT (id) DO NOTHING;

-- ==============================================
-- 插入示例租户管理员（使用雪花算法ID）
-- ==============================================

-- 插入租户管理员
INSERT INTO public.pub_customer_account (
    id, tenant_code, username, password, email, real_name, account_type, status, remark
) VALUES (
    1735123456789012348, -- 雪花算法生成的ID
    'demo',
    'demo_admin',
    '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', -- 密码: admin123
    '<EMAIL>',
    '示例管理员',
    1, -- 管理员
    1, -- 启用
    '示例租户的管理员账户'
) ON CONFLICT (id) DO NOTHING;

-- ==============================================
-- 插入租户内部数据（tenant_demo schema）
-- ==============================================

-- 切换到示例租户Schema
SET search_path TO tenant_demo, public;

-- 插入部门数据（使用雪花算法ID）
INSERT INTO sys_department (id, parent_id, ancestors, dept_name, order_num, leader, phone, email, status, remark) VALUES
(1735123456789012349, 0, '0', '示例公司', 0, '张三', '***********', '<EMAIL>', 1, '示例公司根部门'),
(1735123456789012350, 1735123456789012349, '0,1735123456789012349', '技术部', 1, '李四', '***********', '<EMAIL>', 1, '技术研发部门'),
(1735123456789012351, 1735123456789012349, '0,1735123456789012349', '市场部', 2, '王五', '13800138002', '<EMAIL>', 1, '市场营销部门'),
(1735123456789012352, 1735123456789012349, '0,1735123456789012349', '人事部', 3, '赵六', '13800138003', '<EMAIL>', 1, '人力资源部门'),
(1735123456789012353, 1735123456789012350, '0,1735123456789012349,1735123456789012350', '前端组', 1, '钱七', '13800138004', '<EMAIL>', 1, '前端开发组'),
(1735123456789012354, 1735123456789012350, '0,1735123456789012349,1735123456789012350', '后端组', 2, '孙八', '13800138005', '<EMAIL>', 1, '后端开发组')
ON CONFLICT (id) DO NOTHING;

-- 插入角色数据（使用雪花算法ID）
INSERT INTO sys_role (id, role_name, role_code, role_sort, data_scope, status, remark) VALUES
(1735123456789012355, '超级管理员', 'super_admin', 1, 1, 1, '超级管理员角色，拥有所有权限'),
(1735123456789012356, '管理员', 'admin', 2, 2, 1, '管理员角色，拥有大部分权限'),
(1735123456789012357, '普通用户', 'user', 3, 5, 1, '普通用户角色，基础权限'),
(1735123456789012358, '部门经理', 'dept_manager', 4, 4, 1, '部门经理角色，管理本部门及下级部门'),
(1735123456789012359, '员工', 'employee', 5, 5, 1, '普通员工角色')
ON CONFLICT (id) DO NOTHING;

-- 插入菜单数据（使用雪花算法ID）
INSERT INTO sys_menu (id, menu_name, parent_id, order_num, path, component, menu_type, visible, status, perms, icon, remark) VALUES
-- 一级菜单
(1735123456789012360, '系统管理', 0, 1, 'system', NULL, 'M', true, 1, NULL, 'system', '系统管理目录'),
(1735123456789012361, '用户管理', 0, 2, 'user', NULL, 'M', true, 1, NULL, 'user', '用户管理目录'),
(1735123456789012362, '会员管理', 0, 3, 'member', NULL, 'M', true, 1, NULL, 'peoples', '会员管理目录'),

-- 系统管理子菜单
(1735123456789012363, '用户管理', 1735123456789012360, 1, 'user', 'system/user/index', 'C', true, 1, 'system:user:list', 'user', '用户管理菜单'),
(1735123456789012364, '角色管理', 1735123456789012360, 2, 'role', 'system/role/index', 'C', true, 1, 'system:role:list', 'peoples', '角色管理菜单'),
(1735123456789012365, '菜单管理', 1735123456789012360, 3, 'menu', 'system/menu/index', 'C', true, 1, 'system:menu:list', 'tree-table', '菜单管理菜单'),
(1735123456789012366, '部门管理', 1735123456789012360, 4, 'dept', 'system/dept/index', 'C', true, 1, 'system:dept:list', 'tree', '部门管理菜单'),

-- 用户管理按钮
(1735123456789012367, '用户查询', 1735123456789012363, 1, '', '', 'F', true, 1, 'system:user:query', '#', ''),
(1735123456789012368, '用户新增', 1735123456789012363, 2, '', '', 'F', true, 1, 'system:user:add', '#', ''),
(1735123456789012369, '用户修改', 1735123456789012363, 3, '', '', 'F', true, 1, 'system:user:edit', '#', ''),
(1735123456789012370, '用户删除', 1735123456789012363, 4, '', '', 'F', true, 1, 'system:user:remove', '#', ''),
(1735123456789012371, '用户导出', 1735123456789012363, 5, '', '', 'F', true, 1, 'system:user:export', '#', ''),
(1735123456789012372, '用户导入', 1735123456789012363, 6, '', '', 'F', true, 1, 'system:user:import', '#', ''),
(1735123456789012373, '重置密码', 1735123456789012363, 7, '', '', 'F', true, 1, 'system:user:resetPwd', '#', ''),

-- 角色管理按钮
(1735123456789012374, '角色查询', 1735123456789012364, 1, '', '', 'F', true, 1, 'system:role:query', '#', ''),
(1735123456789012375, '角色新增', 1735123456789012364, 2, '', '', 'F', true, 1, 'system:role:add', '#', ''),
(1735123456789012376, '角色修改', 1735123456789012364, 3, '', '', 'F', true, 1, 'system:role:edit', '#', ''),
(1735123456789012377, '角色删除', 1735123456789012364, 4, '', '', 'F', true, 1, 'system:role:remove', '#', ''),
(1735123456789012378, '角色导出', 1735123456789012364, 5, '', '', 'F', true, 1, 'system:role:export', '#', ''),

-- 菜单管理按钮
(1735123456789012379, '菜单查询', 1735123456789012365, 1, '', '', 'F', true, 1, 'system:menu:query', '#', ''),
(1735123456789012380, '菜单新增', 1735123456789012365, 2, '', '', 'F', true, 1, 'system:menu:add', '#', ''),
(1735123456789012381, '菜单修改', 1735123456789012365, 3, '', '', 'F', true, 1, 'system:menu:edit', '#', ''),
(1735123456789012382, '菜单删除', 1735123456789012365, 4, '', '', 'F', true, 1, 'system:menu:remove', '#', ''),

-- 部门管理按钮
(1735123456789012383, '部门查询', 1735123456789012366, 1, '', '', 'F', true, 1, 'system:dept:query', '#', ''),
(1735123456789012384, '部门新增', 1735123456789012366, 2, '', '', 'F', true, 1, 'system:dept:add', '#', ''),
(1735123456789012385, '部门修改', 1735123456789012366, 3, '', '', 'F', true, 1, 'system:dept:edit', '#', ''),
(1735123456789012386, '部门删除', 1735123456789012366, 4, '', '', 'F', true, 1, 'system:dept:remove', '#', '')
ON CONFLICT (id) DO NOTHING;

-- 插入用户数据（使用雪花算法ID）
INSERT INTO sys_user (id, user_code, username, password, email, real_name, department_id, status, remark) VALUES
(1735123456789012387, 'U001', 'admin', '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', '<EMAIL>', '系统管理员', 1735123456789012349, 1, '系统管理员'),
(1735123456789012388, 'U002', 'tech_manager', '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', '<EMAIL>', '技术经理', 1735123456789012350, 1, '技术部经理'),
(1735123456789012389, 'U003', 'frontend_dev', '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', '<EMAIL>', '前端开发', 1735123456789012353, 1, '前端开发工程师'),
(1735123456789012390, 'U004', 'backend_dev', '$2a$10$7JB720yubVSOfvVWmduYOeIl16PPlYSGqVDdyLOQdqqpOjJ.1Iq.2', '<EMAIL>', '后端开发', 1735123456789012354, 1, '后端开发工程师')
ON CONFLICT (id) DO NOTHING;

-- 插入用户角色关联
INSERT INTO sys_user_role (user_id, role_id) VALUES
(1735123456789012387, 1735123456789012355), -- admin -> 超级管理员
(1735123456789012388, 1735123456789012358), -- tech_manager -> 部门经理
(1735123456789012389, 1735123456789012359), -- frontend_dev -> 员工
(1735123456789012390, 1735123456789012359)  -- backend_dev -> 员工
ON CONFLICT (user_id, role_id) DO NOTHING;

-- 插入角色菜单关联（超级管理员拥有所有权限）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1735123456789012355, id FROM sys_menu WHERE status = 1
ON CONFLICT (role_id, menu_id) DO NOTHING;

-- 插入示例会员数据（使用雪花算法ID）
INSERT INTO sys_member (id, member_no, nickname, real_name, phone, member_level, status, register_source, remark) VALUES
(1735123456789012391, 'M000001', '张小明', '张明', '***********', 1, 1, 'wechat', '微信注册会员'),
(1735123456789012392, 'M000002', '李小红', '李红', '***********', 2, 1, 'phone', '手机号注册会员'),
(1735123456789012393, 'M000003', '王小强', '王强', '***********', 1, 1, 'wechat', '微信注册会员')
ON CONFLICT (id) DO NOTHING;

-- 插入会员账户关联（使用雪花算法ID）
INSERT INTO public.pub_member_account (id, tenant_code, member_id, login_type, login_identifier, status, remark) VALUES
(1735123456789012394, 'demo', 1735123456789012391, 1, 'wx_openid_001', 1, '张小明的微信登录'),
(1735123456789012395, 'demo', 1735123456789012392, 2, '***********', 1, '李小红的手机号登录'),
(1735123456789012396, 'demo', 1735123456789012393, 1, 'wx_openid_003', 1, '王小强的微信登录')
ON CONFLICT (id) DO NOTHING;

-- 恢复搜索路径
SET search_path TO public, system_admin, audit, reporting;

-- 提交事务
COMMIT;

-- 记录脚本执行
INSERT INTO public.schema_version (version, description, executed_at) 
VALUES ('1.0.0', '插入系统初始化数据（雪花算法版本）', NOW())
ON CONFLICT (version) DO UPDATE SET 
    executed_at = NOW(),
    description = EXCLUDED.description;

RAISE NOTICE '==============================================';
RAISE NOTICE '初始数据插入脚本执行完成（雪花算法版本）';
RAISE NOTICE '已插入数据:';
RAISE NOTICE '- 平台管理员账户: admin, platform_admin';
RAISE NOTICE '- 示例租户: demo';
RAISE NOTICE '- 租户管理员: demo_admin';
RAISE NOTICE '- 部门数据: 6个部门';
RAISE NOTICE '- 角色数据: 5个角色';
RAISE NOTICE '- 菜单数据: 完整的菜单权限体系';
RAISE NOTICE '- 用户数据: 4个示例用户';
RAISE NOTICE '- 会员数据: 3个示例会员';
RAISE NOTICE '==============================================';
RAISE NOTICE '默认登录信息:';
RAISE NOTICE '平台管理员 - 用户名: admin, 密码: admin123';
RAISE NOTICE '租户管理员 - 用户名: demo_admin, 密码: admin123';
RAISE NOTICE '==============================================';
RAISE NOTICE '注意: 所有ID均使用雪花算法生成，与MyBatis-Plus兼容';
RAISE NOTICE '==============================================';

-- 显示数据统计
SELECT 
    'pub_platform_account' as table_name,
    COUNT(*) as record_count
FROM public.pub_platform_account
UNION ALL
SELECT 
    'pub_tenant' as table_name,
    COUNT(*) as record_count
FROM public.pub_tenant
UNION ALL
SELECT 
    'pub_customer_account' as table_name,
    COUNT(*) as record_count
FROM public.pub_customer_account
UNION ALL
SELECT 
    'pub_member_account' as table_name,
    COUNT(*) as record_count
FROM public.pub_member_account
ORDER BY table_name;
