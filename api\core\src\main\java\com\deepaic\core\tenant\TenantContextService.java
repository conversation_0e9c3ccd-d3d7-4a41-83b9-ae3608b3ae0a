package com.deepaic.core.tenant;

import com.deepaic.core.auth.models.UserPrincipal;
import com.deepaic.core.entity.PubUserAccount;
import com.deepaic.core.entity.PubMemberAccount;
import com.deepaic.core.entity.PlatformAccount;
import com.deepaic.core.entity.Tenant;
import com.deepaic.core.exception.BussinessException;
import com.deepaic.core.mapper.PubUserAccountMapper;
import com.deepaic.core.mapper.PubMemberAccountMapper;
import com.deepaic.core.mapper.PlatformAccountMapper;
import com.deepaic.core.mapper.TenantMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 租户上下文服务类
 * 基于Sa-Token Session的租户上下文管理服务
 * 主要负责设置和管理多租户上下文信�? *
 * <AUTHOR> */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantContextService {

    private final PubUserAccountMapper pubUserAccountMapper;
    private final PubMemberAccountMapper pubMemberAccountMapper;
    private final PlatformAccountMapper platformAccountMapper;
    private final TenantMapper tenantMapper;
    private final TenantMonitor tenantMonitor;

    /**
     * 为指定用户设置租户上下文到Sa-Token Session
     * 支持三种用户类型：平台用户、客户用户、会员用户
     *
     * @param accountId 用户账户ID
     * @param userType 用户类型
     * @throws BussinessException
     */
    public void setTenantContextForUser(Long accountId, UserPrincipal.UserType userType) throws BussinessException {
        try {
            switch (userType) {
                case PLATFORM_ACCOUNT:
                    setPlatformUserContext(accountId);
                    break;
                case SASS_CLIENT_ACCOUNT:
                    setCustomerUserContext(accountId);
                    break;
                case SASS_MEMBER_ACCOUNT:
                    setMemberUserContext(accountId);
                    break;
                default:
                    throw new BussinessException("不支持的用户类型: " + userType);
            }
        } catch (BussinessException e) {
            log.error("设置用户租户上下文失败 accountId={}, userType={}", accountId, userType, e);
            // 记录失败的监控信息
            tenantMonitor.recordContextSetup("unknown", "unknown", false);
            throw e;
        } catch (Exception e) {
            log.error("设置用户租户上下文失败 accountId={}, userType={}", accountId, userType, e);
            // 记录失败的监控信息
            tenantMonitor.recordContextSetup("unknown", "unknown", false);
            throw new BussinessException("设置租户上下文失败: " + e.getMessage());
        }
    }

    /**
     * 设置平台用户上下文
     * 平台用户不需要租户上下文，使用public schema
     */
    private void setPlatformUserContext(Long accountId) throws BussinessException {
        try {
            // 查询平台账户信息
            PlatformAccount account = platformAccountMapper.selectById(accountId);
            if (account == null) {
                throw new BussinessException("平台用户" + accountId + "不存在");
            }

            // 平台用户设置为public schema，不设置租户信息
            SaTokenTenantContext.setTenant(
                null,           // tenantCode为null，表示平台用户
                "SaaS平台",      // tenantName
                "public",       // schema固定为public
                accountId,      // userId使用accountId
                account.getUsername()
            );

            log.debug("设置平台用户上下文 accountId={}, username={}", accountId, account.getUsername());

            // 记录监控信息
            tenantMonitor.recordContextSetup("platform", account.getUsername(), true);

        } catch (BussinessException e) {
            log.error("设置平台用户上下文失败 accountId={}", accountId, e);
            throw e;
        } catch (Exception e) {
            log.error("设置平台用户上下文失败 accountId={}", accountId, e);
            throw new BussinessException("设置平台用户上下文失败: " + e.getMessage());
        }
    }

    /**
     * 设置客户用户上下文
     * 客户用户需要设置对应的租户schema
     */
    private void setCustomerUserContext(Long accountId) throws BussinessException {
        try {
            // 查询客户账户信息
            PubUserAccount account = pubUserAccountMapper.selectById(accountId);
            if (account == null) {
                throw new BussinessException("客户用户" + accountId + "不存在");
            }
            if (account.getTenantCode() == null) {
                throw new BussinessException("客户用户" + accountId + "没有租户信息");
            }

            // 查询租户信息
            Tenant tenant = tenantMapper.selectByTenantCode(account.getTenantCode());
            if (tenant == null) {
                throw new BussinessException("找不到租户信息 tenantCode=" + account.getTenantCode());
            }
            if (tenant.getSchemaName() == null) {
                throw new BussinessException("租户" + account.getTenantCode() + "没有schema信息");
            }

            // 设置租户信息到Sa-Token Session
            SaTokenTenantContext.setTenant(
                tenant.getTenantCode(),
                tenant.getTenantName(),
                tenant.getSchemaName(),
                account.getSysUserId(), // 使用关联的sys_user_id
                account.getUsername()
            );

            log.debug("设置客户用户租户上下文 accountId={}, tenant={}, schema={}",
                accountId, tenant.getTenantCode(), tenant.getSchemaName());

            // 记录监控信息
            tenantMonitor.recordContextSetup(tenant.getTenantCode(), account.getUsername(), true);

        } catch (BussinessException e) {
            log.error("设置客户用户租户上下文失败 accountId={}", accountId, e);
            throw e;
        } catch (Exception e) {
            log.error("设置客户用户租户上下文失败 accountId={}", accountId, e);
            throw new BussinessException("设置客户用户租户上下文失败: " + e.getMessage());
        }
    }

    /**
     * 设置会员用户上下文
     * 会员用户需要设置对应的租户schema
     */
    private void setMemberUserContext(Long accountId) throws BussinessException {
        try {
            // 查询会员账户信息
            PubMemberAccount account = pubMemberAccountMapper.selectById(accountId);
            if (account == null) {
                throw new BussinessException("会员用户" + accountId + "不存在");
            }
            if (account.getTenantCode() == null) {
                throw new BussinessException("会员用户" + accountId + "没有租户信息");
            }

            // 查询租户信息
            Tenant tenant = tenantMapper.selectByTenantCode(account.getTenantCode());
            if (tenant == null) {
                throw new BussinessException("找不到租户信息 tenantCode=" + account.getTenantCode());
            }
            if (tenant.getSchemaName() == null) {
                throw new BussinessException("租户" + account.getTenantCode() + "没有schema信息");
            }

            // 设置租户信息到Sa-Token Session
            SaTokenTenantContext.setTenant(
                tenant.getTenantCode(),
                tenant.getTenantName(),
                tenant.getSchemaName(),
                account.getMemberId(), // 使用关联的member_id
                "会员用户_" + account.getId() // 会员可能没有username，使用ID
            );

            log.debug("设置会员用户租户上下文 accountId={}, tenant={}, schema={}",
                accountId, tenant.getTenantCode(), tenant.getSchemaName());

            // 记录监控信息
            tenantMonitor.recordContextSetup(tenant.getTenantCode(), "member_" + account.getId(), true);

        } catch (BussinessException e) {
            log.error("设置会员用户租户上下文失败 accountId={}", accountId, e);
            throw e;
        } catch (Exception e) {
            log.error("设置会员用户租户上下文失败 accountId={}", accountId, e);
            throw new BussinessException("设置会员用户租户上下文失败: " + e.getMessage());
        }
    }

    /**
     * 为指定用户设置租户上下文到Sa-Token Session
     * 兼容旧版本方法，默认为客户用户类型
     *
     * @param accountId 用户ID
     * @throws BussinessException
     * @deprecated 请使用 setTenantContextForUser(Long accountId, UserType userType)
     */
    @Deprecated
    public void setTenantContextForUser(Long accountId) throws BussinessException {
        log.warn("使用了已废弃的方法 setTenantContextForUser(Long)，请使用新方法指定用户类型");
        setTenantContextForUser(accountId, UserPrincipal.UserType.SASS_CLIENT_ACCOUNT);
    }

    /**
     * 根据租户代码获取租户信息
     *
     * @param tenantCode 租户代码
     * @return 租户信息，如果不存在返回null
     */
    public Tenant getTenantByCode(String tenantCode) {
        if (tenantCode == null || tenantCode.trim().isEmpty()) {
            return null;
        }

        try {
            return tenantMapper.selectByTenantCode(tenantCode);
        } catch (Exception e) {
            log.error("查询租户信息失败: tenantCode={}", tenantCode, e);
            return null;
        }
    }

    /**
     * 获取当前用户的账户信�?     *
     * @param userId 用户ID
     * @return 账户信息，如果不存在返回null
     */
    @Deprecated
    public PubUserAccount getAccountByUserId(Long userId) {
        if (userId == null) {
            return null;
        }

        try {
            return pubUserAccountMapper.selectById(userId);
        } catch (Exception e) {
            log.error("查询用户账户信息失败: userId={}", userId, e);
            return null;
        }
    }

    /**
     *
     * @param accountId
     * @throws BussinessException
     */
    public void setupTenantContext(Long accountId) throws BussinessException {
        try {
            // 设置租户上下文到Sa-Token Session
            setTenantContextForUser(accountId);
            log.info("设置租户上下文成功 accountId={}", accountId);
        } catch (Exception e) {
            log.error("设置租户上下文失败 accountId={}", accountId, e);
            throw new BussinessException("设置租户上下文失败");
        }
    }

    /**
     * 检查租户是否存在且有效
     *
     * @param tenantCode 租户代码
     * @return 是否有效
     */
    public boolean isValidTenant(String tenantCode) {
        if (tenantCode == null || tenantCode.trim().isEmpty()) {
            return false;
        }

        Tenant tenant = getTenantByCode(tenantCode);
        return tenant != null && tenant.getStatus() == 1;
    }

    /**
     * 在公共Schema中执行操作（不依赖登录状态）
     * 使用ThreadLocal临时存储schema信息供MyBatis拦截器使�?     */
    public <T> T runInPublicSchema(java.util.function.Supplier<T> operation) {
        // 使用ThreadLocal临时存储schema信息
        String originalSchema = getCurrentSchemaFromThreadLocal();

        try {
            // 设置公共schema到ThreadLocal
            setSchemaToThreadLocal("public");
            return operation.get();
        } finally {
            // 恢复原来的schema
            if (originalSchema != null) {
                setSchemaToThreadLocal(originalSchema);
            } else {
                clearSchemaFromThreadLocal();
            }
        }
    }

    /**
     * 在指定租户Schema中执行操作（不依赖登录状态）
     * 使用ThreadLocal临时存储schema信息供MyBatis拦截器使�?     */
    public <T> T runInTenantSchema(String tenantCode, java.util.function.Supplier<T> operation) {
        String originalSchema = getCurrentSchemaFromThreadLocal();

        try {
            // 查询租户信息
            Tenant tenant = tenantMapper.selectByTenantCode(tenantCode);
            if (tenant == null) {
                throw new RuntimeException("租户不存�? " + tenantCode);
            }

            // 设置租户schema到ThreadLocal
            setSchemaToThreadLocal(tenant.getSchemaName());
            return operation.get();
        } finally {
            // 恢复原来的schema
            if (originalSchema != null) {
                setSchemaToThreadLocal(originalSchema);
            } else {
                clearSchemaFromThreadLocal();
            }
        }
    }

    // ThreadLocal用于临时存储schema信息
    private static final ThreadLocal<String> TEMP_SCHEMA = new ThreadLocal<>();

    /**
     * 从ThreadLocal获取当前schema
     */
    private String getCurrentSchemaFromThreadLocal() {
        return TEMP_SCHEMA.get();
    }

    /**
     * 设置schema到ThreadLocal
     */
    private void setSchemaToThreadLocal(String schema) {
        TEMP_SCHEMA.set(schema);
    }

    /**
     * 清除ThreadLocal中的schema
     */
    private void clearSchemaFromThreadLocal() {
        TEMP_SCHEMA.remove();
    }

    /**
     * 获取当前有效的schema（优先从ThreadLocal，然后从SaToken�?     */
    public static String getCurrentEffectiveSchema() {
        // 优先使用ThreadLocal中的临时schema
        String tempSchema = TEMP_SCHEMA.get();
        if (tempSchema != null) {
            return tempSchema;
        }

        // 然后使用SaToken中的schema
        return SaTokenTenantContext.getTenantSchema();
    }
}
