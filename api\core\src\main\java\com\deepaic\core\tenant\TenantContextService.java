package com.deepaic.core.tenant;

import cn.dev33.satoken.stp.StpUtil;
import com.deepaic.core.entity.UserAccount;
import com.deepaic.core.entity.Tenant;
import com.deepaic.core.exception.BussinessException;
import com.deepaic.core.mapper.UserAccountMapper;
import com.deepaic.core.util.AssertUtil;
import com.deepaic.core.mapper.TenantMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 租户上下文服务类
 * 基于Sa-Token Session的租户上下文管理服务
 * 主要负责设置和管理多租户上下文信�? *
 * <AUTHOR> */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantContextService {

    private final UserAccountMapper UserAccountMapper;
    private final TenantMapper tenantMapper;
    private final TenantMonitor tenantMonitor;

    /**
     * 为指定用户设置租户上下文到Sa-Token Session
     *
     * @param accountId 用户ID
     * @throws BussinessException 
     */
    public void setTenantContextForUser(Long accountId) {

        try {
            // 查询用户账户信息
            UserAccount account = UserAccountMapper.selectById(accountId);
            AssertUtil.isTrue(account != null, "用户{}没有有效的租户信息", accountId);
            AssertUtil.isTrue(account.getTenantCode() != null, "用户{}没有有效的租户信息", accountId);

            // 查询租户信息
            Tenant tenant = tenantMapper.selectByTenantCode(account.getTenantCode());
            AssertUtil.isTrue(tenant != null, "找不到租户信息 tenantCode={}", account.getTenantCode());
            AssertUtil.isTrue(tenant.getSchemaName() != null, "找不到租户信息 tenantCode={}", account.getTenantCode());

            // 设置租户信息到Sa-Token Session
            SaTokenTenantContext.setTenant(
                tenant.getTenantCode(),
                tenant.getTenantName(),
                tenant.getSchemaName(),
                account.getUserId(),
                account.getUsername()
            );

            log.debug("设置用户租户上下文 accountId={}, tenant={}, schema={}",
                accountId, tenant.getTenantCode(), tenant.getSchemaName());

            // 记录监控信息
            tenantMonitor.recordContextSetup(tenant.getTenantCode(), account.getUsername(), true);

        } catch (Exception e) {
            log.error("设置用户租户上下文失败 accountId={}", accountId, e);
            // 记录失败的监控信息
            tenantMonitor.recordContextSetup("unknown", "unknown", false);
        }
    }

    /**
     * 根据租户代码获取租户信息
     *
     * @param tenantCode 租户代码
     * @return 租户信息，如果不存在返回null
     */
    public Tenant getTenantByCode(String tenantCode) {
        if (tenantCode == null || tenantCode.trim().isEmpty()) {
            return null;
        }

        try {
            return tenantMapper.selectByTenantCode(tenantCode);
        } catch (Exception e) {
            log.error("查询租户信息失败: tenantCode={}", tenantCode, e);
            return null;
        }
    }

    /**
     * 获取当前用户的账户信�?     *
     * @param userId 用户ID
     * @return 账户信息，如果不存在返回null
     */
    public UserAccount getAccountByUserId(Long userId) {
        if (userId == null) {
            return null;
        }

        try {
            return UserAccountMapper.selectById(userId);
        } catch (Exception e) {
            log.error("查询用户账户信息失败: userId={}", userId, e);
            return null;
        }
    }

    /**
     *
     * @param accountId
     * @throws BussinessException
     */
    public void setupTenantContext(Long accountId) throws BussinessException {
        try {
            // 设置租户上下文到Sa-Token Session
            setTenantContextForUser(accountId);
            log.info("设置租户上下文成功 accountId={}", accountId);
        } catch (Exception e) {
            log.error("设置租户上下文失败 accountId={}", accountId, e);
            throw new BussinessException("设置租户上下文失败");
        }
    }

    /**
     * 检查租户是否存在且有效
     *
     * @param tenantCode 租户代码
     * @return 是否有效
     */
    public boolean isValidTenant(String tenantCode) {
        if (tenantCode == null || tenantCode.trim().isEmpty()) {
            return false;
        }

        Tenant tenant = getTenantByCode(tenantCode);
        return tenant != null && tenant.getStatus() == 1;
    }

    /**
     * 在公共Schema中执行操作（不依赖登录状态）
     * 使用ThreadLocal临时存储schema信息供MyBatis拦截器使�?     */
    public <T> T runInPublicSchema(java.util.function.Supplier<T> operation) {
        // 使用ThreadLocal临时存储schema信息
        String originalSchema = getCurrentSchemaFromThreadLocal();

        try {
            // 设置公共schema到ThreadLocal
            setSchemaToThreadLocal("public");
            return operation.get();
        } finally {
            // 恢复原来的schema
            if (originalSchema != null) {
                setSchemaToThreadLocal(originalSchema);
            } else {
                clearSchemaFromThreadLocal();
            }
        }
    }

    /**
     * 在指定租户Schema中执行操作（不依赖登录状态）
     * 使用ThreadLocal临时存储schema信息供MyBatis拦截器使�?     */
    public <T> T runInTenantSchema(String tenantCode, java.util.function.Supplier<T> operation) {
        String originalSchema = getCurrentSchemaFromThreadLocal();

        try {
            // 查询租户信息
            Tenant tenant = tenantMapper.selectByTenantCode(tenantCode);
            if (tenant == null) {
                throw new RuntimeException("租户不存�? " + tenantCode);
            }

            // 设置租户schema到ThreadLocal
            setSchemaToThreadLocal(tenant.getSchemaName());
            return operation.get();
        } finally {
            // 恢复原来的schema
            if (originalSchema != null) {
                setSchemaToThreadLocal(originalSchema);
            } else {
                clearSchemaFromThreadLocal();
            }
        }
    }

    // ThreadLocal用于临时存储schema信息
    private static final ThreadLocal<String> TEMP_SCHEMA = new ThreadLocal<>();

    /**
     * 从ThreadLocal获取当前schema
     */
    private String getCurrentSchemaFromThreadLocal() {
        return TEMP_SCHEMA.get();
    }

    /**
     * 设置schema到ThreadLocal
     */
    private void setSchemaToThreadLocal(String schema) {
        TEMP_SCHEMA.set(schema);
    }

    /**
     * 清除ThreadLocal中的schema
     */
    private void clearSchemaFromThreadLocal() {
        TEMP_SCHEMA.remove();
    }

    /**
     * 获取当前有效的schema（优先从ThreadLocal，然后从SaToken�?     */
    public static String getCurrentEffectiveSchema() {
        // 优先使用ThreadLocal中的临时schema
        String tempSchema = TEMP_SCHEMA.get();
        if (tempSchema != null) {
            return tempSchema;
        }

        // 然后使用SaToken中的schema
        return SaTokenTenantContext.getTenantSchema();
    }
}
