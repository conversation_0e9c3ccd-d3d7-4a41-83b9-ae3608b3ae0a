package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_role")
public class Role extends BaseEntity {

    private String roleName;

    private String roleCode;

    private Integer roleSort;

    private Short dataScope;

    private Short status;

    private String remark;

    // 数据权限范围常量
    public static final short DATA_SCOPE_ALL = 1;           // 全部数据
    public static final short DATA_SCOPE_DEPT = 2;          // 部门数据
    public static final short DATA_SCOPE_DEPT_AND_CHILD = 3; // 部门及下级数据
    public static final short DATA_SCOPE_SELF = 4;          // 仅本人数据
    public static final short DATA_SCOPE_CUSTOM = 5;        // 自定义数据

    // 状态常量
    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_ENABLED = 1;  // 启用
}
