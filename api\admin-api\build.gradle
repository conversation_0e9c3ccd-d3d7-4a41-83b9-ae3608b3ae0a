// Admin API 模块特定配置
description = 'Beautiful Posture Admin API Module'

dependencies {
    // 依赖核心模块
    implementation project(':core')
    implementation project(':service')

    // Web相关依赖
    implementation libs.bundles.spring.web

    // 数据访问相关 - 使用PostgreSQL
    implementation libs.bundles.spring.data.mybatis.plus.postgresql

    // 安全相关
    implementation libs.bundles.spring.security.jwt

    // API文档
    implementation libs.springdoc.openapi.starter.webmvc.ui

    // 缓存
    implementation libs.redisson.spring.boot.starter
}

// 设置主类
springBoot {
    mainClass = 'com.deepaic.admin.AdminApiApplication'
}

// 运行时JVM参数配置
bootRun {
    jvmArgs = [
        '-Dfile.encoding=UTF-8',
        '-Duser.timezone=Asia/Shanghai',
        '-Dconsole.encoding=UTF-8',
        '-Djava.awt.headless=true'
    ]

    // 设置系统属性
    systemProperty 'file.encoding', 'UTF-8'
    systemProperty 'user.timezone', 'Asia/Shanghai'
}

// 打包配置
bootJar {
    archiveFileName = 'beautiful-posture-admin-api.jar'

    // 排除不需要的文件
    exclude '**/*.yml.example'
    exclude '**/*.properties.example'
    exclude '**/logback-spring.xml.example'
}
