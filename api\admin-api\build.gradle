// Admin API 模块特定配置
description = 'Beautiful Posture Admin API Module'

dependencies {
    // 依赖核心模块
    implementation project(':core')
    implementation project(':service')

    // 数据库相关
    implementation libs.mybatis.plus.spring.boot3.starter
    runtimeOnly libs.postgresql.connector

    // Sa-Token 权限认证
    implementation libs.sa.token.spring.boot3.starter
    implementation libs.sa.token.redis.jackson

    // Redis
    implementation libs.spring.boot.starter.data.redis

    implementation libs.springdoc.openapi.starter.webmvc.ui

    // JSON处理
    implementation 'com.fasterxml.jackson.core:jackson-databind'

    // 工具类
    implementation libs.hutool.all

    // Lombok
    compileOnly libs.lombok
    annotationProcessor libs.lombok

    // 测试依赖
    testImplementation libs.spring.boot.starter.test
    testRuntimeOnly libs.junit.platform.launcher
}

// 设置主类
springBoot {
    mainClass = 'com.deepaic.admin.AdminApiApplication'
}

// 运行时JVM参数配置
bootRun {
    jvmArgs = [
        '-Dfile.encoding=UTF-8',
        '-Duser.timezone=Asia/Shanghai',
        '-Dconsole.encoding=UTF-8',
        '-Djava.awt.headless=true'
    ]

    // 设置系统属性
    systemProperty 'file.encoding', 'UTF-8'
    systemProperty 'user.timezone', 'Asia/Shanghai'
}

// 打包配置
bootJar {
    archiveFileName = 'beautiful-posture-admin-api.jar'

    // 排除不需要的文件
    exclude '**/*.yml.example'
    exclude '**/*.properties.example'
    exclude '**/logback-spring.xml.example'
}
