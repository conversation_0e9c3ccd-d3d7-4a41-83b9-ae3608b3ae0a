# 美姿姿 - 完整部署配置（单机模式）
# 包含所有服务和基础组件，适用于开发环境或单机部署测试
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: beautiful-posture-postgres
    environment:
      POSTGRES_DB: beautiful_posture
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: beautiful-posture-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin API服务
  admin-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile.admin-api
    image: beautiful-posture/admin-api:${VERSION:-latest}
    container_name: beautiful-posture-admin-api
    ports:
      - "${ADMIN_API_PORT:-8080}:8080"
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES:-docker}
      SERVER_PORT: 8080
      SPRING_DATASOURCE_URL: *************************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_DATABASE: 0
      JAVA_OPTS: ${JAVA_OPTS:--Xms512m -Xmx1024m -XX:+UseG1GC}
      LOGGING_LEVEL_ROOT: ${LOG_LEVEL:-INFO}
      LOGGING_LEVEL_COM_DEEPAIC: ${APP_LOG_LEVEL:-DEBUG}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # App API服务
  app-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile.app-api
    image: beautiful-posture/app-api:${VERSION:-latest}
    container_name: beautiful-posture-app-api
    ports:
      - "${APP_API_PORT:-8081}:8081"
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES:-docker}
      SERVER_PORT: 8081
      SPRING_DATASOURCE_URL: *************************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_DATABASE: 1
      JAVA_OPTS: ${JAVA_OPTS:--Xms1024m -Xmx2048m -XX:+UseG1GC}
      LOGGING_LEVEL_ROOT: ${LOG_LEVEL:-INFO}
      LOGGING_LEVEL_COM_DEEPAIC: ${APP_LOG_LEVEL:-DEBUG}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Platform API服务 (SaaS平台管理)
  platform-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile.platform-api
    image: beautiful-posture/platform-api:${VERSION:-latest}
    container_name: beautiful-posture-platform-api
    ports:
      - "${PLATFORM_API_PORT:-8082}:8080"
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES:-docker}
      SERVER_PORT: 8080
      SPRING_DATASOURCE_URL: **********************************************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_DATABASE: 2
      JAVA_OPTS: ${JAVA_OPTS:--Xms768m -Xmx1536m -XX:+UseG1GC}
      LOGGING_LEVEL_ROOT: ${LOG_LEVEL:-INFO}
      LOGGING_LEVEL_COM_BEAUTIFULPOSTURE: ${APP_LOG_LEVEL:-DEBUG}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/platform-api/platform/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  beautiful-posture-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
