package com.deepaic.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.entity.Menu;
import com.deepaic.core.dto.MenuDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface MenuMapper extends BaseMapper<Menu> {

    /**
     * 查询菜单列表（带分页）
     */
    IPage<MenuDTO> selectMenuPage(Page<MenuDTO> page, @Param("query") MenuDTO.MenuQueryDTO query);

    /**
     * 查询所有菜单列表
     */
    List<MenuDTO> selectMenuList(@Param("query") MenuDTO.MenuQueryDTO query);

    /**
     * 根据用户ID查询菜单权限
     */
    List<String> selectMenuPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询菜单列表
     */
    List<MenuDTO> selectMenuListByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询菜单树
     */
    List<MenuDTO> selectMenuTreeByUserId(@Param("userId") Long userId);

    /**
     * 查询子菜单数量
     */
    Integer countChildrenByParentId(@Param("parentId") Long parentId);

    /**
     * 检查菜单名称是否唯一
     */
    Integer checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId, @Param("id") Long id);

    /**
     * 根据父ID查询子菜单
     */
    List<MenuDTO> selectChildrenByParentId(@Param("parentId") Long parentId);

    /**
     * 查询菜单详情
     */
    MenuDTO selectMenuById(@Param("id") Long id);
}
