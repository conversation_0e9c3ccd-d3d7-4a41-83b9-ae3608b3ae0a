package com.deepaic.core.auth.models;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 统一认证请求模型
 * 支持多种认证方式的请求参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthRequest {

    /**
     * 认证类型
     */
    private AuthenticationType authenticationType;

    /**
     * 用户名（用户名密码登录）
     */
    private String username;

    /**
     * 密码（用户名密码登录）
     */
    private String password;

    /**
     * 微信授权码（微信小程序登录）
     */
    private String wechatCode;

    /**
     * 手机号（手机号登录）
     */
    private String phone;

    /**
     * 短信验证码（手机号登录）
     */
    private String smsCode;

    /**
     * 用户类型
     */
    private UserPrincipal.UserType userType;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 客户端IP
     */
    private String clientIp;



    public AuthRequest fillHttpRequestParams(HttpServletRequest httpRequest) {
        this.clientIp = httpRequest.getRemoteAddr();
        this.deviceInfo = httpRequest.getHeader("User-Agent");
        return this;
    }

    /**
     * 扩展参数
     */
    private Map<String, Object> extraParams;

    public static enum AuthenticationType {
        USERNAME_PASSWORD,
        PHONE_PASSWORD,
        PHONE_CODE,
        WECHAT_CODE
    }
 




}
