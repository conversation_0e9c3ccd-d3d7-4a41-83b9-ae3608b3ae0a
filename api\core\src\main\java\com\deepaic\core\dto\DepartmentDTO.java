package com.deepaic.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门DTO
 *
 * <AUTHOR>
 */
@Data
public class DepartmentDTO {

    /**
     * 部门ID
     */
    private Long id;

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    @Size(min = 1, max = 50, message = "部门名称长度必须在1-50个字符之间")
    private String deptName;

    /**
     * 部门编码
     */
    @NotBlank(message = "部门编码不能为空")
    @Size(min = 2, max = 50, message = "部门编码长度必须在2-50个字符之间")
    private String deptCode;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 父部门名称
     */
    private String parentName;

    /**
     * 部门层级路径
     */
    private String ancestors;

    /**
     * 部门负责人ID
     */
    private Long leaderId;

    /**
     * 部门负责人姓名
     */
    private String leaderName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 部门状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 子部门列表
     */
    private List<DepartmentDTO> children;

    /**
     * 部门查询DTO
     */
    @Data
    public static class DepartmentQueryDTO {
        /**
         * 部门名称
         */
        private String deptName;

        /**
         * 部门编码
         */
        private String deptCode;

        /**
         * 部门状态
         */
        private Integer status;

        /**
         * 父部门ID
         */
        private Long parentId;
    }
}
