package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 会员权益-套餐
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_package")
public class MemberPackage extends BaseEntity {

    private Long memberId;

    private Long rechargeId;

    private Long packageId;

    private String packageCode;

    private String packageName;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Short quantity;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 1=未用完，2=已用完
     */
    private Short status;

    /**
     * 总价
     */
    private BigDecimal totalPrice;
}
