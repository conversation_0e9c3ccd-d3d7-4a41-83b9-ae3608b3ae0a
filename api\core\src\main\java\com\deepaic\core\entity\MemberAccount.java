package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("pub_member_account")
public class MemberAccount extends BaseEntity {

    private String tenantCode;

    private Long memberId;

    private Integer wechatOpenId;

    private LocalDateTime lastLoginTime;

    private Object lastLoginIp;

    private Long tenantId;

    private Short status;

    // 登录类型常量
    public static final int LOGIN_TYPE_WECHAT_OPENID = 1; // 微信OpenID
    public static final int LOGIN_TYPE_PHONE = 2;         // 手机号
    public static final int LOGIN_TYPE_EMAIL = 3;         // 邮箱

    // 状态常量
    public static final int STATUS_DISABLED = 0; // 禁用
    public static final int STATUS_ENABLED = 1;  // 启用
}
