package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@TableName("pub_platform_account")
public class PubPlatformAccount extends BaseEntity {

    private String username;

    private String password;

    private String email;

    private String phone;

    private String realName;

    private String avatar;

    private Integer permissionLevel;

    private Integer status;

    private LocalDateTime lastLoginTime;

    private Object lastLoginIp;

    private Integer loginFailCount;

    private LocalDateTime lockTime;

    private LocalDateTime passwordExpireTime;

    private String remark;
}
