-- =====================================================
-- 美姿姿健康管理系统 - 系统配置数据
-- 版本: 1.0.0
-- 创建时间: 2025-06-27
-- 作者: 美姿姿团队
-- 说明: 系统配置参数数据
-- =====================================================

-- 确保在正确的数据库中执行
\c beautiful_posture

-- 设置搜索路径
SET search_path TO system_admin, public;

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_admin.sys_config (
    id BIGSERIAL PRIMARY KEY,
    config_name VARCHAR(100) NOT NULL UNIQUE,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string', -- string, number, boolean, json
    is_system BOOLEAN DEFAULT FALSE, -- 是否为系统配置
    remark TEXT,
    created_by BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by BIGINT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE system_admin.sys_config IS '系统配置参数表';

-- 插入系统配置数据
INSERT INTO system_admin.sys_config (config_name, config_key, config_value, config_type, is_system, remark) VALUES
-- 系统基础配置
('系统名称', 'system.name', '美姿姿健康管理系统', 'string', true, '系统显示名称'),
('系统版本', 'system.version', '1.0.0', 'string', true, '当前系统版本'),
('系统描述', 'system.description', '基于多租户架构的健康管理SaaS平台', 'string', true, '系统描述信息'),
('系统作者', 'system.author', '美姿姿团队', 'string', true, '系统开发团队'),
('系统官网', 'system.website', 'https://www.deepaic.com', 'string', true, '系统官方网站'),

-- 租户配置
('默认租户类型', 'tenant.default.type', '1', 'number', true, '新租户默认类型：1试用 2标准 3专业 4企业'),
('默认最大用户数', 'tenant.default.max_users', '10', 'number', true, '新租户默认最大用户数'),
('默认存储空间', 'tenant.default.max_storage', '1073741824', 'number', true, '新租户默认存储空间（字节）'),
('租户试用期天数', 'tenant.trial.days', '30', 'number', true, '试用租户的有效期天数'),
('租户代码前缀', 'tenant.code.prefix', 'T', 'string', true, '自动生成租户代码的前缀'),

-- 用户配置
('默认密码', 'user.default.password', 'user123456', 'string', true, '新用户默认密码'),
('密码最小长度', 'user.password.min_length', '6', 'number', true, '用户密码最小长度'),
('密码最大长度', 'user.password.max_length', '20', 'number', true, '用户密码最大长度'),
('密码有效期天数', 'user.password.expire_days', '90', 'number', true, '密码有效期天数，0表示永不过期'),
('登录失败锁定次数', 'user.login.max_fail_count', '5', 'number', true, '连续登录失败锁定账户的次数'),
('账户锁定时间分钟', 'user.login.lock_minutes', '30', 'number', true, '账户锁定时间（分钟）'),

-- 会员配置
('会员编号前缀', 'member.no.prefix', 'M', 'string', true, '会员编号前缀'),
('会员编号长度', 'member.no.length', '8', 'number', true, '会员编号总长度'),
('新会员默认积分', 'member.default.points', '100', 'number', true, '新注册会员默认积分'),
('新会员默认等级', 'member.default.level', '1', 'number', true, '新注册会员默认等级'),

-- 文件上传配置
('文件上传路径', 'file.upload.path', '/uploads', 'string', false, '文件上传根路径'),
('文件最大大小', 'file.upload.max_size', '10485760', 'number', false, '单个文件最大大小（字节）'),
('允许的文件类型', 'file.upload.allowed_types', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx', 'string', false, '允许上传的文件类型'),
('头像最大大小', 'file.avatar.max_size', '2097152', 'number', false, '头像文件最大大小（字节）'),

-- 邮件配置
('SMTP服务器', 'email.smtp.host', 'smtp.qq.com', 'string', false, 'SMTP服务器地址'),
('SMTP端口', 'email.smtp.port', '587', 'number', false, 'SMTP服务器端口'),
('SMTP用户名', 'email.smtp.username', '', 'string', false, 'SMTP登录用户名'),
('SMTP密码', 'email.smtp.password', '', 'string', false, 'SMTP登录密码'),
('发件人邮箱', 'email.from.address', '<EMAIL>', 'string', false, '系统发件人邮箱'),
('发件人名称', 'email.from.name', '美姿姿系统', 'string', false, '系统发件人名称'),

-- 短信配置
('短信服务商', 'sms.provider', 'aliyun', 'string', false, '短信服务提供商'),
('短信AccessKey', 'sms.access_key', '', 'string', false, '短信服务AccessKey'),
('短信AccessSecret', 'sms.access_secret', '', 'string', false, '短信服务AccessSecret'),
('短信签名', 'sms.sign_name', '美姿姿', 'string', false, '短信签名'),
('验证码模板', 'sms.template.verify_code', 'SMS_123456789', 'string', false, '验证码短信模板ID'),

-- 微信配置
('微信AppID', 'wechat.app_id', '', 'string', false, '微信小程序AppID'),
('微信AppSecret', 'wechat.app_secret', '', 'string', false, '微信小程序AppSecret'),
('微信支付商户号', 'wechat.pay.mch_id', '', 'string', false, '微信支付商户号'),
('微信支付密钥', 'wechat.pay.key', '', 'string', false, '微信支付API密钥'),

-- 缓存配置
('Redis主机', 'redis.host', 'localhost', 'string', false, 'Redis服务器地址'),
('Redis端口', 'redis.port', '6379', 'number', false, 'Redis服务器端口'),
('Redis密码', 'redis.password', '', 'string', false, 'Redis连接密码'),
('Redis数据库', 'redis.database', '0', 'number', false, 'Redis数据库编号'),
('缓存过期时间', 'cache.expire.seconds', '3600', 'number', false, '默认缓存过期时间（秒）'),

-- 安全配置
('JWT密钥', 'security.jwt.secret', 'beautiful_posture_jwt_secret_key_2025', 'string', true, 'JWT签名密钥'),
('JWT过期时间', 'security.jwt.expire.hours', '24', 'number', false, 'JWT令牌过期时间（小时）'),
('刷新令牌过期时间', 'security.refresh_token.expire.days', '7', 'number', false, '刷新令牌过期时间（天）'),
('API限流次数', 'security.rate_limit.requests', '1000', 'number', false, 'API每分钟最大请求次数'),
('IP白名单', 'security.ip.whitelist', '', 'string', false, 'IP白名单，逗号分隔'),
('IP黑名单', 'security.ip.blacklist', '', 'string', false, 'IP黑名单，逗号分隔'),

-- 日志配置
('日志级别', 'log.level', 'INFO', 'string', false, '系统日志级别'),
('日志保留天数', 'log.retention.days', '30', 'number', false, '日志文件保留天数'),
('慢查询阈值', 'log.slow_query.threshold', '1000', 'number', false, '慢查询记录阈值（毫秒）'),

-- 监控配置
('健康检查间隔', 'monitor.health_check.interval', '60', 'number', false, '健康检查间隔（秒）'),
('性能监控开关', 'monitor.performance.enabled', 'true', 'boolean', false, '是否启用性能监控'),
('告警邮箱', 'monitor.alert.email', '<EMAIL>', 'string', false, '系统告警邮箱'),

-- 业务配置
('数据备份间隔', 'business.backup.interval.hours', '24', 'number', false, '自动备份间隔（小时）'),
('数据清理间隔', 'business.cleanup.interval.days', '7', 'number', false, '数据清理间隔（天）'),
('统计报表生成时间', 'business.report.generate.hour', '2', 'number', false, '每日统计报表生成时间（小时）')

ON CONFLICT (config_key) DO UPDATE SET
    config_value = EXCLUDED.config_value,
    config_type = EXCLUDED.config_type,
    remark = EXCLUDED.remark,
    updated_at = NOW();

-- 创建配置查询函数
CREATE OR REPLACE FUNCTION system_admin.get_config(p_config_key VARCHAR(100))
RETURNS TEXT
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
    config_value TEXT;
BEGIN
    SELECT sc.config_value INTO config_value
    FROM system_admin.sys_config sc
    WHERE sc.config_key = p_config_key;
    
    RETURN config_value;
END $$;

-- 创建配置更新函数
CREATE OR REPLACE FUNCTION system_admin.set_config(
    p_config_key VARCHAR(100),
    p_config_value TEXT,
    p_updated_by BIGINT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    is_system BOOLEAN;
BEGIN
    -- 检查是否为系统配置
    SELECT sc.is_system INTO is_system
    FROM system_admin.sys_config sc
    WHERE sc.config_key = p_config_key;
    
    IF is_system = true THEN
        RAISE EXCEPTION '系统配置不允许修改: %', p_config_key;
    END IF;
    
    -- 更新配置
    UPDATE system_admin.sys_config SET
        config_value = p_config_value,
        updated_by = p_updated_by,
        updated_at = NOW()
    WHERE config_key = p_config_key;
    
    RETURN FOUND;
END $$;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sys_config_key ON system_admin.sys_config(config_key);
CREATE INDEX IF NOT EXISTS idx_sys_config_type ON system_admin.sys_config(config_type);
CREATE INDEX IF NOT EXISTS idx_sys_config_system ON system_admin.sys_config(is_system);

-- 授予权限
GRANT SELECT ON system_admin.sys_config TO beautiful_posture_user;
GRANT SELECT ON system_admin.sys_config TO beautiful_posture_readonly;
GRANT EXECUTE ON FUNCTION system_admin.get_config(VARCHAR) TO beautiful_posture_user;
GRANT EXECUTE ON FUNCTION system_admin.get_config(VARCHAR) TO beautiful_posture_readonly;
GRANT EXECUTE ON FUNCTION system_admin.set_config(VARCHAR, TEXT, BIGINT) TO beautiful_posture_user;

RAISE NOTICE '==============================================';
RAISE NOTICE '系统配置数据插入完成';
RAISE NOTICE '已插入 % 条配置记录', (SELECT COUNT(*) FROM system_admin.sys_config);
RAISE NOTICE '==============================================';
RAISE NOTICE '使用示例:';
RAISE NOTICE '-- 获取配置';
RAISE NOTICE 'SELECT system_admin.get_config(''system.name'');';
RAISE NOTICE '-- 设置配置';
RAISE NOTICE 'SELECT system_admin.set_config(''file.upload.path'', ''/new/path'', 1);';
RAISE NOTICE '==============================================';

-- 显示配置统计
SELECT 
    config_type,
    COUNT(*) as count,
    COUNT(CASE WHEN is_system THEN 1 END) as system_count
FROM system_admin.sys_config
GROUP BY config_type
ORDER BY config_type;
