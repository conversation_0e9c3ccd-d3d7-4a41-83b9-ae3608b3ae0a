package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_user")
public class User extends BaseEntity {

    private String userCode;

    private String username;

    private String password;

    private String email;

    private String phone;

    private String realName;

    private String avatar;

    private Short gender;

    private LocalDate birthday;

    private String employeeNo;

    private LocalDate entryDate;

    private Long accountId;

    private Short status;

    private LocalDateTime lastLoginTime;

    private String lastLoginIp;

    private String remark;

    // 性别常量
    public static final short GENDER_MALE = 1;     // 男
    public static final short GENDER_FEMALE = 2;   // 女

    // 状态常量
    public static final short STATUS_DISABLED = 0;  // 禁用
    public static final short STATUS_ENABLED = 1;   // 启用
    public static final short STATUS_LOCKED = 2;    // 锁定

    /**
     * 检查用户是否启用
     */
    public boolean isEnabled() {
        return Short.valueOf(STATUS_ENABLED).equals(this.status);
    }

    /**
     * 检查是否为男性
     */
    public boolean isMale() {
        return Short.valueOf(GENDER_MALE).equals(this.gender);
    }

    /**
     * 检查是否为女性
     */
    public boolean isFemale() {
        return Short.valueOf(GENDER_FEMALE).equals(this.gender);
    }

    /**
     * 获取性别描述
     */
    public String getGenderDesc() {
        if (GENDER_MALE == this.gender) {
            return "男";
        } else if (GENDER_FEMALE == this.gender) {
            return "女";
        } else {
            return "未知";
        }
    }

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        return STATUS_ENABLED == this.status ? "启用" : "禁用";
    }
}
