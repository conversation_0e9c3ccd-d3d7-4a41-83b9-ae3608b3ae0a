package com.deepaic.admin.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.dto.RoleDTO;
import com.deepaic.service.IRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/role")
@RequiredArgsConstructor
@Validated
public class RoleController {

    private final IRoleService roleService;

    /**
     * 查询角色列表（分页）
     */
    @GetMapping("/page")
    @SaCheckLogin
    @SaCheckPermission("system:role:list")
    public Map<String, Object> getRolePage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            RoleDTO.RoleQueryDTO query) {
        
        Page<RoleDTO> page = new Page<>(current, size);
        IPage<RoleDTO> result = roleService.getRolePage(page, query);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", result);
        return response;
    }

    /**
     * 查询所有角色列表
     */
    @GetMapping("/list")
    @SaCheckLogin
    @SaCheckPermission("system:role:list")
    public Map<String, Object> getRoleList(RoleDTO.RoleQueryDTO query) {
        List<RoleDTO> roleList = roleService.getRoleList(query);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "查询成功");
        response.put("data", roleList);
        return response;
    }

    /**
     * 获取角色详情
     */
    @GetMapping("/{id}")
    @SaCheckLogin
    @SaCheckPermission("system:role:query")
    public Map<String, Object> getRoleById(@PathVariable Long id) {
        RoleDTO role = roleService.getRoleById(id);
        
        Map<String, Object> response = new HashMap<>();
        if (role != null) {
            response.put("code", 200);
            response.put("message", "查询成功");
            response.put("data", role);
        } else {
            response.put("code", 404);
            response.put("message", "角色不存在");
        }
        return response;
    }

    /**
     * 创建角色
     */
    @PostMapping
    @SaCheckLogin
    @SaCheckPermission("system:role:add")
    public Map<String, Object> createRole(@Valid @RequestBody RoleDTO roleDTO) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long roleId = roleService.createRole(roleDTO);
            response.put("code", 200);
            response.put("message", "创建成功");
            response.put("data", roleId);
        } catch (Exception e) {
            log.error("创建角色失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 更新角色
     */
    @PutMapping("/{id}")
    @SaCheckLogin
    @SaCheckPermission("system:role:edit")
    public Map<String, Object> updateRole(@PathVariable Long id, @Valid @RequestBody RoleDTO roleDTO) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = roleService.updateRole(id, roleDTO);
            if (success) {
                response.put("code", 200);
                response.put("message", "更新成功");
            } else {
                response.put("code", 500);
                response.put("message", "更新失败");
            }
        } catch (Exception e) {
            log.error("更新角色失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @SaCheckLogin
    @SaCheckPermission("system:role:remove")
    public Map<String, Object> deleteRole(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = roleService.deleteRole(id);
            if (success) {
                response.put("code", 200);
                response.put("message", "删除成功");
            } else {
                response.put("code", 500);
                response.put("message", "删除失败");
            }
        } catch (Exception e) {
            log.error("删除角色失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping("/batch")
    @SaCheckLogin
    @SaCheckPermission("system:role:remove")
    public Map<String, Object> deleteRoles(@Valid @RequestBody @NotEmpty List<Long> ids) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = roleService.deleteRoles(ids);
            if (success) {
                response.put("code", 200);
                response.put("message", "批量删除成功");
            } else {
                response.put("code", 500);
                response.put("message", "批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除角色失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 分配角色菜单权限
     */
    @PostMapping("/{id}/menus")
    @SaCheckLogin
    @SaCheckPermission("system:role:edit")
    public Map<String, Object> assignRoleMenus(@PathVariable Long id, @RequestBody List<Long> menuIds) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = roleService.assignRoleMenus(id, menuIds);
            if (success) {
                response.put("code", 200);
                response.put("message", "分配菜单权限成功");
            } else {
                response.put("code", 500);
                response.put("message", "分配菜单权限失败");
            }
        } catch (Exception e) {
            log.error("分配角色菜单权限失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 分配角色数据权限
     */
    @PostMapping("/{id}/data-scope")
    @SaCheckLogin
    @SaCheckPermission("system:role:edit")
    public Map<String, Object> assignRoleDataScope(
            @PathVariable Long id,
            @RequestParam Integer dataScope,
            @RequestBody(required = false) List<Long> deptIds) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = roleService.assignRoleDataScope(id, dataScope, deptIds);
            if (success) {
                response.put("code", 200);
                response.put("message", "分配数据权限成功");
            } else {
                response.put("code", 500);
                response.put("message", "分配数据权限失败");
            }
        } catch (Exception e) {
            log.error("分配角色数据权限失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 分配用户角色
     */
    @PostMapping("/assign-user")
    @SaCheckLogin
    @SaCheckPermission("system:role:edit")
    public Map<String, Object> assignUserRoles(@RequestParam Long userId, @RequestBody List<Long> roleIds) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean success = roleService.assignUserRoles(userId, roleIds);
            if (success) {
                response.put("code", 200);
                response.put("message", "分配用户角色成功");
            } else {
                response.put("code", 500);
                response.put("message", "分配用户角色失败");
            }
        } catch (Exception e) {
            log.error("分配用户角色失败", e);
            response.put("code", 500);
            response.put("message", e.getMessage());
        }
        
        return response;
    }

    /**
     * 检查角色名称是否唯一
     */
    @GetMapping("/check-name")
    @SaCheckLogin
    @SaCheckPermission("system:role:list")
    public Map<String, Object> checkRoleNameUnique(
            @RequestParam String roleName,
            @RequestParam(required = false) Long id) {
        
        boolean unique = roleService.checkRoleNameUnique(roleName, id);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", unique);
        return response;
    }

    /**
     * 检查角色编码是否唯一
     */
    @GetMapping("/check-code")
    @SaCheckLogin
    @SaCheckPermission("system:role:list")
    public Map<String, Object> checkRoleCodeUnique(
            @RequestParam String roleCode,
            @RequestParam(required = false) Long id) {
        
        boolean unique = roleService.checkRoleCodeUnique(roleCode, id);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("message", "检查完成");
        response.put("data", unique);
        return response;
    }
}
