package com.deepaic.core.auth.models;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一认证响应模型
 * 包含认证结果和用户信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthResponse {

    /**
     * 认证是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌类型
     */
    @Builder.Default
    private String tokenType = "Bearer";

    /**
     * 令牌过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 用户主体信息
     */
    private UserPrincipal userPrincipal;

    /**
     * 认证时间
     */
    private LocalDateTime authTime;

    /**
     * 扩展信息
     */
    private Map<String, Object> extraInfo;

    /**
     * 创建成功响应
     */
    public static AuthResponse success(String accessToken, String refreshToken, 
                                     Long expiresIn, UserPrincipal userPrincipal) {
        return AuthResponse.builder()
                .success(true)
                .message("认证成功")
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .expiresIn(expiresIn)
                .userPrincipal(userPrincipal)
                .authTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应（仅访问令牌）
     */
    public static AuthResponse success(String accessToken, Long expiresIn, UserPrincipal userPrincipal) {
        return AuthResponse.builder()
                .success(true)
                .message("认证成功")
                .accessToken(accessToken)
                .expiresIn(expiresIn)
                .userPrincipal(userPrincipal)
                .authTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败响应
     */
    public static AuthResponse failure(String message) {
        return AuthResponse.builder()
                .success(false)
                .message(message)
                .authTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败响应（带错误码）
     */
    public static AuthResponse failure(String message, String errorCode) {
        return AuthResponse.builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .authTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建令牌刷新响应
     */
    public static AuthResponse refreshSuccess(String accessToken, Long expiresIn) {
        return AuthResponse.builder()
                .success(true)
                .message("令牌刷新成功")
                .accessToken(accessToken)
                .expiresIn(expiresIn)
                .authTime(LocalDateTime.now())
                .build();
    }

    // 常用错误码
    public static final String ERROR_INVALID_CREDENTIALS = "INVALID_CREDENTIALS";
    public static final String ERROR_ACCOUNT_DISABLED = "ACCOUNT_DISABLED";
    public static final String ERROR_ACCOUNT_LOCKED = "ACCOUNT_LOCKED";
    public static final String ERROR_PASSWORD_EXPIRED = "PASSWORD_EXPIRED";
    public static final String ERROR_TENANT_NOT_FOUND = "TENANT_NOT_FOUND";
    public static final String ERROR_WECHAT_AUTH_FAILED = "WECHAT_AUTH_FAILED";
    public static final String ERROR_SMS_CODE_INVALID = "SMS_CODE_INVALID";
    public static final String ERROR_TOKEN_EXPIRED = "TOKEN_EXPIRED";
    public static final String ERROR_TOKEN_INVALID = "TOKEN_INVALID";
}
