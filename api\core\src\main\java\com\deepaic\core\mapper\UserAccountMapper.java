package com.deepaic.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.entity.UserAccount;
import com.deepaic.core.dto.AccountDTO;
import com.deepaic.core.dto.AccountQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户账户表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface UserAccountMapper extends BaseMapper<UserAccount> {

    /**
     * 根据用户名查询账户（包含租户信息）
     */
    @Select("""
        SELECT a.*, t.tenant_name
        FROM pub_user_account a
        LEFT JOIN pub_tenant t ON a.tenant_code = t.tenant_code
        WHERE a.username = #{username} AND a.deleted = false
        """)
    AccountDTO selectByUsernameWithTenant(@Param("username") String username);

    /**
     * 根据用户名和租户查询账户
     */
    @Select("""
        SELECT * FROM pub_user_account
        WHERE username = #{username} AND tenant_code = #{tenantCode} AND deleted = false
        """)
    UserAccount selectByUsernameAndTenant(@Param("username") String username, @Param("tenantCode") String tenantCode);

    /**
     * 根据邮箱查询账户
     */
    @Select("""
        SELECT a.*, t.tenant_name
        FROM pub_user_account a
        LEFT JOIN pub_tenant t ON a.tenant_code = t.tenant_code
        WHERE a.email = #{email} AND a.deleted = false
        """)
    AccountDTO selectByEmailWithTenant(@Param("email") String email);

    /**
     * 根据手机号查询账户
     */
    @Select("""
        SELECT a.*, t.tenant_name
        FROM pub_user_account a
        LEFT JOIN pub_tenant t ON a.tenant_code = t.tenant_code
        WHERE a.phone = #{phone} AND a.deleted = false
        """)
    AccountDTO selectByPhoneWithTenant(@Param("phone") String phone);

    /**
     * 分页查询账户列表（包含租户信息）
     */
    IPage<AccountDTO> selectPageWithTenant(Page<AccountDTO> page, @Param("query") AccountQueryDTO query);

    /**
     * 根据租户代码查询账户列表
     */
    @Select("""
        SELECT a.*, t.tenant_name
        FROM pub_user_account a
        LEFT JOIN pub_tenant t ON a.tenant_code = t.tenant_code
        WHERE a.tenant_code = #{tenantCode} AND a.deleted = false
        ORDER BY a.create_time DESC
        """)
    List<AccountDTO> selectByTenantCodeWithTenant(@Param("tenantCode") String tenantCode);

    /**
     * 统计租户下的账户数量
     */
    @Select("""
        SELECT COUNT(*)
        FROM pub_user_account
        WHERE tenant_code = #{tenantCode} AND deleted = false
        """)
    Integer countByTenantCode(@Param("tenantCode") String tenantCode);

    /**
     * 统计各状态的账户数量
     */
    @Select("""
        SELECT status, COUNT(*) as count
        FROM pub_user_account
        WHERE deleted = false
        GROUP BY status
        """)
    List<Object> countByStatus();

    /**
     * 更新最后登录信息
     */
    @Update("""
        UPDATE pub_user_account
        SET last_login_time = #{loginTime},
            last_login_ip = #{loginIp},
            login_fail_count = 0,
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int updateLastLoginInfo(@Param("accountId") Long accountId,
                           @Param("loginTime") LocalDateTime loginTime,
                           @Param("loginIp") String loginIp);

    /**
     * 增加登录失败次数
     */
    @Update("""
        UPDATE pub_user_account
        SET login_fail_count = COALESCE(login_fail_count, 0) + 1,
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int incrementLoginFailCount(@Param("accountId") Long accountId);

    /**
     * 锁定账户
     */
    @Update("""
        UPDATE pub_user_account
        SET status = 2,
            lock_time = #{lockTime},
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int lockAccount(@Param("accountId") Long accountId, @Param("lockTime") LocalDateTime lockTime);

    /**
     * 解锁账户
     */
    @Update("""
        UPDATE pub_user_account
        SET status = 1,
            lock_time = NULL,
            login_fail_count = 0,
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int unlockAccount(@Param("accountId") Long accountId);

    /**
     * 重置密码
     */
    @Update("""
        UPDATE pub_user_account
        SET password = #{newPassword},
            password_expire_time = #{expireTime},
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int resetPassword(@Param("accountId") Long accountId,
                     @Param("newPassword") String newPassword,
                     @Param("expireTime") LocalDateTime expireTime);

    /**
     * 检查用户名是否存在
     */
    @Select("""
        SELECT COUNT(*) > 0
        FROM pub_user_account
        WHERE username = #{username} AND deleted = false
        """)
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    @Select("""
        SELECT COUNT(*) > 0
        FROM pub_user_account
        WHERE email = #{email} AND deleted = false
        """)
    boolean existsByEmail(@Param("email") String email);

    /**
     * 检查手机号是否存在
     */
    @Select("""
        SELECT COUNT(*) > 0
        FROM pub_user_account
        WHERE phone = #{phone} AND deleted = false
        """)
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * 根据ID查询账户（包含租户信息）
     */
    @Select("""
        SELECT a.*, t.tenant_name
        FROM pub_user_account a
        LEFT JOIN pub_tenant t ON a.tenant_code = t.tenant_code
        WHERE a.id = #{id} AND a.deleted = false
        """)
    AccountDTO selectByIdWithTenant(@Param("id") Long id);



    @Select("""
        SELECT * FROM pub_user_account
        WHERE username = #{username} AND deleted = false
        """)
    List<UserAccount> selectByUsername(@Param("username") String username);

    @Select("""
        SELECT * FROM pub_user_account
        WHERE phone = #{phone} AND deleted = false
        """)
    List<UserAccount> selectByPhone(@Param("phone") String phone);

    @Select("""
        SELECT * FROM pub_user_account
        WHERE wechat_id = #{wechatId} AND deleted = false
        """)
    List<UserAccount> selectByWechatId(@Param("wechatId") String wechatId);
    
}
