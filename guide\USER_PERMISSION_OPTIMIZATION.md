# 美姿姿 - 用户权限管理优化方案

## 📋 优化背景

基于当前系统架构分析，发现以下需要优化的问题：

1. **组织架构单一**: 当前只有部门表，无法支持复杂的组织架构
2. **账户关联缺失**: 登录账户与业务用户缺乏明确关联
3. **权限粒度粗糙**: 缺乏细粒度的数据权限控制
4. **扩展性不足**: 无法支持多组织归属等复杂场景

## 🎯 优化目标

### 1. **灵活的组织架构**
- 支持公司、部门、小组、岗位等多层级组织
- 支持虚拟组织和项目组
- 支持用户多组织归属

### 2. **完善的账户关联**
- 登录账户与业务用户明确关联
- 支持一个登录账户对应多个业务角色
- 统一的用户身份管理

### 3. **细粒度权限控制**
- 数据权限精确到组织、字段级别
- 支持自定义权限范围
- 动态权限计算

## 🏗️ 优化方案设计

### **核心架构图**

```
登录层 (Public Schema)
├── pub_platform_account (平台用户)
├── pub_customer_account (租户用户) ──┐
└── pub_member_account (会员用户)    │
                                   │ 关联
业务层 (Tenant Schema)              │
├── sys_user (业务用户) ←──────────────┘
├── sys_organization (组织机构)
├── sys_position (岗位)
├── sys_user_organization (用户组织关联)
├── sys_data_permission (数据权限)
└── sys_user_profile (用户扩展信息)
```

## 📊 **详细表结构设计**

### **1. 组织机构表 (sys_organization)**

**替代原有的部门表，支持更灵活的组织架构**

```sql
CREATE TABLE sys_organization (
    id BIGINT PRIMARY KEY,
    parent_id BIGINT DEFAULT 0,
    ancestors VARCHAR(500) DEFAULT '0',
    org_code VARCHAR(50) NOT NULL UNIQUE,
    org_name VARCHAR(100) NOT NULL,
    org_type INTEGER NOT NULL DEFAULT 1, -- 1:公司 2:部门 3:小组 4:岗位 5:虚拟组织
    org_level INTEGER DEFAULT 1, -- 组织层级
    order_num INTEGER DEFAULT 0,
    leader_id BIGINT, -- 负责人ID
    leader_name VARCHAR(50), -- 负责人姓名（冗余字段）
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    description TEXT,
    status INTEGER NOT NULL DEFAULT 1,
    -- 审计字段...
);
```

**优势**：
- ✅ 支持多种组织类型
- ✅ 支持无限层级嵌套
- ✅ 支持虚拟组织（项目组、临时团队）
- ✅ 便于权限继承和计算

### **2. 岗位表 (sys_position)**

**独立的岗位管理，支持精细化职位管理**

```sql
CREATE TABLE sys_position (
    id BIGINT PRIMARY KEY,
    position_code VARCHAR(50) NOT NULL UNIQUE,
    position_name VARCHAR(100) NOT NULL,
    org_id BIGINT NOT NULL, -- 所属组织
    position_level INTEGER DEFAULT 1, -- 岗位级别
    position_category VARCHAR(50), -- 岗位类别
    job_description TEXT, -- 岗位描述
    requirements TEXT, -- 任职要求
    salary_range VARCHAR(100), -- 薪资范围
    -- 其他字段...
);
```

**优势**：
- ✅ 独立的岗位管理
- ✅ 支持岗位级别和类别
- ✅ 便于人力资源管理
- ✅ 支持岗位权限配置

### **3. 用户组织关联表 (sys_user_organization)**

**支持用户多组织归属**

```sql
CREATE TABLE sys_user_organization (
    id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    org_id BIGINT NOT NULL,
    position_id BIGINT,
    is_primary BOOLEAN DEFAULT FALSE, -- 是否为主组织
    is_leader BOOLEAN DEFAULT FALSE, -- 是否为负责人
    join_date DATE DEFAULT CURRENT_DATE,
    leave_date DATE,
    status INTEGER DEFAULT 1, -- 0:离职 1:在职
    -- 审计字段...
);
```

**优势**：
- ✅ 支持用户多组织归属
- ✅ 支持主组织概念
- ✅ 支持组织负责人
- ✅ 支持入职离职管理

### **4. 数据权限表 (sys_data_permission)**

**细粒度的数据权限控制**

```sql
CREATE TABLE sys_data_permission (
    id BIGINT PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL,
    permission_code VARCHAR(100) NOT NULL UNIQUE,
    permission_type INTEGER NOT NULL, -- 1:组织权限 2:数据权限 3:字段权限
    resource_type VARCHAR(50), -- 资源类型（表名、模块名等）
    scope_type INTEGER NOT NULL, -- 1:全部 2:本组织 3:本组织及下级 4:自定义 5:仅本人
    scope_value TEXT, -- 权限范围值（JSON格式）
    -- 其他字段...
);
```

**优势**：
- ✅ 支持多种权限类型
- ✅ 支持自定义权限范围
- ✅ 支持JSON配置复杂权限
- ✅ 便于动态权限计算

### **5. 账户关联优化**

**建立登录账户与业务用户的明确关联**

```sql
-- 租户用户表增加关联字段
ALTER TABLE pub_customer_account 
ADD COLUMN sys_user_id BIGINT,
ADD COLUMN is_primary BOOLEAN DEFAULT TRUE;

-- 用户表增加关联字段
ALTER TABLE sys_user 
ADD COLUMN account_id BIGINT;
```

**关联关系**：
- `pub_customer_account.sys_user_id` → `sys_user.id`
- `sys_user.account_id` → `pub_customer_account.id`
- 支持一对一或一对多关联

## 🔄 **数据迁移策略**

### **1. 部门表迁移**

```sql
-- 将现有部门数据迁移到组织机构表
INSERT INTO sys_organization (
    id, parent_id, ancestors, org_code, org_name, 
    org_type, org_level, leader_name, phone, email, status
)
SELECT 
    id, parent_id, ancestors, 
    'DEPT_' || id as org_code, -- 生成组织代码
    dept_name, 
    2 as org_type, -- 设置为部门类型
    CASE 
        WHEN parent_id = 0 THEN 1
        ELSE (LENGTH(ancestors) - LENGTH(REPLACE(ancestors, ',', '')) + 1)
    END as org_level,
    leader, phone, email, status
FROM sys_department;
```

### **2. 用户关联迁移**

```sql
-- 更新用户表的组织关联
UPDATE sys_user SET org_id = department_id WHERE department_id IS NOT NULL;

-- 创建用户组织关联记录
INSERT INTO sys_user_organization (user_id, org_id, is_primary, is_leader)
SELECT id, org_id, TRUE, FALSE FROM sys_user WHERE org_id IS NOT NULL;
```

## 🎯 **权限计算逻辑**

### **1. 组织权限继承**

```java
/**
 * 计算用户的组织权限
 */
public Set<Long> calculateUserOrgPermissions(Long userId) {
    Set<Long> orgIds = new HashSet<>();
    
    // 1. 获取用户直接关联的组织
    List<UserOrganization> userOrgs = getUserOrganizations(userId);
    
    for (UserOrganization userOrg : userOrgs) {
        orgIds.add(userOrg.getOrgId());
        
        // 2. 根据数据权限范围计算权限组织
        DataScope dataScope = getUserDataScope(userId, userOrg.getOrgId());
        switch (dataScope.getScopeType()) {
            case ALL:
                orgIds.addAll(getAllOrgIds());
                break;
            case CURRENT_AND_SUB:
                orgIds.addAll(getCurrentAndSubOrgIds(userOrg.getOrgId()));
                break;
            case CURRENT_ONLY:
                orgIds.add(userOrg.getOrgId());
                break;
            case CUSTOM:
                orgIds.addAll(parseCustomScope(dataScope.getScopeValue()));
                break;
        }
    }
    
    return orgIds;
}
```

### **2. 数据权限过滤**

```java
/**
 * 数据权限SQL拼接
 */
@Component
public class DataPermissionInterceptor implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 获取当前用户
        Long userId = SecurityUtils.getCurrentUserId();
        
        // 计算用户权限组织
        Set<Long> orgIds = calculateUserOrgPermissions(userId);
        
        // 动态拼接权限SQL
        String permissionSql = buildPermissionSql(orgIds);
        
        // 修改原始SQL
        return modifyOriginalSql(invocation, permissionSql);
    }
}
```

## 📈 **性能优化**

### **1. 索引优化**

```sql
-- 组织架构查询优化
CREATE INDEX idx_sys_org_parent_type ON sys_organization(parent_id, org_type);
CREATE INDEX idx_sys_org_ancestors ON sys_organization USING gin(string_to_array(ancestors, ','));

-- 用户组织关联优化
CREATE INDEX idx_sys_user_org_user_primary ON sys_user_organization(user_id, is_primary);
CREATE INDEX idx_sys_user_org_org_status ON sys_user_organization(org_id, status);

-- 权限计算优化
CREATE INDEX idx_sys_data_perm_resource_scope ON sys_data_permission(resource_type, scope_type);
```

### **2. 缓存策略**

```java
@Service
public class UserPermissionService {
    
    @Cacheable(value = "user_permissions", key = "#userId")
    public UserPermissionInfo getUserPermissions(Long userId) {
        // 计算用户完整权限信息
        return buildUserPermissionInfo(userId);
    }
    
    @CacheEvict(value = "user_permissions", key = "#userId")
    public void refreshUserPermissions(Long userId) {
        // 刷新用户权限缓存
    }
}
```

## 🔧 **实施建议**

### **1. 分阶段实施**

**阶段一**: 基础结构优化
- 创建新的组织架构表
- 建立账户关联关系
- 数据迁移和验证

**阶段二**: 权限体系完善
- 实现数据权限控制
- 完善权限计算逻辑
- 性能优化和测试

**阶段三**: 功能扩展
- 用户扩展信息管理
- 高级权限功能
- 监控和审计

### **2. 兼容性考虑**

- 保留原有部门表，逐步迁移
- 提供数据迁移脚本和回滚方案
- 确保API接口向后兼容

### **3. 测试验证**

- 权限计算正确性测试
- 性能压力测试
- 数据一致性验证
- 用户体验测试

## 📝 **总结**

通过这次优化，美姿姿健康管理系统将获得：

1. **✅ 灵活的组织架构** - 支持复杂的企业组织结构
2. **✅ 完善的账户关联** - 统一的用户身份管理
3. **✅ 细粒度权限控制** - 精确的数据权限管理
4. **✅ 良好的扩展性** - 支持未来业务发展需求
5. **✅ 高性能设计** - 优化的查询和缓存策略

这个优化方案不仅解决了当前的架构问题，还为系统的长期发展奠定了坚实的基础。
