package com.deepaic.core.service;

/**
 * 邮件服务接口
 * 提供邮件发送和验证功能
 *
 * <AUTHOR>
 */
public interface IEmailService {

    /**
     * 发送注册邮箱验证码
     *
     * @param email 邮箱地址
     * @param clientIp 客户端IP
     * @return 是否发送成功
     */
    boolean sendRegistrationEmailCode(String email, String clientIp);

    /**
     * 验证邮箱验证码
     *
     * @param email 邮箱地址
     * @param code 验证码
     * @return 是否验证通过
     */
    boolean validateEmailCode(String email, String code);

    /**
     * 发送注册成功邮件
     *
     * @param email 邮箱地址
     * @param tenantName 租户名称
     * @param username 用户名
     * @param loginUrl 登录地址
     * @return 是否发送成功
     */
    boolean sendRegistrationSuccessEmail(String email, String tenantName, String username, String loginUrl);

    /**
     * 发送账户激活邮件
     *
     * @param email 邮箱地址
     * @param tenantName 租户名称
     * @param activationUrl 激活链接
     * @return 是否发送成功
     */
    boolean sendActivationEmail(String email, String tenantName, String activationUrl);

    /**
     * 发送密码重置邮件
     *
     * @param email 邮箱地址
     * @param resetUrl 重置链接
     * @return 是否发送成功
     */
    boolean sendPasswordResetEmail(String email, String resetUrl);

    /**
     * 发送审核通过邮件
     *
     * @param email 邮箱地址
     * @param tenantName 租户名称
     * @param loginUrl 登录地址
     * @return 是否发送成功
     */
    boolean sendAuditApprovedEmail(String email, String tenantName, String loginUrl);

    /**
     * 发送审核拒绝邮件
     *
     * @param email 邮箱地址
     * @param tenantName 租户名称
     * @param reason 拒绝原因
     * @return 是否发送成功
     */
    boolean sendAuditRejectedEmail(String email, String tenantName, String reason);
}
