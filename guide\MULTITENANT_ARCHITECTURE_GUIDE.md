# 美姿姿 - 多租户架构实现指南

## 📋 概述

美姿姿项目实现了基于PostgreSQL Schema的多租户架构，通过MyBatis-Plus拦截器动态切换数据库schema，实现租户数据隔离。

## 🏗️ 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    多租户架构组件                              │
├─────────────────────────────────────────────────────────────┤
│  Web层                                                      │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ TenantResolver  │  │TenantWebInterceptor│                │
│  │ (租户识别)       │  │ (Web拦截器)      │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  服务层                                                      │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ TenantService   │  │TenantContextInit│                  │
│  │ (租户服务)       │  │ (上下文初始化)   │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  数据层                                                      │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ TenantContext   │  │TenantSchemaInter│                  │
│  │ (租户上下文)     │  │ (MyBatis拦截器) │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│  数据库层                                                    │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ public schema   │  │ tenant_xxx      │                  │
│  │ (用户/租户表)    │  │ (业务数据表)     │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

### 数据库Schema设计

```
PostgreSQL Database: beautiful_posture
├── public schema (系统表)
│   ├── sys_user (用户表)
│   ├── sys_tenant (租户表)
│   ├── sys_role (角色表)
│   ├── sys_permission (权限表)
│   └── sys_config (系统配置表)
├── tenant_company1 (租户1业务数据)
│   ├── business_table1
│   ├── business_table2
│   └── ...
└── tenant_company2 (租户2业务数据)
    ├── business_table1
    ├── business_table2
    └── ...
```

## 🔧 核心实现

### 1. 租户上下文管理 (TenantContext)

```java
// 租户信息结构
public static class TenantInfo {
    private String tenantId;      // 租户ID
    private String tenantCode;    // 租户代码
    private String schemaName;    // Schema名称
    private String tenantName;    // 租户名称
    private Long userId;          // 当前用户ID
    private String username;      // 当前用户名
}

// 使用示例
TenantContext.setTenant(tenantInfo);
String schema = TenantContext.getTenantSchema();
TenantContext.clear();
```

### 2. 租户识别 (TenantResolver)

支持多种租户识别方式：

```java
// HTTP Header方式
X-Tenant-Id: company1
X-Tenant-Code: company1
X-Tenant-Schema: tenant_company1

// 请求参数方式
?tenantId=company1&tenantCode=company1

// 子域名方式
company1.beautiful-posture.com

// URL路径方式
/api/company1/users
```

### 3. MyBatis-Plus拦截器 (TenantSchemaInterceptor)

```java
@Override
public void beforeQuery(Executor executor, MappedStatement ms, ...) {
    // 获取当前租户schema
    String schema = TenantContext.getTenantSchema();
    
    // 特殊处理：用户/租户查询强制使用public schema
    if (isUserOrTenantQuery(ms)) {
        schema = "public";
    }
    
    // 设置PostgreSQL search_path
    String sql = "SET search_path TO " + schema + ", public";
    statement.execute(sql);
}
```

### 4. 用户登录流程

```java
// 1. 用户登录验证（在public schema中）
User user = userService.login(username, password);

// 2. 获取用户租户信息
TenantInfo tenantInfo = tenantService.getTenantByUserId(user.getId());

// 3. 设置租户上下文
TenantContext.setTenant(tenantInfo);

// 4. 后续业务操作自动使用租户schema
businessService.doSomething(); // 在tenant_xxx schema中执行
```

## 🚀 使用指南

### 1. 租户初始化

```java
@Autowired
private TenantContextInitializer tenantInitializer;

// 根据用户ID初始化
boolean success = tenantInitializer.initializeTenantContext(userId);

// 根据用户名初始化
boolean success = tenantInitializer.initializeTenantContext(username);

// 根据租户代码初始化
boolean success = tenantInitializer.initializeTenantContextByCode("company1");
```

### 2. 租户上下文操作

```java
// 检查是否有租户上下文
if (SaTokenTenantContext.hasValidTenantContext()) {
    String tenantCode = SaTokenTenantContext.getTenantCode();
    String schema = SaTokenTenantContext.getTenantSchema();
}

// 在指定租户上下文中执行操作
TenantUtils.runInTenant("company1", "tenant_company1", () -> {
    // 这里的数据库操作会在tenant_company1 schema中执行
    businessService.doSomething();
});

// 在公共Schema中执行操作
TenantUtils.runInPublicSchema(() -> {
    // 查询公共表
});
```

### 3. Web请求处理

```java
// 自动处理（通过TenantWebInterceptor）
// 1. 请求进入时自动解析租户信息
// 2. 设置租户上下文
// 3. 请求结束时清理上下文

// 手动处理
@RestController
public class BusinessController {
    
    @GetMapping("/data")
    public List<Data> getData() {
        // 此时已经自动设置了租户上下文
        // 数据库查询会在对应的租户schema中执行
        return dataService.findAll();
    }
}
```

## 🔒 安全考虑

### 1. Schema名称验证

```java
// 防止SQL注入
private boolean isValidSchemaName(String schema) {
    return schema != null && 
           schema.matches("^[a-zA-Z_][a-zA-Z0-9_]*$") &&
           schema.length() <= 63;
}
```

### 2. 权限控制

- 用户只能访问自己所属租户的数据
- 系统表（public schema）的访问需要特殊权限
- 跨租户操作需要超级管理员权限

### 3. 数据隔离

- 物理隔离：不同租户使用不同schema
- 逻辑隔离：通过拦截器确保查询正确的schema
- 备份隔离：可以按租户进行数据备份

## 📊 性能优化

### 1. 连接池优化

```properties
# 为不同租户配置独立连接池
spring.datasource.hikari.maximum-pool-size=30
spring.datasource.hikari.minimum-idle=10
```

### 2. 缓存策略

```java
// 租户信息缓存
private final Map<String, TenantInfo> tenantCache = new ConcurrentHashMap<>();

// 用户租户映射缓存
private final Map<Long, TenantInfo> userTenantCache = new ConcurrentHashMap<>();
```

### 3. 索引优化

```sql
-- 为租户相关查询添加索引
CREATE INDEX idx_sys_user_tenant_id ON sys_user(tenant_id);
CREATE INDEX idx_sys_tenant_code ON sys_tenant(tenant_code);
```

## 🛠️ 运维管理

### 1. 租户创建

```java
// 1. 在sys_tenant表中创建租户记录
Tenant tenant = new Tenant();
tenant.setTenantCode("company1");
tenant.setSchemaName("tenant_company1");
tenantService.save(tenant);

// 2. 创建对应的数据库schema
CREATE SCHEMA tenant_company1;

// 3. 在新schema中创建业务表
-- 复制表结构或执行建表脚本
```

### 2. 租户迁移

```java
// 数据迁移示例
TenantContext.runWithTenantSchema("tenant_old", () -> {
    List<Data> data = dataService.findAll();
    
    TenantContext.runWithTenantSchema("tenant_new", () -> {
        dataService.batchSave(data);
    });
});
```

### 3. 监控告警

```java
// 租户上下文监控
@Component
public class TenantMonitor {
    
    @EventListener
    public void onTenantContextSet(TenantContextEvent event) {
        // 记录租户切换日志
        log.info("租户上下文切换: {} -> {}", 
            event.getOldTenant(), event.getNewTenant());
    }
}
```

## 🚨 故障排除

### 1. 常见问题

**问题**: Schema不存在
```
ERROR: schema "tenant_xxx" does not exist
```
**解决**: 检查租户配置，确保schema已创建

**问题**: 权限不足
```
ERROR: permission denied for schema tenant_xxx
```
**解决**: 检查数据库用户权限

**问题**: 租户上下文丢失
```
使用了错误的schema进行查询
```
**解决**: 检查拦截器配置和租户解析逻辑

### 2. 调试技巧

```java
// 启用调试日志
logging.level.com.deepaic.core.tenant=DEBUG

// 检查当前租户上下文
String summary = tenantInitializer.getCurrentTenantSummary();
log.info("当前租户上下文: {}", summary);

// 验证租户上下文
TenantContextValidationResult result = tenantInitializer.validateTenantContext();
log.info("租户上下文验证: {}", result);
```

## 📈 扩展建议

1. **动态Schema创建**: 支持运行时创建新租户schema
2. **读写分离**: 为不同租户配置读写分离
3. **分库分表**: 大租户可以使用独立数据库
4. **缓存分离**: 不同租户使用不同的缓存空间
5. **监控分离**: 按租户进行性能监控和告警

---

**美姿姿团队** - 让健康管理更美好 💪
