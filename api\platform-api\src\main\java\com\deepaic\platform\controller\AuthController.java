package com.deepaic.platform.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.deepaic.core.auth.AuthenticationManager;
import com.deepaic.core.auth.models.AuthRequest;
import com.deepaic.core.auth.models.AuthResponse;
import com.deepaic.core.auth.models.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * SaaS平台认证控制器
 * 处理平台用户的登录、登出等认证操作
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/platform/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final AuthenticationManager authenticationManager;

    /**
     * 平台用户登录
     * 使用用户名和密码进行登录认证
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody @Valid PlatformLoginRequest request, 
                                   HttpServletRequest httpRequest) {
        log.info("平台用户登录请求: username={}", request.getUsername());
        
        // 创建认证请求
        AuthRequest authRequest = AuthRequest.platformLogin(request.getUsername(), request.getPassword());
        
        // 执行认证
        AuthResponse authResponse = authenticationManager.authenticate(authRequest, httpRequest);
        
        Map<String, Object> response = new HashMap<>();
        if (authResponse.isSuccess()) {
            response.put("code", 200);
            response.put("message", "登录成功");
            
            Map<String, Object> data = new HashMap<>();
            data.put("accessToken", authResponse.getAccessToken());
            data.put("expiresIn", authResponse.getExpiresIn());
            data.put("userInfo", authResponse.getUserPrincipal());
            response.put("data", data);
            
            log.info("平台用户登录成功: username={}", request.getUsername());
        } else {
            response.put("code", authResponse.getErrorCode());
            response.put("message", authResponse.getMessage());
            response.put("data", null);
            
            log.warn("平台用户登录失败: username={}, error={}", request.getUsername(), authResponse.getMessage());
        }
        
        return response;
    }

    /**
     * 平台用户登出
     */
    @PostMapping("/logout")
    @SaCheckLogin
    public Map<String, Object> logout() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取当前用户信息
            String loginId = StpUtil.getLoginId().toString();
            
            // 执行登出
            StpUtil.logout();
            
            response.put("code", 200);
            response.put("message", "登出成功");
            response.put("data", null);
            
            log.info("平台用户登出成功: loginId={}", loginId);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "登出失败: " + e.getMessage());
            response.put("data", null);
            
            log.error("平台用户登出失败", e);
        }
        
        return response;
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/current-user")
    @SaCheckLogin
    public Map<String, Object> getCurrentUser() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从Session中获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) StpUtil.getSession().get("userPrincipal");
            
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", userPrincipal);
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "获取用户信息失败: " + e.getMessage());
            response.put("data", null);
            
            log.error("获取当前用户信息失败", e);
        }
        
        return response;
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/check-login")
    public Map<String, Object> checkLogin() {
        Map<String, Object> response = new HashMap<>();
        
        boolean isLogin = StpUtil.isLogin();
        response.put("code", 200);
        response.put("message", "检查完成");
        
        Map<String, Object> data = new HashMap<>();
        data.put("isLogin", isLogin);
        if (isLogin) {
            data.put("loginId", StpUtil.getLoginId());
            data.put("tokenValue", StpUtil.getTokenValue());
        }
        response.put("data", data);
        
        return response;
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh-token")
    public Map<String, Object> refreshToken(@RequestBody RefreshTokenRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 平台用户类型为PLATFORM_ACCOUNT
            AuthResponse authResponse = authenticationManager.refreshToken(
                request.getRefreshToken(),
                UserPrincipal.UserType.PLATFORM_ACCOUNT
            );

            if (authResponse.isSuccess()) {
                response.put("code", 200);
                response.put("message", "刷新成功");

                Map<String, Object> data = new HashMap<>();
                data.put("accessToken", authResponse.getAccessToken());
                data.put("expiresIn", authResponse.getExpiresIn());
                response.put("data", data);
            } else {
                response.put("code", authResponse.getErrorCode());
                response.put("message", authResponse.getMessage());
                response.put("data", null);
            }
        } catch (Exception e) {
            response.put("code", 500);
            response.put("message", "刷新令牌失败: " + e.getMessage());
            response.put("data", null);

            log.error("刷新令牌失败", e);
        }

        return response;
    }

    /**
     * 平台登录请求DTO
     */
    public static class PlatformLoginRequest {
        @NotBlank(message = "用户名不能为空")
        private String username;
        
        @NotBlank(message = "密码不能为空")
        private String password;

        // Getters and Setters
        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    /**
     * 刷新令牌请求DTO
     */
    public static class RefreshTokenRequest {
        @NotBlank(message = "刷新令牌不能为空")
        private String refreshToken;

        // Getters and Setters
        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }
    }
}
