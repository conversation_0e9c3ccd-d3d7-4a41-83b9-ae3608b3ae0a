<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepaic.core.mapper.AccountMapper">

    <!-- 账户结果映射 -->
    <resultMap id="AccountDTOResultMap" type="com.deepaic.core.dto.AccountDTO">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="real_name" property="realName"/>
        <result column="avatar" property="avatar"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="status" property="status"/>
        <result column="account_type" property="accountType"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="last_login_ip" property="lastLoginIp"/>
        <result column="login_fail_count" property="loginFailCount"/>
        <result column="lock_time" property="lockTime"/>
        <result column="password_expire_time" property="passwordExpireTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
    </resultMap>

    <!-- 分页查询账户列表（包含租户信息） -->
    <select id="selectPageWithTenant" resultMap="AccountDTOResultMap">
        SELECT 
            a.id, a.username, a.email, a.phone, a.real_name, a.avatar,
            a.tenant_code, t.tenant_name, a.status, a.account_type,
            a.last_login_time, a.last_login_ip, a.login_fail_count,
            a.lock_time, a.password_expire_time, a.remark,
            a.create_time, a.update_time, a.version
        FROM pub_account a
        LEFT JOIN pub_tenant t ON a.tenant_code = t.tenant_code
        WHERE a.deleted = 0
        <if test="query != null">
            <if test="query.username != null and query.username != ''">
                AND a.username LIKE CONCAT('%', #{query.username}, '%')
            </if>
            <if test="query.email != null and query.email != ''">
                AND a.email LIKE CONCAT('%', #{query.email}, '%')
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND a.phone LIKE CONCAT('%', #{query.phone}, '%')
            </if>
            <if test="query.realName != null and query.realName != ''">
                AND a.real_name LIKE CONCAT('%', #{query.realName}, '%')
            </if>
            <if test="query.tenantCode != null and query.tenantCode != ''">
                AND a.tenant_code = #{query.tenantCode}
            </if>
            <if test="query.status != null">
                AND a.status = #{query.status}
            </if>
            <if test="query.accountType != null">
                AND a.account_type = #{query.accountType}
            </if>
            <if test="query.createTimeStart != null">
                AND a.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND a.create_time &lt;= #{query.createTimeEnd}
            </if>
            <if test="query.lastLoginTimeStart != null">
                AND a.last_login_time >= #{query.lastLoginTimeStart}
            </if>
            <if test="query.lastLoginTimeEnd != null">
                AND a.last_login_time &lt;= #{query.lastLoginTimeEnd}
            </if>
        </if>
        ORDER BY a.create_time DESC
    </select>

</mapper>
