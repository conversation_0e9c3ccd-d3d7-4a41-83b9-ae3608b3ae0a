package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_consume_project")
public class ConsumeProject extends BaseEntity {

    private Long consumeId;

    /**
     * 1=会员权益-项目，2=会员权益-套餐，3=系统项目
     */
    private Short sourceType;

    /**
     * 套餐id，当source_type=2的时候使用
     */
    private Long memberPackageId;

    /**
     * 套餐 project id，当source_type=2的时候使用
     */
    private Long memberPackageProjectId;

    /**
     * project id
     */
    private Long projectId;

    private Short quantity;

    private BigDecimal unitPrice;

    private BigDecimal discount;

    private BigDecimal totlePrice;
}
