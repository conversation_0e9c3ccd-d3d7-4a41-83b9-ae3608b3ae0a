@echo off
setlocal enabledelayedexpansion

REM 美姿姿 项目构建脚本 (Windows版本)
REM 用于构建所有模块并生成可部署的JAR文件

echo ==========================================
echo 美姿姿 项目构建开始
echo ==========================================

REM 检查Java环境
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Java环境，请安装Java 21或更高版本
    exit /b 1
)

echo Java环境检查通过

REM 清理之前的构建
echo 清理之前的构建...
cd ..\api
gradlew.bat clean
if errorlevel 1 (
    echo 清理失败
    exit /b 1
)

REM 构建所有模块
echo 开始构建所有模块...
gradlew.bat build -x test
if errorlevel 1 (
    echo 构建失败
    exit /b 1
)
cd ..\docker

REM 检查构建结果
echo 检查构建结果...

REM 检查API模块的JAR文件
set "API_MODULES=platform-api admin-api app-api"
for %%m in (%API_MODULES%) do (
    if "%%m"=="platform-api" (
        set "jar_file=..\api\%%m\build\libs\platform-api.jar"
    ) else (
        set "jar_file=..\api\%%m\build\libs\beautiful-posture-%%m.jar"
    )
    if exist "!jar_file!" (
        echo ✓ %%m 构建成功: !jar_file!
        for %%F in ("!jar_file!") do echo   文件大小: %%~zF 字节
    ) else (
        echo ✗ %%m 构建失败: 未找到 !jar_file!
        exit /b 1
    )
)

REM 检查库模块的JAR文件
set "LIB_MODULES=core service"
for %%m in (%LIB_MODULES%) do (
    set "jar_file=..\api\%%m\build\libs\beautiful-posture-%%m.jar"
    if exist "!jar_file!" (
        echo ✓ %%m 构建成功: !jar_file!
        for %%F in ("!jar_file!") do echo   文件大小: %%~zF 字节
    ) else (
        echo ✗ %%m 构建失败: 未找到 !jar_file!
        exit /b 1
    )
)

REM 创建部署目录
set "DEPLOY_DIR=deploy"
echo 创建部署目录: %DEPLOY_DIR%
if not exist "%DEPLOY_DIR%" mkdir "%DEPLOY_DIR%"

REM 复制JAR文件到部署目录
echo 复制JAR文件到部署目录...
for %%m in (%API_MODULES%) do (
    if "%%m"=="platform-api" (
        copy "..\api\%%m\build\libs\platform-api.jar" "%DEPLOY_DIR%\" >nul
    ) else (
        copy "..\api\%%m\build\libs\beautiful-posture-%%m.jar" "%DEPLOY_DIR%\" >nul
    )
)

REM 复制配置文件模板
echo 复制配置文件模板...
if exist "application.yml.template" (
    copy "application.yml.template" "%DEPLOY_DIR%\application.yml.example" >nul
)

REM 创建启动脚本
echo 创建启动脚本...

echo @echo off > "%DEPLOY_DIR%\start-platform-api.bat"
echo java -Xms512m -Xmx1024m -XX:+UseG1GC -jar platform-api.jar --server.port=8080 >> "%DEPLOY_DIR%\start-platform-api.bat"

echo @echo off > "%DEPLOY_DIR%\start-admin-api.bat"
echo java -Xms512m -Xmx1024m -XX:+UseG1GC -jar beautiful-posture-admin-api.jar --server.port=8081 >> "%DEPLOY_DIR%\start-admin-api.bat"

echo @echo off > "%DEPLOY_DIR%\start-app-api.bat"
echo java -Xms512m -Xmx1024m -XX:+UseG1GC -jar beautiful-posture-app-api.jar --server.port=8082 >> "%DEPLOY_DIR%\start-app-api.bat"

REM 生成版本信息
echo 生成版本信息...
echo 美姿姿 项目构建信息 > "%DEPLOY_DIR%\version.txt"
echo 构建时间: %date% %time% >> "%DEPLOY_DIR%\version.txt"
echo 构建环境: Windows >> "%DEPLOY_DIR%\version.txt"
java -version 2>> "%DEPLOY_DIR%\version.txt"

echo ==========================================
echo 构建完成！
echo ==========================================
echo 部署文件位置: %DEPLOY_DIR%\
echo 包含以下文件:
dir /b "%DEPLOY_DIR%"
echo.
echo 启动命令:
echo   SaaS平台API:  cd %DEPLOY_DIR% ^&^& start-platform-api.bat
echo   客户管理API:  cd %DEPLOY_DIR% ^&^& start-admin-api.bat
echo   小程序API:    cd %DEPLOY_DIR% ^&^& start-app-api.bat
echo.
echo Docker构建命令:
echo   docker-compose build
echo   docker-compose up -d

pause
