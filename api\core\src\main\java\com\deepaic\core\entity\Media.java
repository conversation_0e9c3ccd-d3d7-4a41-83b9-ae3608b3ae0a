package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_media")
public class Media extends BaseEntity {

    /**
     * 1=image,2=video,3=other
     */
    private Short type;

    /**
     * 来源id，比如project_id
     */
    private Long sourceId;

    private String path;

    // 媒体类型常量
    public static final int TYPE_IMAGE = 1;  // 图片
    public static final int TYPE_VIDEO = 2;  // 视频
    public static final int TYPE_AUDIO = 3;  // 音频
    public static final int TYPE_DOCUMENT = 4; // 文档
}
