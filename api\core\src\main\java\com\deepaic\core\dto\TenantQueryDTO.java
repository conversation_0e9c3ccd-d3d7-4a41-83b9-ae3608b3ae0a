package com.deepaic.core.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 租户查询DTO
 * 
 * <AUTHOR>
 */
@Data
public class TenantQueryDTO {

    private String tenantCode;

    private String tenantName;

    private Integer tenantType;

    private Integer status;

    private String contactName;

    private String contactPhone;

    private String contactEmail;

    private LocalDateTime createTimeStart;

    private LocalDateTime createTimeEnd;

    private LocalDateTime serviceStartTimeStart;

    private LocalDateTime serviceStartTimeEnd;

    private LocalDateTime serviceEndTimeStart;

    private LocalDateTime serviceEndTimeEnd;

    private Boolean expired;

    private Boolean inTrial;
}
