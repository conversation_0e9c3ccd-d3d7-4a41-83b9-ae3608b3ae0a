package com.deepaic.core.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜单DTO
 *
 * <AUTHOR>
 */
@Data
public class MenuDTO {

    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(min = 1, max = 50, message = "菜单名称长度必须在1-50个字符之间")
    private String menuName;

    /**
     * 父菜单ID
     */
    private Long parentId;

    /**
     * 父菜单名称
     */
    private String parentName;

    /**
     * 菜单类型 - 1:目录 2:菜单 3:按钮
     */
    @NotNull(message = "菜单类型不能为空")
    private Integer menuType;

    /**
     * 菜单路径
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 权限标识
     */
    private String permission;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 是否可见 - 0:隐藏 1:显示
     */
    private Boolean visible;

    /**
     * 菜单状态 - 0:禁用 1:启用
     */
    private Short status;

    /**
     * 是否外链 - 0:否 1:是
     */
    private Boolean isFrame;

    /**
     * 是否缓存 - 0:不缓存 1:缓存
     */
    private Boolean isCache;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 子菜单列表
     */
    private List<MenuDTO> children;

    /**
     * 菜单查询DTO
     */
    @Data
    public static class MenuQueryDTO {
        /**
         * 菜单名称
         */
        private String menuName;

        /**
         * 菜单状态
         */
        private Short status;

        /**
         * 菜单类型
         */
        private Short menuType;

        /**
         * 父菜单ID
         */
        private Long parentId;
    }
}
