package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * <p>
 * 调拨表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_stock_inventory")
public class StockInventory extends BaseEntity {

    /**
     * 盘点单号
     */
    private String code;

    /**
     * 盘点日期
     */
    private LocalDate inventoryDate;

    /**
     * 签字
     */
    private String signatureFile;

    private String remark;

    /**
     * 门店id或者仓库id
     */
    private Long storeWarehouseId;

    /**
     * 门店或者仓库
     */
    private Short storeWarehouseType;

    private Short status;
}
