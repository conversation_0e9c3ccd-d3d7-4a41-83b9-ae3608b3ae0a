package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_paid_card")
public class PaidCard extends BaseEntity {

    private String code;

    private String name;

    /**
     * 最小储值金额
     */
    private BigDecimal minRechargeAmount;

    /**
     * 赠送金额
     */
    private BigDecimal giftAmount;

    /**
     * 项目折扣
     */
    private BigDecimal projectDiscount;

    /**
     * 产品折扣
     */
    private BigDecimal productDiscount;

    /**
     * 是否限制库存
     */
    private Boolean isStockLimited;

    /**
     * 限制数量
     */
    private Integer stockQuantity;

    /**
     * 是否自动停售
     */
    private Boolean isAutoStopSelling;

    /**
     * 自动停售日期
     */
    private LocalDate stopSellingDate;

    /**
     * 1启用，0禁用
     */
    private Short status;

    // 状态常量
    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_NORMAL = 1;   // 正常
}
