package com.deepaic.service.impl;

import com.deepaic.core.entity.PubPlatformAccount;
import com.deepaic.core.mapper.PubPlatformAccountMapper;

import com.deepaic.service.IPubPlatformAccountService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Service
public class PubPlatformAccountServiceImpl extends BaseServiceImpl<PubPlatformAccountMapper, PubPlatformAccount> implements IPubPlatformAccountService {

}
