package com.deepaic.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepaic.core.entity.Tenant;
import com.deepaic.core.dto.TenantDTO;
import com.deepaic.core.dto.TenantQueryDTO;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 租户Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantMapper extends BaseMapper<Tenant> {

    /**
     * 根据租户代码查询租户
     */
    @Select("SELECT * FROM pub_tenant WHERE tenant_code = #{tenantCode} AND deleted = false")
    Tenant selectByTenantCode(@Param("tenantCode") String tenantCode);

    /**
     * 根据Schema名称查询租户
     */
    @Select("SELECT * FROM pub_tenant WHERE schema_name = #{schemaName} AND deleted = false")
    Tenant selectBySchemaName(@Param("schemaName") String schemaName);

    /**
     * 检查租户代码是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM pub_tenant WHERE tenant_code = #{tenantCode} AND deleted = false")
    boolean existsByTenantCode(@Param("tenantCode") String tenantCode);

    /**
     * 检查Schema名称是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM pub_tenant WHERE schema_name = #{schemaName} AND deleted = false")
    boolean existsBySchemaName(@Param("schemaName") String schemaName);

    /**
     * 分页查询租户列表（包含统计信息）
     */
    IPage<TenantDTO> selectPageWithStats(Page<TenantDTO> page, @Param("query") TenantQueryDTO query);

    /**
     * 查询所有启用的租户
     */
    @Select("SELECT * FROM pub_tenant WHERE status = 1 AND deleted = false ORDER BY create_time DESC")
    List<Tenant> selectEnabledTenants();

    /**
     * 启用租户
     */
    @Update("UPDATE pub_tenant SET status = 1, update_time = NOW() WHERE id = #{tenantId}")
    int enableTenant(@Param("tenantId") Long tenantId);

    /**
     * 禁用租户
     */
    @Update("UPDATE pub_tenant SET status = 0, update_time = NOW() WHERE id = #{tenantId}")
    int disableTenant(@Param("tenantId") Long tenantId);

    /**
     * 暂停租户
     */
    @Update("UPDATE pub_tenant SET status = 2, update_time = NOW() WHERE id = #{tenantId}")
    int suspendTenant(@Param("tenantId") Long tenantId);

    /**
     * 设置租户过期
     */
    @Update("UPDATE pub_tenant SET status = 3, update_time = NOW() WHERE id = #{tenantId}")
    int expireTenant(@Param("tenantId") Long tenantId);

    /**
     * 检查租户名称是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM pub_tenant WHERE tenant_name = #{tenantName} AND deleted = false")
    boolean existsByTenantName(@Param("tenantName") String tenantName);

    /**
     * 统计各状态的租户数量
     */
    @Select("SELECT status, COUNT(*) as count FROM pub_tenant WHERE deleted = false GROUP BY status")
    List<Object> countByStatus();

    /**
     * 统计各类型的租户数量
     */
    @Select("SELECT tenant_type, COUNT(*) as count FROM pub_tenant WHERE deleted = false GROUP BY tenant_type")
    List<Object> countByType();

    /**
     * 查询即将到期的租户
     */
    @Select("SELECT * FROM pub_tenant WHERE service_end_time IS NOT NULL AND service_end_time <= #{expireTime} AND status = 1 AND deleted = false ORDER BY service_end_time ASC")
    List<Tenant> selectExpiringSoon(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 查询试用期即将结束的租户
     */
    @Select("SELECT * FROM pub_tenant WHERE tenant_type = 4 AND trial_end_time IS NOT NULL AND trial_end_time <= #{expireTime} AND status = 1 AND deleted = false ORDER BY trial_end_time ASC")
    List<Tenant> selectTrialExpiringSoon(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 更新租户用户数量
     */
    @Update("UPDATE pub_tenant SET current_users = #{userCount}, update_time = NOW() WHERE tenant_code = #{tenantCode}")
    int updateUserCount(@Param("tenantCode") String tenantCode, @Param("userCount") Integer userCount);

    /**
     * 更新租户存储使用量
     */
    @Update("UPDATE pub_tenant SET storage_used = #{storageUsed}, update_time = NOW() WHERE tenant_code = #{tenantCode}")
    int updateStorageUsed(@Param("tenantCode") String tenantCode, @Param("storageUsed") Long storageUsed);

    /**
     * 获取租户统计信息
     */
    @Select("SELECT COUNT(*) as total_count, SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as enabled_count FROM pub_tenant WHERE deleted = false")
    Object selectTenantStats();

    /**
     * 查询超出用户限制的租户
     */
    @Select("SELECT * FROM pub_tenant WHERE max_users IS NOT NULL AND current_users IS NOT NULL AND current_users >= max_users AND status = 1 AND deleted = false")
    List<Tenant> selectUserLimitExceeded();

    /**
     * 查询超出存储限制的租户
     */
    @Select("SELECT * FROM pub_tenant WHERE storage_limit IS NOT NULL AND storage_used IS NOT NULL AND storage_used >= storage_limit AND status = 1 AND deleted = false")
    List<Tenant> selectStorageLimitExceeded();

    /**
     * 根据自定义域名查询租户
     */
    @Select("SELECT * FROM pub_tenant WHERE custom_domain = #{domain} AND deleted = false")
    Tenant selectByCustomDomain(@Param("domain") String domain);

    /**
     * 检查企业名称是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM pub_tenant WHERE company_name = #{companyName} AND deleted = false")
    boolean existsByCompanyName(@Param("companyName") String companyName);
}