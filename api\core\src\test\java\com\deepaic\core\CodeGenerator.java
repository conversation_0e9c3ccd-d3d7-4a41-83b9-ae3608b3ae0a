package com.deepaic.core;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.model.ClassAnnotationAttributes;

import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;

public class CodeGenerator {

    public static void main(String[] args) {
        String jdbcUrl = "****************************************";
        String baseDir = Paths.get(System.getProperty("user.dir")).toString();
        
        FastAutoGenerator.create(jdbcUrl, "postgres", "deepaic!2025")
                .globalConfig(builder -> builder
                        .author("Deepaic")
                        .outputDir(baseDir + "/api/core/src/main/test")
                        .commentDate("yyyy-MM-dd")
                        .disableOpenDir()
                )
                .packageConfig(builder -> builder
                        .parent("com.deepaic.core")
                        .entity("entity")
                        .mapper("mapper")
                        .xml("mapper.xml")
                        .service("service")
                        .serviceImpl("service.impl")
                        // 设置 mapper.xml 文件的单独路径
                )
                .strategyConfig(builder -> builder
                        .addTablePrefix("sys_","pub_")
                        .entityBuilder()
                        .disableSerialVersionUID()
                        .addSuperEntityColumns(Arrays.asList("id", "created_by", "updated_by", "created_at", "updated_at", "deleted"))
                        .superClass("com.deepaic.core.entity.BaseEntity")
                        .enableLombok(new ClassAnnotationAttributes("@Data","lombok.Data"))
                        .mapperBuilder().disable()
                        .serviceBuilder().disable()
                        .superServiceClass("com.deepaic.core.service.IBaseService")
                        .superServiceImplClass("com.deepaic.core.service.impl.BaseServiceImpl")
                        .controllerBuilder().disable()
                )
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}
