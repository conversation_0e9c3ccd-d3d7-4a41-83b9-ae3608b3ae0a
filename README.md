# 美姿姿健康管理系统 (Beautiful Posture Health Management System)

[![Java](https://img.shields.io/badge/Java-21-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2.0-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://www.postgresql.org/)
[![Redis](https://img.shields.io/badge/Redis-7.0+-red.svg)](https://redis.io/)
[![Sa-Token](https://img.shields.io/badge/Sa--Token-1.37.0-blue.svg)](https://sa-token.cc/)

基于Spring Boot 3.2.0和Java 21的多租户SaaS健康管理平台，支持PostgreSQL多租户schema隔离，集成Sa-Token权限认证框架。

## 🏗️ 项目结构

```
beautiful-posture/
├── api/                           # 🚀 API模块目录
│   ├── platform-api/             # SaaS平台管理API模块
│   ├── admin-api/                 # 客户系统管理后台API模块
│   ├── app-api/                   # 小程序后端API模块
│   ├── core/                      # 核心公共模块
│   └── service/                   # 业务服务模块
├── docker/                        # 🐳 Docker和构建相关文件
│   ├── Dockerfile.platform-api   # SaaS平台API Docker镜像
│   ├── Dockerfile.admin-api       # 客户管理API Docker镜像
│   ├── Dockerfile.app-api         # 小程序API Docker镜像
│   ├── docker-compose.yml         # Docker编排文件
│   ├── build.sh                   # Linux/Mac构建脚本
│   ├── build.bat                  # Windows构建脚本
│   └── application.yml.template   # 应用配置模板
├── guide/                         # 📚 文档指南
│   ├── GRADLE_BUILD_GUIDE.md      # Gradle构建指南
│   ├── GRADLE_DEPENDENCY_MANAGEMENT.md # 依赖管理指南
│   ├── LOMBOK_GUIDE.md            # Lombok使用指南
│   ├── MYBATIS_PLUS_GUIDE.md      # MyBatis-Plus使用指南
│   ├── POSTGRESQL_MULTITENANT_GUIDE.md # PostgreSQL多租户指南
│   └── JAVA21_FEATURES.md         # Java 21特性说明
├── gradle/                        # Gradle配置
│   └── libs.versions.toml         # 统一版本管理
├── build.gradle                   # 根项目构建配置
├── settings.gradle                # 项目设置
└── README.md                      # 项目说明文档
```

## 🚀 快速开始

### 环境要求

- **Java**: 21 LTS或更高版本
- **Gradle**: 8.13或更高版本
- **PostgreSQL**: 15或更高版本
- **Redis**: 7或更高版本 (可选)

### 构建项目

#### 使用构建脚本 (推荐)

```bash
# Linux/Mac
cd docker
chmod +x build.sh
./build.sh

# Windows
cd docker
build.bat
```

#### 手动构建

```bash
# 构建所有模块
./gradlew buildAll

# 清理所有模块
./gradlew cleanAll

# 查看项目结构
./gradlew projects
```

### 运行项目

#### Docker方式 (推荐)

```bash
cd docker
docker-compose up -d
```

服务访问地址：
- SaaS平台API: http://localhost:8080
- 客户管理API: http://localhost:8081
- 小程序API: http://localhost:8082
- PostgreSQL: localhost:5432
- Redis: localhost:6379

#### 传统方式

```bash
# 启动SaaS平台API
java -jar api/platform-api/build/libs/platform-api.jar

# 启动客户管理API
java -jar api/admin-api/build/libs/beautiful-posture-admin-api.jar --server.port=8081

# 启动小程序API
java -jar api/app-api/build/libs/beautiful-posture-app-api.jar --server.port=8082
```

## 🏛️ 技术架构

### 核心技术栈

- **框架**: Spring Boot 3.2.0
- **语言**: Java 21 LTS
- **构建**: Gradle 8.13
- **数据库**: PostgreSQL 15+
- **ORM**: MyBatis-Plus 3.5.4
- **权限认证**: Sa-Token 1.37.0
- **缓存**: Redis 7+ / Redisson
- **连接池**: HikariCP
- **文档**: SpringDoc OpenAPI 3

### 特色功能

- ✅ **多租户支持**: 基于PostgreSQL Schema的租户隔离
- ✅ **权限认证**: Sa-Token RBAC权限控制体系
- ✅ **统一依赖管理**: Gradle Version Catalogs
- ✅ **容器化部署**: Docker + Docker Compose
- ✅ **代码生成**: MyBatis-Plus Generator
- ✅ **自动填充**: 创建时间、更新时间等字段
- ✅ **分页查询**: 内置分页插件
- ✅ **乐观锁**: 防止并发更新冲突
- ✅ **逻辑删除**: 软删除功能
- ✅ **API文档**: 自动生成Swagger文档
- ✅ **完整接口**: 用户、角色、权限、菜单、租户管理

## 📖 文档指南

详细的使用指南请查看 `guide/` 目录：

- [模块架构指南](guide/MODULE_ARCHITECTURE_GUIDE.md) - 项目模块结构和职责说明
- [Gradle构建指南](guide/GRADLE_BUILD_GUIDE.md) - 完整的构建和部署指南
- [依赖管理指南](guide/GRADLE_DEPENDENCY_MANAGEMENT.md) - 统一版本管理说明
- [Docker部署指南](guide/DOCKER_DEPLOYMENT_GUIDE.md) - 容器化部署完整指南
- [MyBatis-Plus指南](guide/MYBATIS_PLUS_GUIDE.md) - 数据访问层使用说明
- [PostgreSQL多租户指南](guide/POSTGRESQL_MULTITENANT_GUIDE.md) - 多租户架构基础
- [多租户架构指南](guide/MULTITENANT_ARCHITECTURE_GUIDE.md) - 多租户架构详细指南
- [Lombok指南](guide/LOMBOK_GUIDE.md) - 代码简化工具使用
- [Java 21特性说明](guide/JAVA21_FEATURES.md) - 新版本特性介绍

## 🔧 开发指南

### 多租户使用

```java
// 设置租户上下文
TenantContext.setTenantSchema("tenant1");

// 在指定租户下执行操作
TenantContext.runWithTenantSchema("tenant2", () -> {
    List<User> users = userService.list();
    // 处理tenant2的数据
});

// 清理租户上下文
TenantContext.clear();
```

### 实体类定义

```java
@Data
@TableName("users")
public class User extends BaseEntity {
    
    @TableField("username")
    private String username;
    
    @TableField("email")
    private String email;
    
    // 其他字段...
}
```

### 服务层使用

```java
@Service
public class UserService extends ServiceImpl<UserMapper, User> {
    
    public IPage<User> getUserPage(int current, int size) {
        Page<User> page = new Page<>(current, size);
        return this.page(page);
    }
}
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: [Beautiful Posture](https://gitee.com/deep-shield/beautiful-posture)
- 问题反馈: [Issues](https://gitee.com/deep-shield/beautiful-posture/issues)
- 团队: 美姿姿团队

---

**美姿姿团队** - 让健康管理更美好 💪
