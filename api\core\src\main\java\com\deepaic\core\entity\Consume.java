package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_consume")
public class Consume extends BaseEntity {

    /**
     * 编号
     */
    private String code;

    /**
     * 1=会员,2=散客
     */
    private Short consumeUserType;

    private Long memberId;

    private Long storeId;

    private BigDecimal totalBalance;

    private BigDecimal actualBalance;

    /**
     * 操作人
     */
    private Long operatorUserId;

    /**
     * 1=收银系统，2=小程序
     */
    private Short platform;

    /**
     * 总价折扣
     */
    private BigDecimal discount;
}
