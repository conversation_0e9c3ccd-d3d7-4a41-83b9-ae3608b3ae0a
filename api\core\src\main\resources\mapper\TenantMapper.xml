<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepaic.core.mapper.TenantMapper">

    <!-- 租户结果映射 -->
    <resultMap id="TenantDTOResultMap" type="com.deepaic.core.dto.TenantDTO">
        <id column="id" property="id"/>
        <result column="tenant_code" property="tenantCode"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="tenant_short_name" property="tenantShortName"/>
        <result column="schema_name" property="schemaName"/>
        <result column="tenant_type" property="tenantType"/>
        <result column="status" property="status"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="address" property="address"/>
        <result column="website" property="website"/>
        <result column="logo_url" property="logoUrl"/>
        <result column="max_users" property="maxUsers"/>
        <result column="current_users" property="currentUsers"/>
        <result column="storage_limit" property="storageLimit"/>
        <result column="storage_used" property="storageUsed"/>
        <result column="service_start_time" property="serviceStartTime"/>
        <result column="service_end_time" property="serviceEndTime"/>
        <result column="trial_end_time" property="trialEndTime"/>
        <result column="custom_domain" property="customDomain"/>
        <result column="config_json" property="configJson"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="version" property="version"/>
        <result column="storage_usage_percentage" property="storageUsagePercentage"/>
        <result column="user_usage_percentage" property="userUsagePercentage"/>
        <result column="expired" property="expired"/>
        <result column="in_trial" property="inTrial"/>
    </resultMap>

    <!-- 分页查询租户列表（包含统计信息） -->
    <select id="selectPageWithStats" resultMap="TenantDTOResultMap">
        SELECT 
            t.id, t.tenant_code, t.tenant_name, t.tenant_short_name, t.schema_name,
            t.tenant_type, t.status, t.contact_name, t.contact_phone, t.contact_email,
            t.address, t.website, t.logo_url, t.max_users, t.current_users,
            t.storage_limit, t.storage_used, t.service_start_time, t.service_end_time,
            t.trial_end_time, t.custom_domain, t.config_json, t.remark,
            t.create_time, t.update_time, t.version,
            -- 计算字段
            CASE 
                WHEN t.storage_limit IS NULL OR t.storage_limit = 0 THEN 0.0
                ELSE ROUND((t.storage_used::DECIMAL / t.storage_limit::DECIMAL) * 100, 2)
            END as storage_usage_percentage,
            CASE 
                WHEN t.max_users IS NULL OR t.max_users = 0 THEN 0.0
                ELSE ROUND((t.current_users::DECIMAL / t.max_users::DECIMAL) * 100, 2)
            END as user_usage_percentage,
            CASE 
                WHEN t.status = 3 OR (t.service_end_time IS NOT NULL AND t.service_end_time &lt; NOW()) THEN true
                ELSE false
            END as expired,
            CASE 
                WHEN t.tenant_type = 4 AND t.trial_end_time IS NOT NULL AND t.trial_end_time > NOW() THEN true
                ELSE false
            END as in_trial
        FROM pub_tenant t
        WHERE t.deleted = 0
        <if test="query != null">
            <if test="query.tenantCode != null and query.tenantCode != ''">
                AND t.tenant_code LIKE CONCAT('%', #{query.tenantCode}, '%')
            </if>
            <if test="query.tenantName != null and query.tenantName != ''">
                AND t.tenant_name LIKE CONCAT('%', #{query.tenantName}, '%')
            </if>
            <if test="query.tenantType != null">
                AND t.tenant_type = #{query.tenantType}
            </if>
            <if test="query.status != null">
                AND t.status = #{query.status}
            </if>
            <if test="query.contactName != null and query.contactName != ''">
                AND t.contact_name LIKE CONCAT('%', #{query.contactName}, '%')
            </if>
            <if test="query.contactPhone != null and query.contactPhone != ''">
                AND t.contact_phone LIKE CONCAT('%', #{query.contactPhone}, '%')
            </if>
            <if test="query.contactEmail != null and query.contactEmail != ''">
                AND t.contact_email LIKE CONCAT('%', #{query.contactEmail}, '%')
            </if>
            <if test="query.createTimeStart != null">
                AND t.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND t.create_time &lt;= #{query.createTimeEnd}
            </if>
            <if test="query.serviceStartTimeStart != null">
                AND t.service_start_time >= #{query.serviceStartTimeStart}
            </if>
            <if test="query.serviceStartTimeEnd != null">
                AND t.service_start_time &lt;= #{query.serviceStartTimeEnd}
            </if>
            <if test="query.serviceEndTimeStart != null">
                AND t.service_end_time >= #{query.serviceEndTimeStart}
            </if>
            <if test="query.serviceEndTimeEnd != null">
                AND t.service_end_time &lt;= #{query.serviceEndTimeEnd}
            </if>
            <if test="query.expired != null">
                <if test="query.expired == true">
                    AND (t.status = 3 OR (t.service_end_time IS NOT NULL AND t.service_end_time &lt; NOW()))
                </if>
                <if test="query.expired == false">
                    AND t.status != 3 AND (t.service_end_time IS NULL OR t.service_end_time >= NOW())
                </if>
            </if>
            <if test="query.inTrial != null">
                <if test="query.inTrial == true">
                    AND t.tenant_type = 4 AND t.trial_end_time IS NOT NULL AND t.trial_end_time > NOW()
                </if>
                <if test="query.inTrial == false">
                    AND NOT (t.tenant_type = 4 AND t.trial_end_time IS NOT NULL AND t.trial_end_time > NOW())
                </if>
            </if>
        </if>
        ORDER BY t.create_time DESC
    </select>

</mapper>
