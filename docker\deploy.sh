#!/bin/bash

# 美姿姿 - 分布式部署脚本
# 支持分别部署基础设施和各个API服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "美姿姿 - 分布式部署脚本"
    echo ""
    echo "用法: $0 [选项] <服务>"
    echo ""
    echo "服务选项:"
    echo "  infrastructure    部署基础设施组件（数据库、缓存、监控等）"
    echo "  admin-api        部署管理后台API服务"
    echo "  app-api          部署移动应用API服务"
    echo "  client-api       部署客户端API服务"
    echo "  all              部署基础设施（等同于infrastructure）
  full             部署所有服务（单机完整模式）"
    echo ""
    echo "选项:"
    echo "  -h, --help       显示此帮助信息"
    echo "  -e, --env FILE   指定环境变量文件（默认: .env）"
    echo "  -p, --profile    指定Docker Compose profile"
    echo "  -d, --detach     后台运行"
    echo "  -f, --force      强制重新构建镜像"
    echo "  --stop           停止指定服务"
    echo "  --restart        重启指定服务"
    echo "  --logs           查看服务日志"
    echo "  --status         查看服务状态"
    echo ""
    echo "示例:"
    echo "  $0 infrastructure              # 部署基础设施"
    echo "  $0 admin-api -f                # 强制重新构建并部署Admin API"
    echo "  $0 app-api -p monitoring       # 部署App API并启用监控"
    echo "  $0 --stop client-api           # 停止Client API服务"
    echo "  $0 --logs admin-api            # 查看Admin API日志"
}

# 检查Docker和Docker Compose
check_requirements() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在PATH中"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装或不在PATH中"
        exit 1
    fi

    log_info "Docker 和 Docker Compose 检查通过"
}

# 检查环境变量文件
check_env_file() {
    if [[ ! -f "$ENV_FILE" ]]; then
        log_warning "环境变量文件 $ENV_FILE 不存在"
        if [[ -f ".env.example" ]]; then
            log_info "复制 .env.example 到 $ENV_FILE"
            cp .env.example "$ENV_FILE"
            log_warning "请编辑 $ENV_FILE 文件配置正确的环境变量"
            exit 1
        else
            log_error "找不到环境变量文件模板"
            exit 1
        fi
    fi
    log_info "使用环境变量文件: $ENV_FILE"
}

# 创建网络
create_network() {
    if ! docker network ls | grep -q "beautiful-posture-network"; then
        log_info "创建Docker网络: beautiful-posture-network"
        docker network create beautiful-posture-network --driver bridge --subnet=172.20.0.0/16
    else
        log_info "Docker网络已存在: beautiful-posture-network"
    fi
}

# 部署基础设施
deploy_infrastructure() {
    log_info "部署基础设施组件..."
    
    local compose_cmd="docker-compose -f docker-compose.infrastructure.yml --env-file $ENV_FILE"
    
    if [[ "$PROFILE" != "" ]]; then
        compose_cmd="$compose_cmd --profile $PROFILE"
    fi
    
    if [[ "$FORCE_BUILD" == "true" ]]; then
        $compose_cmd build --no-cache
    fi
    
    if [[ "$DETACH" == "true" ]]; then
        $compose_cmd up -d
    else
        $compose_cmd up
    fi
    
    log_success "基础设施部署完成"
}

# 部署API服务
deploy_api_service() {
    local service=$1
    log_info "部署 $service 服务..."
    
    local compose_file="docker-compose.$service.yml"
    if [[ ! -f "$compose_file" ]]; then
        log_error "找不到配置文件: $compose_file"
        exit 1
    fi
    
    local compose_cmd="docker-compose -f $compose_file --env-file $ENV_FILE"
    
    if [[ "$FORCE_BUILD" == "true" ]]; then
        $compose_cmd build --no-cache
    fi
    
    if [[ "$DETACH" == "true" ]]; then
        $compose_cmd up -d
    else
        $compose_cmd up
    fi
    
    log_success "$service 服务部署完成"
}

# 部署基础设施（使用主配置文件）
deploy_all() {
    log_info "部署基础设施组件..."

    local compose_cmd="docker-compose -f docker-compose.yml --env-file $ENV_FILE"

    if [[ "$FORCE_BUILD" == "true" ]]; then
        $compose_cmd build --no-cache
    fi

    if [[ "$DETACH" == "true" ]]; then
        $compose_cmd up -d
    else
        $compose_cmd up
    fi

    log_success "基础设施部署完成"
}

# 部署所有服务（完整模式）
deploy_full() {
    log_info "部署所有服务（完整模式）..."

    local compose_cmd="docker-compose -f docker-compose.full.yml --env-file $ENV_FILE"

    if [[ "$FORCE_BUILD" == "true" ]]; then
        $compose_cmd build --no-cache
    fi

    if [[ "$DETACH" == "true" ]]; then
        $compose_cmd up -d
    else
        $compose_cmd up
    fi

    log_success "所有服务部署完成"
}

# 停止服务
stop_service() {
    local service=$1
    log_info "停止 $service 服务..."
    
    if [[ "$service" == "infrastructure" ]]; then
        docker-compose -f docker-compose.infrastructure.yml --env-file $ENV_FILE down
    elif [[ "$service" == "all" ]]; then
        docker-compose -f docker-compose.yml --env-file $ENV_FILE down
    else
        docker-compose -f "docker-compose.$service.yml" --env-file $ENV_FILE down
    fi
    
    log_success "$service 服务已停止"
}

# 重启服务
restart_service() {
    local service=$1
    log_info "重启 $service 服务..."
    
    stop_service "$service"
    sleep 2
    
    case $service in
        infrastructure)
            deploy_infrastructure
            ;;
        admin-api|app-api|client-api)
            deploy_api_service "$service"
            ;;
        all)
            deploy_all
            ;;
        *)
            log_error "未知服务: $service"
            exit 1
            ;;
    esac
}

# 查看日志
show_logs() {
    local service=$1
    log_info "查看 $service 服务日志..."
    
    if [[ "$service" == "infrastructure" ]]; then
        docker-compose -f docker-compose.infrastructure.yml --env-file $ENV_FILE logs -f
    elif [[ "$service" == "all" ]]; then
        docker-compose -f docker-compose.yml --env-file $ENV_FILE logs -f
    else
        docker-compose -f "docker-compose.$service.yml" --env-file $ENV_FILE logs -f
    fi
}

# 查看状态
show_status() {
    local service=$1
    log_info "查看 $service 服务状态..."
    
    if [[ "$service" == "infrastructure" ]]; then
        docker-compose -f docker-compose.infrastructure.yml --env-file $ENV_FILE ps
    elif [[ "$service" == "all" ]]; then
        docker-compose -f docker-compose.yml --env-file $ENV_FILE ps
    else
        docker-compose -f "docker-compose.$service.yml" --env-file $ENV_FILE ps
    fi
}

# 默认值
ENV_FILE=".env"
PROFILE=""
DETACH="true"
FORCE_BUILD="false"
ACTION="deploy"
SERVICE=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--env)
            ENV_FILE="$2"
            shift 2
            ;;
        -p|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -d|--detach)
            DETACH="true"
            shift
            ;;
        -f|--force)
            FORCE_BUILD="true"
            shift
            ;;
        --stop)
            ACTION="stop"
            shift
            ;;
        --restart)
            ACTION="restart"
            shift
            ;;
        --logs)
            ACTION="logs"
            shift
            ;;
        --status)
            ACTION="status"
            shift
            ;;
        infrastructure|admin-api|app-api|client-api|all|full)
            SERVICE="$1"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查服务参数
if [[ "$SERVICE" == "" ]]; then
    log_error "请指定要操作的服务"
    show_help
    exit 1
fi

# 主逻辑
main() {
    log_info "美姿姿分布式部署脚本启动"
    
    check_requirements
    check_env_file
    create_network
    
    case $ACTION in
        deploy)
            case $SERVICE in
                infrastructure)
                    deploy_infrastructure
                    ;;
                admin-api|app-api|client-api)
                    deploy_api_service "$SERVICE"
                    ;;
                all)
                    deploy_all
                    ;;
                full)
                    deploy_full
                    ;;
            esac
            ;;
        stop)
            stop_service "$SERVICE"
            ;;
        restart)
            restart_service "$SERVICE"
            ;;
        logs)
            show_logs "$SERVICE"
            ;;
        status)
            show_status "$SERVICE"
            ;;
    esac
    
    log_success "操作完成"
}

# 执行主函数
main
