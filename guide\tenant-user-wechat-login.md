# 租户用户微信登录设计

## 概述

为租户用户（pub_customer_account）增加微信登录支持，使租户管理员和普通用户可以通过微信扫码或授权的方式登录系统。

## 表结构设计

### pub_customer_account 表变更

```sql
-- 新增字段
login_type INTEGER NOT NULL DEFAULT 1, -- 1:用户名密码 2:微信 3:手机 4:邮箱
login_identifier VARCHAR(200) NOT NULL, -- 登录标识
wechat_openid VARCHAR(100), -- 微信openid
wechat_unionid VARCHAR(100), -- 微信unionid
bind_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(), -- 绑定时间

-- 字段调整
username VARCHAR(50), -- 改为可选，微信登录时可为空
password VARCHAR(100), -- 改为可选，微信登录时可为空

-- 约束调整
CONSTRAINT uk_tenant_login UNIQUE (tenant_code, login_type, login_identifier)
```

## 登录类型说明

| 登录类型 | 值 | login_identifier | 必填字段 | 说明 |
|---------|---|------------------|----------|------|
| 用户名密码 | 1 | 用户名 | username, password | 传统登录方式 |
| 微信登录 | 2 | 微信openid | wechat_openid | 微信扫码/授权登录 |
| 手机登录 | 3 | 手机号 | phone, password | 手机号+密码登录 |
| 邮箱登录 | 4 | 邮箱地址 | email, password | 邮箱+密码登录 |

## 微信登录流程

### 1. 微信授权登录
```
1. 用户点击微信登录
2. 跳转到微信授权页面
3. 用户授权后获取code
4. 后端通过code获取access_token和openid
5. 查询数据库是否存在该openid的账户
6. 存在则直接登录，不存在则引导绑定
```

### 2. 账户绑定流程
```
1. 新用户微信授权后，系统提示需要绑定账户
2. 用户选择绑定方式：
   - 绑定已有账户（输入用户名密码）
   - 创建新账户（填写基本信息）
3. 绑定成功后，创建微信登录记录
4. 后续可直接通过微信登录
```

## 数据示例

### 用户名密码登录账户
```sql
INSERT INTO pub_customer_account (
    tenant_code, login_type, login_identifier, username, password,
    real_name, account_type
) VALUES (
    'demo_tenant', 1, 'admin', 'admin', '$2a$10$...',
    '管理员', 1
);
```

### 微信登录账户
```sql
INSERT INTO pub_customer_account (
    tenant_code, login_type, login_identifier, wechat_openid, wechat_unionid,
    real_name, account_type
) VALUES (
    'demo_tenant', 2, 'wx_openid_123456', 'wx_openid_123456', 'wx_unionid_789',
    '张三', 2
);
```

### 同一用户多种登录方式
```sql
-- 用户名密码登录
INSERT INTO pub_customer_account (
    tenant_code, login_type, login_identifier, username, password,
    real_name, sys_user_id, is_primary
) VALUES (
    'demo_tenant', 1, 'zhangsan', 'zhangsan', '$2a$10$...',
    '张三', 1001, true
);

-- 微信登录（绑定到同一用户）
INSERT INTO pub_customer_account (
    tenant_code, login_type, login_identifier, wechat_openid,
    real_name, sys_user_id, is_primary
) VALUES (
    'demo_tenant', 2, 'wx_openid_123456', 'wx_openid_123456',
    '张三', 1001, false
);
```

## API接口设计

### 1. 微信登录接口
```java
@PostMapping("/auth/wechat/login")
public Result<LoginResponse> wechatLogin(@RequestBody WechatLoginRequest request) {
    // 1. 验证微信授权code
    // 2. 获取用户微信信息
    // 3. 查询或创建账户
    // 4. 生成登录token
    // 5. 返回登录结果
}
```

### 2. 账户绑定接口
```java
@PostMapping("/auth/wechat/bind")
public Result<Void> bindWechatAccount(@RequestBody WechatBindRequest request) {
    // 1. 验证现有账户
    // 2. 绑定微信信息
    // 3. 创建微信登录记录
}
```

### 3. 解绑微信接口
```java
@PostMapping("/auth/wechat/unbind")
public Result<Void> unbindWechatAccount() {
    // 1. 验证用户身份
    // 2. 删除微信登录记录
    // 3. 清除微信信息
}
```

## 安全考虑

### 1. 数据安全
- openid和unionid需要加密存储
- 定期清理过期的授权信息
- 记录登录日志和异常操作

### 2. 业务安全
- 同一微信账号只能绑定一个租户账户
- 支持解绑和重新绑定功能
- 管理员可以查看和管理微信绑定情况

### 3. 权限控制
- 微信登录用户的权限与普通用户一致
- 通过sys_user_id关联租户内的用户权限
- 支持微信登录用户的权限管理

## 配置说明

### 微信应用配置
```yaml
wechat:
  app-id: your_app_id
  app-secret: your_app_secret
  redirect-uri: https://your-domain.com/auth/wechat/callback
```

### 登录配置
```yaml
auth:
  wechat:
    enabled: true
    auto-register: false  # 是否允许自动注册
    require-bind: true    # 是否要求绑定已有账户
```

## 注意事项

1. **数据一致性**：确保同一用户的多个登录方式关联到同一个sys_user_id
2. **主账户标识**：is_primary字段标识主要登录方式
3. **租户隔离**：微信登录同样需要遵循租户隔离原则
4. **用户体验**：提供便捷的账户绑定和管理功能
5. **数据迁移**：现有用户数据需要适配新的表结构
