package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 充值定金
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_recharge_deposit")
public class RechargeDeposit extends BaseEntity {

    private Long rechargeId;

    private Long storeId;

    private String packageCode;

    private String packageName;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Short quantity;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 总价
     */
    private BigDecimal totalPrice;
}
