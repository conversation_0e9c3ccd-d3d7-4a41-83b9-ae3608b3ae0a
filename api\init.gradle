/**
 * Gradle全局初始化脚本 - 美姿姿项目
 * 此脚本会在所有项目构建前执行，用于配置全局的仓库镜像
 * 
 * 使用方法：
 * 1. 项目级别：将此文件放在项目根目录，使用 --init-script init.gradle
 * 2. 全局级别：将此文件复制到 ~/.gradle/init.gradle
 * 
 * <AUTHOR>
 */

allprojects {
    repositories {
        // 移除所有现有仓库
        all { ArtifactRepository repo ->
            if (repo instanceof MavenArtifactRepository) {
                def url = repo.url.toString()
                // 如果不是中国镜像，则移除
                if (!url.contains('aliyun.com') && 
                    !url.contains('tencent.com') && 
                    !url.contains('huaweicloud.com')) {
                    remove repo
                }
            }
        }
        
        // 添加中国镜像源（按优先级排序）
        maven {
            name 'AliYunMaven'
            url 'https://maven.aliyun.com/repository/public/'
            content {
                // 包含所有组
                includeGroupByRegex '.*'
            }
        }
        
        maven {
            name 'AliYunSpring'
            url 'https://maven.aliyun.com/repository/spring/'
            content {
                includeGroup 'org.springframework'
                includeGroup 'org.springframework.boot'
                includeGroup 'org.springframework.cloud'
                includeGroup 'org.springframework.security'
                includeGroup 'org.springframework.data'
            }
        }
        
        maven {
            name 'AliYunSpringPlugin'
            url 'https://maven.aliyun.com/repository/spring-plugin/'
            content {
                includeGroup 'org.springframework.boot'
                includeGroup 'io.spring.gradle'
            }
        }
        
        maven {
            name 'AliYunGradle'
            url 'https://maven.aliyun.com/repository/gradle-plugin/'
            content {
                includeGroupByRegex 'com\\.gradle.*'
                includeGroupByRegex 'org\\.gradle.*'
            }
        }
        
        maven {
            name 'TencentMaven'
            url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/'
        }
        
        maven {
            name 'HuaweiMaven'
            url 'https://repo.huaweicloud.com/repository/maven/'
        }
        
        // 最后添加官方仓库作为备用
        mavenCentral()
        gradlePluginPortal()
    }
    
    // 配置插件仓库
    pluginManagement {
        repositories {
            maven {
                name 'AliYunGradlePlugin'
                url 'https://maven.aliyun.com/repository/gradle-plugin/'
            }
            maven {
                name 'TencentGradlePlugin'
                url 'https://mirrors.cloud.tencent.com/nexus/repository/gradle-plugins/'
            }
            gradlePluginPortal()
            mavenCentral()
        }
    }
}

// 配置构建脚本仓库
buildscript {
    repositories {
        maven {
            name 'AliYunMaven'
            url 'https://maven.aliyun.com/repository/public/'
        }
        maven {
            name 'AliYunGradle'
            url 'https://maven.aliyun.com/repository/gradle-plugin/'
        }
        gradlePluginPortal()
        mavenCentral()
    }
}

// 全局配置
gradle.projectsEvaluated {
    println "🚀 美姿姿项目构建 - 使用中国镜像源加速"
    println "📦 Maven仓库: 阿里云、腾讯云、华为云"
    println "⚡ Gradle版本: ${gradle.gradleVersion}"
    println "☕ Java版本: ${System.getProperty('java.version')}"
    println "🏠 项目路径: ${gradle.rootProject.projectDir}"
    println "=" * 60
}

// 任务执行监听
gradle.taskGraph.whenReady { taskGraph ->
    if (taskGraph.hasTask(':build') || taskGraph.hasTask(':buildAll')) {
        println "🔨 开始构建美姿姿项目..."
    }
}

// 构建完成监听
gradle.buildFinished { result ->
    if (result.failure) {
        println "❌ 构建失败: ${result.failure.message}"
    } else {
        println "✅ 构建成功完成!"
    }
}
