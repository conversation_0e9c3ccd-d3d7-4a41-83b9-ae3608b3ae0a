package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 充值项目
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_recharge_project")
public class RechargeProject extends BaseEntity {

    private Long rechargeId;

    private Long projectId;

    private String projectCode;

    private String projectName;

    private String projectCategory;

    /**
     * 服务时长
     */
    private String serviceDuration;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Short quantity;

    /**
     * 赠送数量
     */
    private Short giftQuantity;

    /**
     * 总价
     */
    private BigDecimal totalPrice;
}
