# 会员系统设计总结

## 🎯 系统概述

为了支持小程序会员登录和管理，我们设计了一套完整的多租户会员系统。每个租户都有自己独立的会员体系，会员可以通过小程序进行登录和使用服务。

## 📊 系统架构

### 多租户会员架构
```
租户A (Schema: tenant_a)
├── 会员管理
│   ├── sys_member (会员表)
│   ├── sys_member_level (会员等级表)
│   └── sys_member_login_log (登录记录表)
└── 权限管理
    ├── sys_menu (菜单表)
    ├── sys_role (角色表)
    └── sys_department (部门表)

租户B (Schema: tenant_b)
├── 会员管理 (相同结构，数据隔离)
└── 权限管理 (相同结构，数据隔离)

公共Schema (public)
├── pub_account (管理员账户表)
└── pub_tenant (租户表)
```

### API架构分层
```
小程序端 (会员)
    ↓
app-api (小程序接口)
    ↓
service (业务逻辑层)
    ↓
core (核心组件层)
    ↓
数据库 (多租户Schema)
```

## 🗄️ 数据库设计

### 核心表结构

#### 1. 会员表 (sys_member)
```sql
-- 存储会员基本信息
CREATE TABLE sys_member (
    id BIGSERIAL PRIMARY KEY,
    member_no VARCHAR(50) NOT NULL,        -- 会员编号
    wx_openid VARCHAR(100),                -- 微信OpenID
    wx_unionid VARCHAR(100),               -- 微信UnionID
    phone VARCHAR(20),                     -- 手机号
    nickname VARCHAR(50),                  -- 昵称
    real_name VARCHAR(50),                 -- 真实姓名
    gender INTEGER DEFAULT 0,             -- 性别
    birthday DATE,                         -- 生日
    avatar VARCHAR(500),                   -- 头像
    level_id BIGINT,                       -- 会员等级ID
    level_name VARCHAR(50),                -- 会员等级名称
    points INTEGER DEFAULT 0,             -- 积分
    balance BIGINT DEFAULT 0,              -- 余额(分)
    status INTEGER DEFAULT 1,             -- 状态
    register_source INTEGER DEFAULT 1,     -- 注册来源
    referrer_id BIGINT,                    -- 推荐人ID
    referrer_no VARCHAR(50),               -- 推荐人编号
    last_login_time TIMESTAMP,             -- 最后登录时间
    last_login_ip VARCHAR(50),             -- 最后登录IP
    -- 标准字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);
```

#### 2. 会员等级表 (sys_member_level)
```sql
-- 存储会员等级配置
CREATE TABLE sys_member_level (
    id BIGSERIAL PRIMARY KEY,
    level_name VARCHAR(50) NOT NULL,       -- 等级名称
    level_code VARCHAR(50) NOT NULL,       -- 等级编码
    level_order INTEGER DEFAULT 0,         -- 等级序号
    required_points INTEGER DEFAULT 0,     -- 升级所需积分
    required_amount BIGINT DEFAULT 0,      -- 升级所需消费金额
    discount_rate DECIMAL(5,4) DEFAULT 1.0000, -- 折扣率
    points_rate DECIMAL(5,4) DEFAULT 1.0000,   -- 积分倍率
    description VARCHAR(200),              -- 等级描述
    benefits VARCHAR(1000),                -- 等级权益
    is_default INTEGER DEFAULT 0,         -- 是否默认等级
    status INTEGER DEFAULT 1,             -- 状态
    -- 标准字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);
```

#### 3. 会员登录记录表 (sys_member_login_log)
```sql
-- 存储会员登录记录
CREATE TABLE sys_member_login_log (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT,                      -- 会员ID
    member_no VARCHAR(50),                 -- 会员编号
    login_type INTEGER DEFAULT 1,         -- 登录方式
    platform INTEGER DEFAULT 1,           -- 登录平台
    login_ip VARCHAR(50),                  -- 登录IP
    login_address VARCHAR(200),            -- 登录地址
    user_agent VARCHAR(500),               -- 用户代理
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 登录时间
    login_status INTEGER DEFAULT 1,       -- 登录状态
    fail_reason VARCHAR(200),              -- 失败原因
    -- 标准字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted INTEGER DEFAULT 0
);
```

## 🔧 核心功能

### 1. 会员认证系统

#### 微信小程序登录流程
```
1. 小程序获取微信授权码 (wx.login)
2. 发送授权码到后端 (/api/app/auth/wechat-mini-login)
3. 后端调用微信API获取OpenID
4. 根据OpenID查询或创建会员
5. 生成访问令牌和刷新令牌
6. 返回登录结果和会员信息
```

#### 手机号登录流程
```
1. 小程序获取手机号
2. 发送手机号和验证码到后端 (/api/app/auth/phone-login)
3. 后端验证短信验证码
4. 根据手机号查询或创建会员
5. 生成访问令牌和刷新令牌
6. 返回登录结果和会员信息
```

### 2. 会员管理功能

#### 会员信息管理
- ✅ 查询会员详细信息
- ✅ 更新会员基本信息
- ✅ 会员状态管理（正常、禁用、冻结）
- ✅ 会员等级管理

#### 会员积分和余额
- ✅ 积分查询和更新
- ✅ 余额查询和更新
- ✅ 会员等级自动升级

#### 推荐关系管理
- ✅ 推荐人设置
- ✅ 推荐会员列表查询
- ✅ 推荐统计

### 3. 多租户隔离

#### 数据隔离
- 每个租户有独立的Schema
- 会员数据完全隔离
- 自动Schema切换

#### 认证隔离
- 基于Sa-Token的会员认证
- 租户上下文自动设置
- 会员权限独立管理

## 🚀 API接口设计

### App API (小程序接口)

#### 认证接口 (/api/app/auth)
```
POST /wechat-mini-login    # 微信小程序登录
POST /phone-login          # 手机号登录
POST /logout               # 登出
POST /refresh-token        # 刷新令牌
GET  /current-member       # 获取当前会员信息
GET  /check-login          # 检查登录状态
GET  /login-info           # 获取登录信息
```

#### 会员接口 (/api/app/member)
```
GET  /profile              # 获取会员详细信息
PUT  /profile              # 更新会员信息
GET  /points               # 获取积分信息
GET  /balance              # 获取余额信息
GET  /referrals            # 获取推荐会员列表
GET  /by-no/{memberNo}     # 根据会员编号查询
GET  /statistics           # 获取会员统计信息
```

### 接口响应格式
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据
    }
}
```

## 🔒 安全设计

### 1. 认证安全
- 基于Sa-Token的JWT令牌认证
- 访问令牌 + 刷新令牌机制
- 令牌自动过期和刷新

### 2. 数据安全
- 多租户数据完全隔离
- 敏感信息加密存储
- API接口权限验证

### 3. 登录安全
- 登录记录完整追踪
- 异常登录检测
- IP地址记录和验证

## 📱 小程序集成

### 1. 微信授权登录
```javascript
// 小程序端代码示例
wx.login({
  success: (res) => {
    if (res.code) {
      // 发送授权码到后端
      wx.request({
        url: 'https://api.example.com/api/app/auth/wechat-mini-login',
        method: 'POST',
        data: {
          code: res.code,
          platform: 1, // 微信小程序
          userInfo: {
            nickName: '用户昵称',
            avatarUrl: '头像URL',
            gender: 1
          }
        },
        success: (response) => {
          if (response.data.code === 200) {
            // 保存令牌
            wx.setStorageSync('accessToken', response.data.data.accessToken);
            wx.setStorageSync('refreshToken', response.data.data.refreshToken);
          }
        }
      });
    }
  }
});
```

### 2. API调用示例
```javascript
// 带认证的API调用
function callAPI(url, data) {
  const token = wx.getStorageSync('accessToken');
  
  wx.request({
    url: url,
    method: 'GET',
    header: {
      'Authorization': 'Bearer ' + token
    },
    data: data,
    success: (response) => {
      if (response.data.code === 401) {
        // 令牌过期，尝试刷新
        refreshToken();
      } else {
        // 处理正常响应
        console.log(response.data);
      }
    }
  });
}
```

## 🎯 使用场景

### 1. 会员注册登录
- 微信小程序一键登录
- 手机号验证登录
- 推荐码注册

### 2. 会员信息管理
- 个人资料维护
- 积分余额查询
- 等级权益查看

### 3. 推荐营销
- 推荐好友注册
- 推荐关系管理
- 推荐奖励发放

### 4. 数据统计
- 会员增长统计
- 登录活跃度分析
- 推荐效果分析

## 🎉 总结

### ✅ 系统特点

1. **多租户支持** - 完全的数据隔离和独立管理
2. **小程序友好** - 专为小程序设计的API接口
3. **安全可靠** - 完整的认证和权限体系
4. **功能完整** - 涵盖会员管理的各个方面
5. **易于扩展** - 模块化设计，便于功能扩展

### 🚀 技术优势

- **Sa-Token认证** - 轻量级、高性能的认证框架
- **MyBatis Plus** - 简化数据库操作
- **多租户架构** - 支持SaaS模式部署
- **RESTful API** - 标准的API设计
- **Spring Boot** - 现代化的Java开发框架

现在系统已经具备了完整的会员管理能力，可以支持小程序会员的注册、登录、信息管理等各种场景！🎊
