package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member")
public class Member extends BaseEntity {

    private String code;

    private Long storeId;

    private String wechatName;

    private String wechatId;

    private String realName;

    private Short gender;

    private LocalDate birthday;

    /**
     * 阴历生日
     */
    private String chinaBirthday;

    private Long avatarId;

    private String phone;

    private String address;

    private Short status;

    /**
     * 职业
     */
    private String consumption;

    /**
     * 是否有小孩
     */
    private Boolean hasChildren;

    /**
     * 婚姻状态 1=已婚，2=未婚
     */
    private Short maritalStatus;

    private String remark;

    // 性别常量
    public static final int GENDER_MALE = 1;    // 男
    public static final int GENDER_FEMALE = 2;  // 女

    // 状态常量
    public static final int STATUS_DISABLED = 0; // 禁用
    public static final int STATUS_NORMAL = 1;   // 正常

    // 婚姻状况常量
    public static final int MARITAL_SINGLE = 1;   // 未婚
    public static final int MARITAL_MARRIED = 2;  // 已婚
    public static final int MARITAL_DIVORCED = 3; // 离异
    public static final int MARITAL_WIDOWED = 4;  // 丧偶
}
