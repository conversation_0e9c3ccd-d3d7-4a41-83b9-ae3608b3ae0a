package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_product")
public class Product extends BaseEntity {

    private String name;

    private String code;

    /**
     * 产品分类字典表
     */
    private Long dictCategoryId;

    /**
     * 产品品牌字典表
     */
    private String dictBrandId;

    /**
     * 进价
     */
    private BigDecimal costPrice;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    /**
     * 进货单位
     */
    private BigDecimal purchaseUnit;

    /**
     * 标准单位
     */
    private String standardUnit;

    /**
     * 消耗单位
     */
    private String consumptionUnit;

    /**
     * 进货单位 换算 标准单位
     */
    private Integer purchaseToStandard;

    /**
     * 标准单位 换算 消耗单位
     */
    private Integer purchaseToConsumption;

    /**
     * 1 上架 | 0 下架
     */
    private Short status;

    // 状态常量
    public static final short STATUS_DISABLED = 0; // 禁用
    public static final short STATUS_NORMAL = 1;   // 正常
}
