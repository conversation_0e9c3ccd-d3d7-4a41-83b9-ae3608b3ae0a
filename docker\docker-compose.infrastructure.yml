# 美姿姿 - 基础设施组件配置
# 包含数据库、缓存、消息队列等基础组件
# 适用于生产环境的基础设施服务器部署
version: '3.8'

services:
  # PostgreSQL数据库 - 主数据库
  postgres:
    image: postgres:15-alpine
    container_name: beautiful-posture-postgres
    environment:
      POSTGRES_DB: beautiful_posture
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./scripts/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存 - 主缓存
  redis:
    image: redis:7-alpine
    container_name: beautiful-posture-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./scripts/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Sentinel - 高可用配置（可选）
  redis-sentinel:
    image: redis:7-alpine
    container_name: beautiful-posture-redis-sentinel
    ports:
      - "${REDIS_SENTINEL_PORT:-26379}:26379"
    volumes:
      - ./scripts/sentinel.conf:/usr/local/etc/redis/sentinel.conf
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    profiles:
      - ha  # 高可用模式才启动

  # Nginx - 负载均衡和反向代理
  nginx:
    image: nginx:alpine
    container_name: beautiful-posture-nginx
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    profiles:
      - proxy  # 需要代理时才启动

  # Elasticsearch - 日志和搜索（可选）
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: beautiful-posture-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "${ES_PORT:-9200}:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    profiles:
      - logging  # 需要日志系统时才启动

  # Kibana - 日志可视化（可选）
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: beautiful-posture-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "${KIBANA_PORT:-5601}:5601"
    depends_on:
      - elasticsearch
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    profiles:
      - logging  # 需要日志系统时才启动

  # Prometheus - 监控指标收集（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: beautiful-posture-prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    profiles:
      - monitoring  # 需要监控时才启动

  # Grafana - 监控可视化（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: beautiful-posture-grafana
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    depends_on:
      - prometheus
    networks:
      - beautiful-posture-network
    restart: unless-stopped
    profiles:
      - monitoring  # 需要监控时才启动

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  beautiful-posture-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
