package com.deepaic.core.auth.services;

import cn.dev33.satoken.stp.StpUtil;
import com.deepaic.core.auth.interfaces.TokenService;
import com.deepaic.core.auth.models.UserPrincipal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 基于Sa-Token的令牌服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SaTokenServiceImpl implements TokenService {

    @Override
    public String generateAccessToken(UserPrincipal userPrincipal) {
        try {
            // 构建登录ID：userType:userId
            String loginId = buildLoginId(userPrincipal);
            
            // 执行登录
            StpUtil.login(loginId);
            
            // 设置用户信息到Session
            StpUtil.getSession().set("userPrincipal", userPrincipal);
            
            // 返回令牌
            String token = StpUtil.getTokenValue();
            log.info("生成访问令牌成功: userId={}, userType={}", 
                    userPrincipal.getUserId(), userPrincipal.getUserType());
            
            return token;
        } catch (Exception e) {
            log.error("生成访问令牌失败: userId={}", userPrincipal.getUserId(), e);
            throw new RuntimeException("生成访问令牌失败", e);
        }
    }

    @Override
    public String generateRefreshToken(UserPrincipal userPrincipal) {
        // Sa-Token 默认不支持刷新令牌，这里返回访问令牌
        // 如果需要刷新令牌功能，可以考虑使用JWT或自定义实现
        return generateAccessToken(userPrincipal);
    }

    @Override
    public boolean validateToken(String token) {
        try {
            return StpUtil.getLoginIdByToken(token) != null;
        } catch (Exception e) {
            log.debug("令牌验证失败: token={}", token, e);
            return false;
        }
    }

    @Override
    public UserPrincipal parseToken(String token) {
        try {
            // 通过令牌获取登录ID
            Object loginId = StpUtil.getLoginIdByToken(token);
            if (loginId == null) {
                return null;
            }

            // 从Session中获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) StpUtil.getSessionByLoginId(loginId).get("userPrincipal");
            if (userPrincipal != null) {
                return userPrincipal;
            }

            // 如果Session中没有，尝试从登录ID解析
            return parseLoginId(loginId.toString());
        } catch (Exception e) {
            log.debug("解析令牌失败: token={}", token, e);
            return null;
        }
    }

    @Override
    public Long getTokenExpiration(String token) {
        try {
            return StpUtil.getTokenTimeout();
        } catch (Exception e) {
            log.debug("获取令牌过期时间失败: token={}", token, e);
            return null;
        }
    }

    @Override
    public String refreshAccessToken(String refreshToken) {
        // Sa-Token 默认不支持刷新令牌
        // 这里简单返回原令牌，实际项目中可能需要重新登录
        if (validateToken(refreshToken)) {
            return refreshToken;
        }
        throw new RuntimeException("刷新令牌无效");
    }

    @Override
    public void revokeToken(String token) {
        try {
            Object loginId = StpUtil.getLoginIdByToken(token);
            if (loginId != null) {
                StpUtil.logout(loginId);
                log.info("撤销令牌成功: loginId={}", loginId);
            }
        } catch (Exception e) {
            log.error("撤销令牌失败: token={}", token, e);
        }
    }

    @Override
    public void revokeAllTokens(String userId) {
        try {
            // 需要遍历所有可能的用户类型
            for (UserPrincipal.UserType userType : UserPrincipal.UserType.values()) {
                String loginId = userType.name() + ":" + userId;
                StpUtil.logout(loginId);
            }
            log.info("撤销用户所有令牌成功: userId={}", userId);
        } catch (Exception e) {
            log.error("撤销用户所有令牌失败: userId={}", userId, e);
        }
    }

    /**
     * 构建登录ID
     */
    private String buildLoginId(UserPrincipal userPrincipal) {
        return userPrincipal.getUserType().name() + ":" + userPrincipal.getUserId();
    }

    /**
     * 解析登录ID
     */
    private UserPrincipal parseLoginId(String loginId) {
        try {
            String[] parts = loginId.split(":");
            if (parts.length != 2) {
                return null;
            }

            String userTypeCode = parts[0];
            String userId = parts[1];
            UserPrincipal.UserType userType = UserPrincipal.UserType.valueOf(userTypeCode);

            return UserPrincipal.builder()
                    .userId(userId)
                    .userType(userType)
                    .status(UserPrincipal.AccountStatus.ENABLED)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("解析登录ID失败: loginId=" + loginId, e);
        }
    }
}
