# 租户用户登录示例

## 数据插入示例

### 1. 传统用户名密码登录
```sql
-- 创建管理员账户
INSERT INTO public.pub_customer_account (
    id, tenant_code, login_type, login_identifier, username, password,
    real_name, account_type, sys_user_id, is_primary
) VALUES (
    1001, 'demo_tenant', 1, 'admin', 'admin', '$2a$10$encrypted_password',
    '系统管理员', 1, 2001, true
);

-- 创建普通用户账户
INSERT INTO public.pub_customer_account (
    id, tenant_code, login_type, login_identifier, username, password,
    real_name, account_type, sys_user_id, is_primary
) VALUES (
    1002, 'demo_tenant', 1, 'zhangsan', 'zhangsan', '$2a$10$encrypted_password',
    '张三', 2, 2002, true
);
```

### 2. 微信登录账户
```sql
-- 纯微信登录用户
INSERT INTO public.pub_customer_account (
    id, tenant_code, login_type, login_identifier, 
    wechat_openid, wechat_unionid, real_name, account_type, sys_user_id, is_primary
) VALUES (
    1003, 'demo_tenant', 2, 'wx_openid_abc123',
    'wx_openid_abc123', 'wx_unionid_def456', '李四', 2, 2003, true
);
```

### 3. 手机号登录账户
```sql
-- 手机号+密码登录
INSERT INTO public.pub_customer_account (
    id, tenant_code, login_type, login_identifier, 
    phone, password, real_name, account_type, sys_user_id, is_primary
) VALUES (
    1004, 'demo_tenant', 3, '***********',
    '***********', '$2a$10$encrypted_password', '王五', 2, 2004, true
);
```

### 4. 邮箱登录账户
```sql
-- 邮箱+密码登录
INSERT INTO public.pub_customer_account (
    id, tenant_code, login_type, login_identifier, 
    email, password, real_name, account_type, sys_user_id, is_primary
) VALUES (
    1005, 'demo_tenant', 4, '<EMAIL>',
    '<EMAIL>', '$2a$10$encrypted_password', '赵六', 2, 2005, true
);
```

### 5. 同一用户多种登录方式
```sql
-- 主账户：用户名密码登录
INSERT INTO public.pub_customer_account (
    id, tenant_code, login_type, login_identifier, username, password,
    real_name, account_type, sys_user_id, is_primary
) VALUES (
    1006, 'demo_tenant', 1, 'multiuser', 'multiuser', '$2a$10$encrypted_password',
    '多登录用户', 2, 2006, true
);

-- 绑定微信登录
INSERT INTO public.pub_customer_account (
    id, tenant_code, login_type, login_identifier, 
    wechat_openid, wechat_unionid, real_name, account_type, sys_user_id, is_primary
) VALUES (
    1007, 'demo_tenant', 2, 'wx_openid_multi123',
    'wx_openid_multi123', 'wx_unionid_multi456', '多登录用户', 2, 2006, false
);

-- 绑定手机登录
INSERT INTO public.pub_customer_account (
    id, tenant_code, login_type, login_identifier, 
    phone, password, real_name, account_type, sys_user_id, is_primary
) VALUES (
    1008, 'demo_tenant', 3, '***********',
    '***********', '$2a$10$encrypted_password', '多登录用户', 2, 2006, false
);
```

## 查询示例

### 1. 根据登录方式查询用户
```sql
-- 查询用户名登录的用户
SELECT * FROM public.pub_customer_account 
WHERE tenant_code = 'demo_tenant' AND login_type = 1;

-- 查询微信登录的用户
SELECT * FROM public.pub_customer_account 
WHERE tenant_code = 'demo_tenant' AND login_type = 2;
```

### 2. 根据登录标识查询
```sql
-- 用户名登录验证
SELECT * FROM public.pub_customer_account 
WHERE tenant_code = 'demo_tenant' 
  AND login_type = 1 
  AND login_identifier = 'admin';

-- 微信登录验证
SELECT * FROM public.pub_customer_account 
WHERE tenant_code = 'demo_tenant' 
  AND login_type = 2 
  AND login_identifier = 'wx_openid_abc123';
```

### 3. 查询用户的所有登录方式
```sql
-- 查询某个用户的所有登录账户
SELECT 
    login_type,
    login_identifier,
    username,
    wechat_openid,
    phone,
    email,
    is_primary,
    bind_time
FROM public.pub_customer_account 
WHERE tenant_code = 'demo_tenant' 
  AND sys_user_id = 2006
ORDER BY is_primary DESC, bind_time ASC;
```

### 4. 统计各种登录方式的用户数量
```sql
-- 统计租户的登录方式分布
SELECT 
    login_type,
    CASE login_type
        WHEN 1 THEN '用户名密码'
        WHEN 2 THEN '微信登录'
        WHEN 3 THEN '手机登录'
        WHEN 4 THEN '邮箱登录'
        ELSE '未知'
    END AS login_type_name,
    COUNT(*) as user_count
FROM public.pub_customer_account 
WHERE tenant_code = 'demo_tenant' 
  AND deleted = false
GROUP BY login_type
ORDER BY login_type;
```

## 业务场景示例

### 1. 用户登录验证
```sql
-- 验证用户名密码登录
SELECT id, sys_user_id, account_type, status 
FROM public.pub_customer_account 
WHERE tenant_code = ? 
  AND login_type = 1 
  AND login_identifier = ? 
  AND password = ? 
  AND status = 1 
  AND deleted = false;

-- 验证微信登录
SELECT id, sys_user_id, account_type, status 
FROM public.pub_customer_account 
WHERE tenant_code = ? 
  AND login_type = 2 
  AND login_identifier = ? 
  AND status = 1 
  AND deleted = false;
```

### 2. 账户绑定检查
```sql
-- 检查微信是否已绑定其他账户
SELECT COUNT(*) 
FROM public.pub_customer_account 
WHERE tenant_code = ? 
  AND login_type = 2 
  AND wechat_openid = ? 
  AND deleted = false;

-- 检查用户是否已绑定微信
SELECT COUNT(*) 
FROM public.pub_customer_account 
WHERE tenant_code = ? 
  AND sys_user_id = ? 
  AND login_type = 2 
  AND deleted = false;
```

### 3. 账户管理
```sql
-- 禁用用户的所有登录方式
UPDATE public.pub_customer_account 
SET status = 0, updated_at = NOW() 
WHERE tenant_code = ? AND sys_user_id = ?;

-- 解绑微信登录
DELETE FROM public.pub_customer_account 
WHERE tenant_code = ? 
  AND sys_user_id = ? 
  AND login_type = 2;
```

## 注意事项

1. **数据一致性**：同一用户的多个登录方式必须关联到同一个 `sys_user_id`
2. **主账户标识**：每个用户只能有一个主账户（`is_primary = true`）
3. **唯一性约束**：`(tenant_code, login_type, login_identifier)` 必须唯一
4. **微信字段**：微信登录时，`wechat_openid` 和 `login_identifier` 应该保持一致
5. **密码字段**：微信登录时 `password` 字段可以为空
6. **租户隔离**：所有操作都必须带上 `tenant_code` 进行租户隔离
