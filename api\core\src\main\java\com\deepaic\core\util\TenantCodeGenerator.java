package com.deepaic.core.util;

import com.deepaic.core.mapper.TenantMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.text.Normalizer;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 租户编码生成器
 * 根据租户名称和企业名称生成唯一的租户编码
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantCodeGenerator {

    private final TenantMapper tenantMapper;
    private final Random random = new SecureRandom();

    // 中文转拼音映射（简化版）
    private static final String[][] PINYIN_MAP = {
        {"阿", "a"}, {"爱", "ai"}, {"安", "an"}, {"奥", "ao"},
        {"八", "ba"}, {"白", "bai"}, {"班", "ban"}, {"宝", "bao"}, {"北", "bei"}, {"本", "ben"}, {"比", "bi"}, {"边", "bian"}, {"标", "biao"}, {"别", "bie"}, {"宾", "bin"}, {"冰", "bing"}, {"博", "bo"}, {"不", "bu"},
        {"才", "cai"}, {"参", "can"}, {"草", "cao"}, {"策", "ce"}, {"层", "ceng"}, {"查", "cha"}, {"产", "chan"}, {"常", "chang"}, {"车", "che"}, {"成", "cheng"}, {"城", "cheng"}, {"程", "cheng"}, {"吃", "chi"}, {"出", "chu"}, {"创", "chuang"}, {"春", "chun"}, {"从", "cong"}, {"村", "cun"},
        {"大", "da"}, {"带", "dai"}, {"单", "dan"}, {"当", "dang"}, {"道", "dao"}, {"得", "de"}, {"的", "de"}, {"地", "di"}, {"第", "di"}, {"点", "dian"}, {"电", "dian"}, {"定", "ding"}, {"东", "dong"}, {"都", "dou"}, {"度", "du"}, {"对", "dui"}, {"多", "duo"},
        {"而", "er"}, {"二", "er"},
        {"发", "fa"}, {"法", "fa"}, {"反", "fan"}, {"方", "fang"}, {"房", "fang"}, {"非", "fei"}, {"分", "fen"}, {"风", "feng"}, {"服", "fu"}, {"福", "fu"},
        {"该", "gai"}, {"感", "gan"}, {"高", "gao"}, {"个", "ge"}, {"给", "gei"}, {"根", "gen"}, {"更", "geng"}, {"工", "gong"}, {"公", "gong"}, {"关", "guan"}, {"管", "guan"}, {"光", "guang"}, {"广", "guang"}, {"国", "guo"}, {"过", "guo"},
        {"还", "hai"}, {"海", "hai"}, {"好", "hao"}, {"和", "he"}, {"很", "hen"}, {"红", "hong"}, {"后", "hou"}, {"华", "hua"}, {"化", "hua"}, {"话", "hua"}, {"环", "huan"}, {"回", "hui"}, {"会", "hui"}, {"活", "huo"}, {"火", "huo"},
        {"机", "ji"}, {"基", "ji"}, {"及", "ji"}, {"家", "jia"}, {"加", "jia"}, {"间", "jian"}, {"见", "jian"}, {"建", "jian"}, {"江", "jiang"}, {"将", "jiang"}, {"交", "jiao"}, {"教", "jiao"}, {"接", "jie"}, {"结", "jie"}, {"解", "jie"}, {"金", "jin"}, {"进", "jin"}, {"经", "jing"}, {"就", "jiu"}, {"九", "jiu"}, {"局", "ju"}, {"具", "ju"},
        {"开", "kai"}, {"看", "kan"}, {"可", "ke"}, {"科", "ke"}, {"空", "kong"}, {"口", "kou"}, {"快", "kuai"},
        {"来", "lai"}, {"老", "lao"}, {"了", "le"}, {"类", "lei"}, {"里", "li"}, {"理", "li"}, {"力", "li"}, {"立", "li"}, {"利", "li"}, {"连", "lian"}, {"两", "liang"}, {"量", "liang"}, {"林", "lin"}, {"流", "liu"}, {"六", "liu"}, {"龙", "long"}, {"路", "lu"}, {"绿", "lv"},
        {"马", "ma"}, {"买", "mai"}, {"满", "man"}, {"没", "mei"}, {"美", "mei"}, {"门", "men"}, {"们", "men"}, {"民", "min"}, {"明", "ming"}, {"名", "ming"},
        {"那", "na"}, {"南", "nan"}, {"内", "nei"}, {"能", "neng"}, {"你", "ni"}, {"年", "nian"}, {"宁", "ning"}, {"农", "nong"},
        {"欧", "ou"},
        {"平", "ping"}, {"品", "pin"}, {"普", "pu"},
        {"七", "qi"}, {"其", "qi"}, {"起", "qi"}, {"前", "qian"}, {"强", "qiang"}, {"青", "qing"}, {"清", "qing"}, {"情", "qing"}, {"区", "qu"}, {"全", "quan"}, {"群", "qun"},
        {"然", "ran"}, {"人", "ren"}, {"日", "ri"}, {"如", "ru"}, {"入", "ru"},
        {"三", "san"}, {"色", "se"}, {"山", "shan"}, {"商", "shang"}, {"上", "shang"}, {"社", "she"}, {"设", "she"}, {"深", "shen"}, {"生", "sheng"}, {"省", "sheng"}, {"十", "shi"}, {"时", "shi"}, {"实", "shi"}, {"市", "shi"}, {"事", "shi"}, {"是", "shi"}, {"手", "shou"}, {"首", "shou"}, {"水", "shui"}, {"说", "shuo"}, {"四", "si"}, {"思", "si"}, {"所", "suo"},
        {"他", "ta"}, {"她", "ta"}, {"太", "tai"}, {"天", "tian"}, {"田", "tian"}, {"条", "tiao"}, {"通", "tong"}, {"同", "tong"}, {"头", "tou"}, {"土", "tu"},
        {"外", "wai"}, {"万", "wan"}, {"王", "wang"}, {"网", "wang"}, {"为", "wei"}, {"位", "wei"}, {"文", "wen"}, {"我", "wo"}, {"无", "wu"}, {"五", "wu"},
        {"西", "xi"}, {"系", "xi"}, {"下", "xia"}, {"先", "xian"}, {"现", "xian"}, {"线", "xian"}, {"相", "xiang"}, {"想", "xiang"}, {"向", "xiang"}, {"小", "xiao"}, {"新", "xin"}, {"信", "xin"}, {"行", "xing"}, {"形", "xing"}, {"学", "xue"},
        {"要", "yao"}, {"也", "ye"}, {"业", "ye"}, {"一", "yi"}, {"以", "yi"}, {"用", "yong"}, {"有", "you"}, {"又", "you"}, {"于", "yu"}, {"与", "yu"}, {"元", "yuan"}, {"原", "yuan"}, {"月", "yue"}, {"云", "yun"}, {"运", "yun"},
        {"在", "zai"}, {"早", "zao"}, {"增", "zeng"}, {"展", "zhan"}, {"站", "zhan"}, {"张", "zhang"}, {"长", "zhang"}, {"找", "zhao"}, {"这", "zhe"}, {"真", "zhen"}, {"正", "zheng"}, {"政", "zheng"}, {"之", "zhi"}, {"知", "zhi"}, {"直", "zhi"}, {"制", "zhi"}, {"中", "zhong"}, {"重", "zhong"}, {"主", "zhu"}, {"住", "zhu"}, {"专", "zhuan"}, {"转", "zhuan"}, {"资", "zi"}, {"自", "zi"}, {"总", "zong"}, {"组", "zu"}, {"作", "zuo"}
    };

    /**
     * 生成租户编码
     *
     * @param tenantName 租户名称
     * @param companyName 企业名称
     * @return 租户编码
     */
    public String generate(String tenantName, String companyName) {
        // 优先使用租户名称，如果为空则使用企业名称
        String baseName = tenantName != null ? tenantName : companyName;
        
        // 生成基础编码
        String baseCode = generateBaseCode(baseName);
        
        // 确保编码唯一
        return ensureUnique(baseCode);
    }

    /**
     * 生成基础编码
     */
    private String generateBaseCode(String name) {
        if (name == null || name.trim().isEmpty()) {
            return generateRandomCode();
        }

        // 移除特殊字符和空格
        String cleanName = name.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "");
        
        if (cleanName.isEmpty()) {
            return generateRandomCode();
        }

        // 转换为拼音或保留英文
        String pinyin = convertToPinyin(cleanName);
        
        // 限制长度并添加随机后缀
        if (pinyin.length() > 8) {
            pinyin = pinyin.substring(0, 8);
        }
        
        // 添加随机数字后缀
        String suffix = String.format("%03d", random.nextInt(1000));
        
        return pinyin + suffix;
    }

    /**
     * 转换中文为拼音
     */
    private String convertToPinyin(String text) {
        StringBuilder result = new StringBuilder();
        
        for (char c : text.toCharArray()) {
            String ch = String.valueOf(c);
            
            // 如果是英文字母或数字，直接添加
            if (Character.isLetterOrDigit(c) && c < 128) {
                result.append(Character.toLowerCase(c));
                continue;
            }
            
            // 查找中文字符对应的拼音
            boolean found = false;
            for (String[] mapping : PINYIN_MAP) {
                if (mapping[0].equals(ch)) {
                    result.append(mapping[1]);
                    found = true;
                    break;
                }
            }
            
            // 如果没有找到映射，使用字符的Unicode值
            if (!found) {
                if (Character.isLetter(c)) {
                    // 对于其他字符，使用简化处理
                    String normalized = Normalizer.normalize(ch, Normalizer.Form.NFD);
                    String ascii = normalized.replaceAll("[^\\p{ASCII}]", "");
                    if (!ascii.isEmpty()) {
                        result.append(ascii.toLowerCase());
                    } else {
                        result.append("x"); // 默认字符
                    }
                }
            }
        }
        
        return result.toString();
    }

    /**
     * 生成随机编码
     */
    private String generateRandomCode() {
        String chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder code = new StringBuilder();
        
        // 生成6位随机字符
        for (int i = 0; i < 6; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        // 添加3位随机数字
        code.append(String.format("%03d", random.nextInt(1000)));
        
        return code.toString();
    }

    /**
     * 确保编码唯一
     */
    private String ensureUnique(String baseCode) {
        String code = baseCode;
        int attempt = 0;
        
        while (tenantMapper.existsByTenantCode(code) && attempt < 100) {
            attempt++;
            // 如果编码已存在，添加递增的数字后缀
            code = baseCode + String.format("%02d", attempt);
        }
        
        if (attempt >= 100) {
            // 如果尝试100次仍然重复，使用UUID
            code = "tenant_" + java.util.UUID.randomUUID().toString().substring(0, 8);
        }
        
        return code;
    }

    /**
     * 验证租户编码格式
     */
    public boolean isValidTenantCode(String tenantCode) {
        if (tenantCode == null || tenantCode.trim().isEmpty()) {
            return false;
        }
        
        // 租户编码只能包含字母、数字、下划线和横线，长度3-50
        Pattern pattern = Pattern.compile("^[a-zA-Z0-9_-]{3,50}$");
        return pattern.matcher(tenantCode).matches();
    }

    /**
     * 建议租户编码
     */
    public String suggestTenantCode(String tenantName, String companyName) {
        String suggested = generate(tenantName, companyName);
        
        // 如果建议的编码已存在，提供几个备选方案
        if (tenantMapper.existsByTenantCode(suggested)) {
            return generateBaseCode(tenantName != null ? tenantName : companyName);
        }
        
        return suggested;
    }
}
