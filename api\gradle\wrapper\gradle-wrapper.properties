distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
# 使用腾讯云镜像加速Gradle下载
distributionUrl=https\://mirrors.cloud.tencent.com/gradle/gradle-8.13-bin.zip
# 备用：阿里云镜像
# distributionUrl=https\://mirrors.aliyun.com/macports/distfiles/gradle/gradle-8.13-bin.zip
# 备用：华为云镜像
# distributionUrl=https\://repo.huaweicloud.com/gradle/gradle-8.13-bin.zip
# 官方地址（备用）
# distributionUrl=https\://services.gradle.org/distributions/gradle-8.13-bin.zip
networkTimeout=60000
validateDistributionUrl=true
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists
