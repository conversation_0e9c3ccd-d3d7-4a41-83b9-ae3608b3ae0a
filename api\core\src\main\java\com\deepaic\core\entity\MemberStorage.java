package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 会员寄存表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_storage")
public class MemberStorage extends BaseEntity {

    private Long memberId;

    private Long storeId;

    private Long productId;

    private String productName;

    /**
     * 数量
     */
    private Short quantity;

    /**
     * 来源 1=自购，2=赠送
     */
    private Short sourceType;

    /**
     * 单位
     */
    private String unit;

    /**
     * 1=已用完，2=未用完
     */
    private Short status;
}
