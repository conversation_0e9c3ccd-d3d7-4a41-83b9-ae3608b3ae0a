package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 充值套餐
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_recharge_package_project")
public class RechargePackageProject extends BaseEntity {

    private Long rechargeId;

    private Long rechargePackageId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private BigDecimal projectName;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Short quantity;

    /**
     * 总价
     */
    private BigDecimal totlePrice;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 折扣总价
     */
    private BigDecimal discountTotalPrice;
}
