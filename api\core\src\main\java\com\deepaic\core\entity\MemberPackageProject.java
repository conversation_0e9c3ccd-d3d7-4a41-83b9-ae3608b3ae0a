package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 会员权益-套餐-项目
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_member_package_project")
public class MemberPackageProject extends BaseEntity {

    private String sysMemberPackageId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private BigDecimal projectName;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Short quantity;

    /**
     * 总价
     */
    private BigDecimal totlePrice;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 折扣总价
     */
    private BigDecimal discountTotalPrice;

    /**
     * 1=未用完，2=已用完
     */
    private Short status;
}
