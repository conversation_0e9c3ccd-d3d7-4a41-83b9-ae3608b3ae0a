package com.deepaic.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import java.nio.charset.StandardCharsets;

/**
 * 数据库初始化器
 * 在应用启动时执行数据库初始化脚本
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DatabaseInitializer implements ApplicationRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("=== 开始数据库初始化 ===");
        
        try {
            // 检查是否需要初始化
            if (needsInitialization()) {
                log.info("检测到数据库需要初始化，开始执行初始化脚本...");
                executeInitializationScripts();
                log.info("数据库初始化完成！");
            } else {
                log.info("数据库已初始化，跳过初始化脚本");
            }
        } catch (Exception e) {
            log.error("数据库初始化失败: {}", e.getMessage(), e);
            throw e;
        }
        
        log.info("=== 数据库初始化检查完成 ===");
    }

    /**
     * 检查是否需要初始化数据库
     */
    private boolean needsInitialization() {
        try {
            // 检查pub_tenant表是否存在
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'pub_tenant'";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
            return count == null || count == 0;
        } catch (Exception e) {
            log.warn("检查数据库初始化状态时出错: {}", e.getMessage());
            return true; // 出错时假设需要初始化
        }
    }

    /**
     * 执行初始化脚本
     */
    private void executeInitializationScripts() {
        String[] scriptPaths = {
            "sql/init/01_create_schemas.sql",
            "sql/init/02_create_extensions.sql", 
            "sql/init/03_create_tables.sql"
        };

        for (String scriptPath : scriptPaths) {
            executeScript(scriptPath);
        }
    }

    /**
     * 执行单个SQL脚本
     */
    private void executeScript(String scriptPath) {
        try {
            log.info("执行脚本: {}", scriptPath);
            
            ClassPathResource resource = new ClassPathResource(scriptPath);
            if (!resource.exists()) {
                log.warn("脚本文件不存在: {}", scriptPath);
                return;
            }
            
            String sql = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            
            // 分割SQL语句（以分号分割）
            String[] statements = sql.split(";");
            
            for (String statement : statements) {
                String trimmedStatement = statement.trim();
                if (!trimmedStatement.isEmpty() && !trimmedStatement.startsWith("--")) {
                    try {
                        jdbcTemplate.execute(trimmedStatement);
                    } catch (Exception e) {
                        // 某些语句可能因为已存在而失败，这是正常的
                        if (!isIgnorableError(e.getMessage())) {
                            log.warn("执行SQL语句时出现警告: {} - SQL: {}", e.getMessage(), trimmedStatement.substring(0, Math.min(100, trimmedStatement.length())));
                        }
                    }
                }
            }
            
            log.info("脚本执行完成: {}", scriptPath);
            
        } catch (Exception e) {
            log.error("执行脚本 {} 时出错: {}", scriptPath, e.getMessage(), e);
            throw new RuntimeException("数据库初始化脚本执行失败: " + scriptPath, e);
        }
    }

    /**
     * 判断是否为可忽略的错误
     */
    private boolean isIgnorableError(String errorMessage) {
        if (errorMessage == null) {
            return false;
        }
        
        String lowerMessage = errorMessage.toLowerCase();
        return lowerMessage.contains("already exists") ||
               lowerMessage.contains("duplicate key") ||
               lowerMessage.contains("relation") && lowerMessage.contains("already exists");
    }

    /**
     * 创建演示租户
     */
    public void createDemoTenant() {
        try {
            log.info("创建演示租户...");
            
            // 检查演示租户是否已存在
            String checkSql = "SELECT COUNT(*) FROM pub_tenant WHERE tenant_code = 'demo_tenant'";
            Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class);
            
            if (count != null && count > 0) {
                log.info("演示租户已存在，跳过创建");
                return;
            }
            
            // 创建演示租户
            String insertTenantSql = """
                INSERT INTO pub_tenant (
                    id, tenant_code, tenant_name, schema_name, tenant_type, status,
                    contact_name, contact_phone, contact_email, max_users, max_storage,
                    created_at, updated_at, deleted
                ) VALUES (
                    1, 'demo_tenant', '演示租户', 'tenant_demo', 1, 1,
                    '演示联系人', '***********', '<EMAIL>', 100, **********,
                    NOW(), NOW(), false
                )
            """;
            
            jdbcTemplate.update(insertTenantSql);
            
            // 创建演示管理员账户
            String insertAccountSql = """
                INSERT INTO pub_customer_account (
                    id, tenant_code, login_type, login_identifier, username, password,
                    real_name, account_type, status, login_fail_count, bind_time,
                    is_primary, created_at, updated_at, deleted
                ) VALUES (
                    1, 'demo_tenant', 1, 'admin', 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9P8gS6NJjd68Ni.',
                    '系统管理员', 1, 1, 0, NOW(),
                    true, NOW(), NOW(), false
                )
            """;
            
            jdbcTemplate.update(insertAccountSql);
            
            log.info("演示租户创建完成");
            
        } catch (Exception e) {
            log.error("创建演示租户失败: {}", e.getMessage(), e);
        }
    }
}
