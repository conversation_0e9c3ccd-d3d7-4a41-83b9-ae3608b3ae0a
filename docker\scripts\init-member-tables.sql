-- 会员管理相关表初始化脚本
-- 这些表需要在每个租户的schema中创建

-- 会员等级表
CREATE TABLE IF NOT EXISTS sys_member_level (
    id BIGSERIAL PRIMARY KEY,
    level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
    level_code VARCHAR(50) NOT NULL COMMENT '等级编码',
    level_order INTEGER DEFAULT 0 COMMENT '等级序号',
    required_points INTEGER DEFAULT 0 COMMENT '升级所需积分',
    required_amount BIGINT DEFAULT 0 COMMENT '升级所需消费金额(分)',
    discount_rate DECIMAL(5,4) DEFAULT 1.0000 COMMENT '折扣率',
    points_rate DECIMAL(5,4) DEFAULT 1.0000 COMMENT '积分倍率',
    icon VARCHAR(500) COMMENT '等级图标',
    color VARCHAR(20) COMMENT '等级颜色',
    description VARCHAR(200) COMMENT '等级描述',
    benefits VARCHAR(1000) COMMENT '等级权益说明',
    is_default INTEGER DEFAULT 0 COMMENT '是否默认等级(0:否 1:是)',
    status INTEGER DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    sort_order INTEGER DEFAULT 0 COMMENT '排序号',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建会员等级表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_member_level_code ON sys_member_level(level_code) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_sys_member_level_order ON sys_member_level(level_order);
CREATE INDEX IF NOT EXISTS idx_sys_member_level_status ON sys_member_level(status);
CREATE INDEX IF NOT EXISTS idx_sys_member_level_deleted ON sys_member_level(deleted);

-- 会员表
CREATE TABLE IF NOT EXISTS sys_member (
    id BIGSERIAL PRIMARY KEY,
    member_no VARCHAR(50) NOT NULL COMMENT '会员编号',
    wx_openid VARCHAR(100) COMMENT '微信OpenID',
    wx_unionid VARCHAR(100) COMMENT '微信UnionID',
    phone VARCHAR(20) COMMENT '手机号',
    nickname VARCHAR(50) COMMENT '会员昵称',
    real_name VARCHAR(50) COMMENT '真实姓名',
    gender INTEGER DEFAULT 0 COMMENT '性别(0:未知 1:男 2:女)',
    birthday DATE COMMENT '生日',
    avatar VARCHAR(500) COMMENT '头像URL',
    level_id BIGINT COMMENT '会员等级ID',
    level_name VARCHAR(50) COMMENT '会员等级名称',
    points INTEGER DEFAULT 0 COMMENT '积分余额',
    balance BIGINT DEFAULT 0 COMMENT '余额(分)',
    status INTEGER DEFAULT 1 COMMENT '会员状态(0:禁用 1:正常 2:冻结)',
    register_source INTEGER DEFAULT 1 COMMENT '注册来源(1:微信小程序 2:H5 3:APP 4:其他)',
    referrer_id BIGINT COMMENT '推荐人会员ID',
    referrer_no VARCHAR(50) COMMENT '推荐人会员编号',
    last_login_time TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建会员表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_member_no ON sys_member(member_no) WHERE deleted = 0;
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_member_wx_openid ON sys_member(wx_openid) WHERE deleted = 0 AND wx_openid IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_member_wx_unionid ON sys_member(wx_unionid) WHERE deleted = 0 AND wx_unionid IS NOT NULL;
CREATE UNIQUE INDEX IF NOT EXISTS uk_sys_member_phone ON sys_member(phone) WHERE deleted = 0 AND phone IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_sys_member_level_id ON sys_member(level_id);
CREATE INDEX IF NOT EXISTS idx_sys_member_status ON sys_member(status);
CREATE INDEX IF NOT EXISTS idx_sys_member_register_source ON sys_member(register_source);
CREATE INDEX IF NOT EXISTS idx_sys_member_referrer_id ON sys_member(referrer_id);
CREATE INDEX IF NOT EXISTS idx_sys_member_create_time ON sys_member(create_time);
CREATE INDEX IF NOT EXISTS idx_sys_member_deleted ON sys_member(deleted);

-- 会员登录记录表
CREATE TABLE IF NOT EXISTS sys_member_login_log (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT COMMENT '会员ID',
    member_no VARCHAR(50) COMMENT '会员编号',
    login_type INTEGER DEFAULT 1 COMMENT '登录方式(1:微信授权 2:手机号 3:账号密码)',
    platform INTEGER DEFAULT 1 COMMENT '登录平台(1:微信小程序 2:H5 3:APP)',
    login_ip VARCHAR(50) COMMENT '登录IP',
    login_address VARCHAR(200) COMMENT '登录地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    login_status INTEGER DEFAULT 1 COMMENT '登录状态(0:失败 1:成功)',
    fail_reason VARCHAR(200) COMMENT '失败原因',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建会员登录记录表索引
CREATE INDEX IF NOT EXISTS idx_sys_member_login_log_member_id ON sys_member_login_log(member_id);
CREATE INDEX IF NOT EXISTS idx_sys_member_login_log_login_time ON sys_member_login_log(login_time);
CREATE INDEX IF NOT EXISTS idx_sys_member_login_log_login_status ON sys_member_login_log(login_status);
CREATE INDEX IF NOT EXISTS idx_sys_member_login_log_platform ON sys_member_login_log(platform);
CREATE INDEX IF NOT EXISTS idx_sys_member_login_log_deleted ON sys_member_login_log(deleted);

-- 插入默认会员等级数据
INSERT INTO sys_member_level (level_name, level_code, level_order, required_points, required_amount, discount_rate, points_rate, description, is_default, status, sort_order) VALUES
('普通会员', 'NORMAL', 1, 0, 0, 1.0000, 1.0000, '普通会员，享受基础服务', 1, 1, 1),
('银卡会员', 'SILVER', 2, 1000, 100000, 0.9500, 1.2000, '银卡会员，享受95折优惠和1.2倍积分', 0, 1, 2),
('金卡会员', 'GOLD', 3, 5000, 500000, 0.9000, 1.5000, '金卡会员，享受9折优惠和1.5倍积分', 0, 1, 3),
('钻石会员', 'DIAMOND', 4, 20000, 2000000, 0.8500, 2.0000, '钻石会员，享受85折优惠和2倍积分', 0, 1, 4),
('至尊会员', 'VIP', 5, 50000, 5000000, 0.8000, 2.5000, '至尊会员，享受8折优惠和2.5倍积分', 0, 1, 5)
ON CONFLICT DO NOTHING;

-- 为会员表添加外键约束（可选）
-- ALTER TABLE sys_member ADD CONSTRAINT fk_sys_member_level_id FOREIGN KEY (level_id) REFERENCES sys_member_level(id);
-- ALTER TABLE sys_member ADD CONSTRAINT fk_sys_member_referrer_id FOREIGN KEY (referrer_id) REFERENCES sys_member(id);

-- 创建会员相关的触发器（自动更新update_time）
CREATE OR REPLACE FUNCTION update_member_updated_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为会员表创建触发器
DROP TRIGGER IF EXISTS trigger_update_member_time ON sys_member;
CREATE TRIGGER trigger_update_member_time
    BEFORE UPDATE ON sys_member
    FOR EACH ROW
    EXECUTE FUNCTION update_member_updated_time();

-- 为会员等级表创建触发器
DROP TRIGGER IF EXISTS trigger_update_member_level_time ON sys_member_level;
CREATE TRIGGER trigger_update_member_level_time
    BEFORE UPDATE ON sys_member_level
    FOR EACH ROW
    EXECUTE FUNCTION update_member_updated_time();
