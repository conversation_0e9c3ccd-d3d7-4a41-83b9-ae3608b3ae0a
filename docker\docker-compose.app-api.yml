# 美姿姿 - App API 服务配置
# 移动应用API服务，提供移动端业务功能
# 适用于部署到专门的应用服务器
version: '3.8'

services:
  # App API服务
  app-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile.app-api
    image: beautiful-posture/app-api:${VERSION:-latest}
    container_name: beautiful-posture-app-api
    ports:
      - "${APP_API_PORT:-8081}:8081"
    environment:
      # Spring配置
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES:-production}
      SERVER_PORT: 8081
      
      # 数据库配置 - 连接到远程数据库服务器
      SPRING_DATASOURCE_URL: ${DATABASE_URL:-********************************************************}
      SPRING_DATASOURCE_USERNAME: ${DATABASE_USERNAME:-postgres}
      SPRING_DATASOURCE_PASSWORD: ${DATABASE_PASSWORD:-postgres123}
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: org.postgresql.Driver
      
      # 连接池配置 - App API通常需要更多连接
      SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE: ${DB_POOL_SIZE:-30}
      SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE: ${DB_MIN_IDLE:-10}
      SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT: 30000
      SPRING_DATASOURCE_HIKARI_IDLE_TIMEOUT: 600000
      SPRING_DATASOURCE_HIKARI_MAX_LIFETIME: 1800000
      
      # Redis配置 - 连接到远程Redis服务器
      SPRING_REDIS_HOST: ${REDIS_HOST:-redis-server}
      SPRING_REDIS_PORT: ${REDIS_PORT:-6379}
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      SPRING_REDIS_DATABASE: ${REDIS_DATABASE:-1}  # 使用不同的数据库
      SPRING_REDIS_TIMEOUT: 5000ms
      
      # Redis连接池配置
      SPRING_REDIS_LETTUCE_POOL_MAX_ACTIVE: 30
      SPRING_REDIS_LETTUCE_POOL_MAX_IDLE: 15
      SPRING_REDIS_LETTUCE_POOL_MIN_IDLE: 8
      SPRING_REDIS_LETTUCE_POOL_MAX_WAIT: -1ms
      
      # JVM配置 - App API通常需要更多内存
      JAVA_OPTS: ${JAVA_OPTS:--Xms1024m -Xmx2048m -XX:+UseG1GC -XX:+UseStringDeduplication}
      
      # 应用配置
      APP_NAME: beautiful-posture-app-api
      APP_VERSION: ${VERSION:-1.0.0}
      
      # 日志配置
      LOGGING_LEVEL_ROOT: ${LOG_LEVEL:-INFO}
      LOGGING_LEVEL_COM_DEEPAIC: ${APP_LOG_LEVEL:-DEBUG}
      LOGGING_FILE_NAME: /app/logs/app-api.log
      
      # 监控配置
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,info,metrics,prometheus
      MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: when_authorized
      MANAGEMENT_METRICS_EXPORT_PROMETHEUS_ENABLED: true
      
      # 安全配置
      SECURITY_JWT_SECRET: ${JWT_SECRET:-your-secret-key-change-in-production}
      SECURITY_JWT_EXPIRATION: ${JWT_EXPIRATION:-86400}
      
      # 多租户配置
      TENANT_DEFAULT_SCHEMA: ${DEFAULT_TENANT_SCHEMA:-public}
      TENANT_RESOLVER_TYPE: ${TENANT_RESOLVER:-header}
      
      # 文件上传配置 - App通常需要处理更多文件
      SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE: ${MAX_FILE_SIZE:-50MB}
      SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE: ${MAX_REQUEST_SIZE:-100MB}
      
      # 移动端特定配置
      APP_MOBILE_PUSH_ENABLED: ${PUSH_ENABLED:-true}
      APP_MOBILE_PUSH_PROVIDER: ${PUSH_PROVIDER:-firebase}
      APP_MOBILE_OFFLINE_SYNC_ENABLED: ${OFFLINE_SYNC:-true}
      
    volumes:
      # 日志目录
      - app_api_logs:/app/logs
      # 配置文件目录（可选）
      - ./config/app-api:/app/config
      # 临时文件目录
      - app_api_temp:/tmp
      # 文件上传目录
      - app_api_uploads:/app/uploads
      
    networks:
      - beautiful-posture-network
      
    restart: unless-stopped
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
      
    # 资源限制 - App API通常需要更多资源
    deploy:
      resources:
        limits:
          memory: 2.5G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.8'
          
    # 依赖外部服务（通过网络连接）
    external_links:
      - postgres-server:postgres
      - redis-server:redis
      
    # 标签
    labels:
      - "com.beautiful-posture.service=app-api"
      - "com.beautiful-posture.version=${VERSION:-latest}"
      - "com.beautiful-posture.environment=${ENVIRONMENT:-production}"

volumes:
  app_api_logs:
    driver: local
  app_api_temp:
    driver: local
  app_api_uploads:
    driver: local

networks:
  beautiful-posture-network:
    external: true  # 使用外部网络，与基础设施共享
