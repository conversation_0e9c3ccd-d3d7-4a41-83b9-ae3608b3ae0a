# 应用层数据完整性维护

## 概述

由于数据库设计中不使用外键约束，所有的数据完整性和一致性都需要在应用层进行维护。本文档提供了具体的实现方案和代码示例。

## 核心原则

### 1. 事务管理
- 使用数据库事务确保操作的原子性
- 复杂操作必须在同一个事务中完成
- 异常时自动回滚所有相关操作

### 2. 数据验证
- 在业务逻辑层进行完整的数据验证
- 验证关联数据的存在性
- 检查业务规则的合法性

### 3. 级联操作
- 删除操作需要处理所有相关数据
- 更新操作需要同步相关字段
- 批量操作需要特别注意数据一致性

## 实现示例

### 1. 用户管理服务

```java
@Service
@Transactional
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Autowired
    private UserOrganizationMapper userOrganizationMapper;
    
    @Autowired
    private UserProfileMapper userProfileMapper;
    
    @Autowired
    private TenantService tenantService;
    
    @Autowired
    private OrganizationService organizationService;
    
    /**
     * 创建用户 - 验证关联数据存在性
     */
    public Long createUser(CreateUserRequest request) {
        // 1. 验证租户存在
        validateTenantExists(request.getTenantCode());
        
        // 2. 验证组织存在（如果指定）
        if (request.getOrgIds() != null && !request.getOrgIds().isEmpty()) {
            validateOrganizationsExist(request.getOrgIds());
        }
        
        // 3. 验证角色存在（如果指定）
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            validateRolesExist(request.getRoleIds());
        }
        
        // 4. 创建用户
        SysUser user = buildUser(request);
        userMapper.insert(user);
        
        // 5. 创建用户组织关联
        if (request.getOrgIds() != null && !request.getOrgIds().isEmpty()) {
            createUserOrganizations(user.getId(), request.getOrgIds(), request.getPrimaryOrgId());
        }
        
        // 6. 创建用户角色关联
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            createUserRoles(user.getId(), request.getRoleIds());
        }
        
        // 7. 创建用户扩展信息
        if (request.getProfile() != null) {
            createUserProfile(user.getId(), request.getProfile());
        }
        
        return user.getId();
    }
    
    /**
     * 删除用户 - 级联删除相关数据
     */
    public void deleteUser(Long userId) {
        // 1. 验证用户存在
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 2. 删除用户角色关联
        userRoleMapper.deleteByUserId(userId);
        
        // 3. 删除用户组织关联
        userOrganizationMapper.deleteByUserId(userId);
        
        // 4. 删除用户扩展信息
        userProfileMapper.deleteByUserId(userId);
        
        // 5. 检查是否有其他关联数据
        checkUserReferences(userId);
        
        // 6. 删除用户
        userMapper.deleteById(userId);
    }
    
    /**
     * 更新用户组织 - 保持数据一致性
     */
    public void updateUserOrganizations(Long userId, List<Long> orgIds, Long primaryOrgId) {
        // 1. 验证用户存在
        validateUserExists(userId);
        
        // 2. 验证组织存在
        validateOrganizationsExist(orgIds);
        
        // 3. 验证主组织在列表中
        if (primaryOrgId != null && !orgIds.contains(primaryOrgId)) {
            throw new BusinessException("主组织必须在组织列表中");
        }
        
        // 4. 删除现有关联
        userOrganizationMapper.deleteByUserId(userId);
        
        // 5. 创建新关联
        createUserOrganizations(userId, orgIds, primaryOrgId);
    }
    
    // 私有方法
    private void validateTenantExists(String tenantCode) {
        if (!tenantService.existsByCode(tenantCode)) {
            throw new BusinessException("租户不存在: " + tenantCode);
        }
    }
    
    private void validateOrganizationsExist(List<Long> orgIds) {
        List<Long> existingOrgIds = organizationService.getExistingIds(orgIds);
        if (existingOrgIds.size() != orgIds.size()) {
            List<Long> missingIds = new ArrayList<>(orgIds);
            missingIds.removeAll(existingOrgIds);
            throw new BusinessException("组织不存在: " + missingIds);
        }
    }
    
    private void createUserOrganizations(Long userId, List<Long> orgIds, Long primaryOrgId) {
        for (Long orgId : orgIds) {
            SysUserOrganization userOrg = new SysUserOrganization();
            userOrg.setUserId(userId);
            userOrg.setOrgId(orgId);
            userOrg.setIsPrimary(Objects.equals(orgId, primaryOrgId));
            userOrganizationMapper.insert(userOrg);
        }
    }
    
    private void checkUserReferences(Long userId) {
        // 检查是否有其他表引用此用户
        // 例如：检查是否是某个组织的负责人
        List<SysOrganization> leaderOrgs = organizationService.getByLeaderId(userId);
        if (!leaderOrgs.isEmpty()) {
            throw new BusinessException("用户是组织负责人，无法删除");
        }
    }
}
```

### 2. 组织管理服务

```java
@Service
@Transactional
public class OrganizationService {
    
    @Autowired
    private OrganizationMapper organizationMapper;
    
    @Autowired
    private UserOrganizationMapper userOrganizationMapper;
    
    /**
     * 删除组织 - 处理层级关系和用户关联
     */
    public void deleteOrganization(Long orgId) {
        // 1. 验证组织存在
        SysOrganization org = organizationMapper.selectById(orgId);
        if (org == null) {
            throw new BusinessException("组织不存在");
        }
        
        // 2. 检查是否有子组织
        List<SysOrganization> children = organizationMapper.selectByParentId(orgId);
        if (!children.isEmpty()) {
            throw new BusinessException("存在子组织，无法删除");
        }
        
        // 3. 检查是否有用户关联
        int userCount = userOrganizationMapper.countByOrgId(orgId);
        if (userCount > 0) {
            throw new BusinessException("组织下有用户，无法删除");
        }
        
        // 4. 删除组织
        organizationMapper.deleteById(orgId);
    }
    
    /**
     * 移动组织 - 更新层级关系
     */
    public void moveOrganization(Long orgId, Long newParentId) {
        // 1. 验证组织存在
        SysOrganization org = organizationMapper.selectById(orgId);
        if (org == null) {
            throw new BusinessException("组织不存在");
        }
        
        // 2. 验证新父组织存在
        if (newParentId != null && newParentId != 0) {
            SysOrganization parentOrg = organizationMapper.selectById(newParentId);
            if (parentOrg == null) {
                throw new BusinessException("父组织不存在");
            }
            
            // 3. 检查不能移动到自己的子组织下
            if (isDescendant(newParentId, orgId)) {
                throw new BusinessException("不能移动到子组织下");
            }
        }
        
        // 4. 更新组织层级
        updateOrganizationHierarchy(orgId, newParentId);
    }
    
    private boolean isDescendant(Long ancestorId, Long descendantId) {
        // 检查 ancestorId 是否是 descendantId 的后代
        SysOrganization org = organizationMapper.selectById(ancestorId);
        return org != null && org.getAncestors().contains("," + descendantId + ",");
    }
    
    private void updateOrganizationHierarchy(Long orgId, Long newParentId) {
        // 更新组织的父级关系和祖先路径
        // 这里需要递归更新所有子组织的祖先路径
        // 具体实现略...
    }
}
```

### 3. 数据一致性检查工具

```java
@Component
public class DataConsistencyChecker {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Autowired
    private RoleMapper roleMapper;
    
    /**
     * 检查用户角色关联的一致性
     */
    public ConsistencyReport checkUserRoleConsistency() {
        ConsistencyReport report = new ConsistencyReport();
        
        // 1. 检查孤立的用户角色关联
        List<SysUserRole> orphanUserRoles = userRoleMapper.findOrphanUserRoles();
        report.addIssue("孤立的用户角色关联", orphanUserRoles.size());
        
        // 2. 检查引用不存在用户的角色关联
        List<SysUserRole> invalidUserRoles = userRoleMapper.findInvalidUserReferences();
        report.addIssue("引用不存在用户的角色关联", invalidUserRoles.size());
        
        // 3. 检查引用不存在角色的用户关联
        List<SysUserRole> invalidRoleRefs = userRoleMapper.findInvalidRoleReferences();
        report.addIssue("引用不存在角色的用户关联", invalidRoleRefs.size());
        
        return report;
    }
    
    /**
     * 修复数据一致性问题
     */
    @Transactional
    public void fixDataConsistency() {
        // 1. 删除孤立的用户角色关联
        userRoleMapper.deleteOrphanUserRoles();
        
        // 2. 删除引用不存在数据的关联
        userRoleMapper.deleteInvalidReferences();
        
        // 记录修复日志
        log.info("数据一致性修复完成");
    }
}
```

### 4. 批量操作示例

```java
@Service
@Transactional
public class BatchOperationService {
    
    /**
     * 批量删除用户
     */
    public void batchDeleteUsers(List<Long> userIds) {
        // 1. 验证所有用户存在
        List<SysUser> users = userMapper.selectBatchIds(userIds);
        if (users.size() != userIds.size()) {
            throw new BusinessException("部分用户不存在");
        }
        
        // 2. 检查业务约束
        for (Long userId : userIds) {
            checkUserCanBeDeleted(userId);
        }
        
        // 3. 批量删除关联数据
        userRoleMapper.deleteBatchByUserIds(userIds);
        userOrganizationMapper.deleteBatchByUserIds(userIds);
        userProfileMapper.deleteBatchByUserIds(userIds);
        
        // 4. 批量删除用户
        userMapper.deleteBatchIds(userIds);
    }
    
    private void checkUserCanBeDeleted(Long userId) {
        // 检查用户是否可以被删除
        // 例如：检查是否是组织负责人等
    }
}
```

## 最佳实践

### 1. 异常处理
- 使用统一的业务异常类
- 提供清晰的错误信息
- 记录详细的操作日志

### 2. 性能优化
- 批量操作时使用批量SQL
- 合理使用缓存减少数据库查询
- 对频繁查询的关联字段建立索引

### 3. 监控告警
- 定期执行数据一致性检查
- 监控关键业务操作的成功率
- 设置数据异常告警机制

### 4. 测试策略
- 编写完整的单元测试
- 测试各种异常场景
- 验证事务回滚机制

## 总结

通过在应用层实现完整的数据验证和一致性维护机制，可以在不使用外键约束的情况下，确保数据的完整性和一致性。这种设计提供了更高的灵活性，但也要求开发团队严格遵循数据操作规范。
