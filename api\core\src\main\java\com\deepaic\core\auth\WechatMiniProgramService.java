package com.deepaic.core.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 微信小程序服务
 * 处理微信小程序相关的API调用
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WechatMiniProgramService {

    private final RestTemplate restTemplate;

    @Value("${wechat.mini.appid:}")
    private String appId;

    @Value("${wechat.mini.secret:}")
    private String appSecret;

    /**
     * 通过授权码获取OpenID和SessionKey
     */
    public WechatAuthResult getOpenidByCode(String code) {
        try {
            String url = String.format(
                "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                appId, appSecret, code
            );

            WechatAuthResponse response = restTemplate.getForObject(url, WechatAuthResponse.class);
            
            if (response == null) {
                log.error("微信授权响应为空");
                return null;
            }

            if (response.getErrcode() != null && response.getErrcode() != 0) {
                log.error("微信授权失败: errcode={}, errmsg={}", response.getErrcode(), response.getErrmsg());
                return null;
            }

            WechatAuthResult result = new WechatAuthResult();
            result.setOpenid(response.getOpenid());
            result.setSessionKey(response.getSessionKey());
            result.setUnionid(response.getUnionid());

            log.info("微信授权成功: openid={}", response.getOpenid());
            return result;

        } catch (Exception e) {
            log.error("微信授权异常: code={}", code, e);
            return null;
        }
    }

    /**
     * 获取微信访问令牌
     */
    public String getAccessToken() {
        try {
            String url = String.format(
                "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s",
                appId, appSecret
            );

            WechatAccessTokenResponse response = restTemplate.getForObject(url, WechatAccessTokenResponse.class);
            
            if (response == null) {
                log.error("获取微信访问令牌响应为空");
                return null;
            }

            if (response.getErrcode() != null && response.getErrcode() != 0) {
                log.error("获取微信访问令牌失败: errcode={}, errmsg={}", response.getErrcode(), response.getErrmsg());
                return null;
            }

            log.info("获取微信访问令牌成功");
            return response.getAccessToken();

        } catch (Exception e) {
            log.error("获取微信访问令牌异常", e);
            return null;
        }
    }

    /**
     * 发送订阅消息
     */
    public boolean sendSubscribeMessage(String openid, String templateId, String page, Object data) {
        try {
            String accessToken = getAccessToken();
            if (accessToken == null) {
                return false;
            }

            String url = String.format(
                "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s",
                accessToken
            );

            WechatSubscribeMessageRequest request = new WechatSubscribeMessageRequest();
            request.setTouser(openid);
            request.setTemplateId(templateId);
            request.setPage(page);
            request.setData(data);

            WechatCommonResponse response = restTemplate.postForObject(url, request, WechatCommonResponse.class);
            
            if (response == null) {
                log.error("发送订阅消息响应为空");
                return false;
            }

            if (response.getErrcode() != null && response.getErrcode() != 0) {
                log.error("发送订阅消息失败: errcode={}, errmsg={}", response.getErrcode(), response.getErrmsg());
                return false;
            }

            log.info("发送订阅消息成功: openid={}, templateId={}", openid, templateId);
            return true;

        } catch (Exception e) {
            log.error("发送订阅消息异常: openid={}, templateId={}", openid, templateId, e);
            return false;
        }
    }

    /**
     * 微信授权结果
     */
    @Data
    public static class WechatAuthResult {
        private String openid;
        private String sessionKey;
        private String unionid;
    }

    /**
     * 微信授权响应
     */
    @Data
    private static class WechatAuthResponse {
        private String openid;
        
        @JsonProperty("session_key")
        private String sessionKey;
        
        private String unionid;
        private Integer errcode;
        private String errmsg;
    }

    /**
     * 微信访问令牌响应
     */
    @Data
    private static class WechatAccessTokenResponse {
        @JsonProperty("access_token")
        private String accessToken;
        
        @JsonProperty("expires_in")
        private Integer expiresIn;
        
        private Integer errcode;
        private String errmsg;
    }

    /**
     * 微信订阅消息请求
     */
    @Data
    private static class WechatSubscribeMessageRequest {
        private String touser;
        
        @JsonProperty("template_id")
        private String templateId;
        
        private String page;
        private Object data;
    }

    /**
     * 微信通用响应
     */
    @Data
    private static class WechatCommonResponse {
        private Integer errcode;
        private String errmsg;
    }
}
