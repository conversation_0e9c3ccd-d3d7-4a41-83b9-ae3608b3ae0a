package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_project_product")
public class ProjectProduct extends BaseEntity {

    private Long productId;

    /**
     * 冗余产品名称
     */
    private String productName;

    /**
     * 每次消耗产品量
     */
    private BigDecimal consumption;
}
