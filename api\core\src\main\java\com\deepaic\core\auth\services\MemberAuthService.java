package com.deepaic.core.auth.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepaic.core.auth.interfaces.AuthenticationService;
import com.deepaic.core.auth.interfaces.TokenService;
import com.deepaic.core.auth.models.AuthRequest;
import com.deepaic.core.auth.models.AuthResponse;
import com.deepaic.core.auth.models.UserPrincipal;
import com.deepaic.core.entity.PubMemberAccount;
import com.deepaic.core.entity.Tenant;
import com.deepaic.core.entity.Member;
import com.deepaic.core.exception.BussinessException;
import com.deepaic.core.mapper.PubMemberAccountMapper;
import com.deepaic.core.mapper.TenantMapper;
import com.deepaic.core.mapper.MemberMapper;
import com.deepaic.core.tenant.TenantContextService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 会员认证服务
 * 处理小程序会员的登录认证
 * 会员用户需要设置租户上下文，切换到对应的租户schema
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberAuthService implements AuthenticationService {

    private final PubMemberAccountMapper pubMemberAccountMapper;
    private final TenantMapper tenantMapper;
    private final MemberMapper memberMapper;
    private final TokenService tokenService;
    private final TenantContextService tenantContextService;

    @Override
    public AuthResponse authenticate(AuthRequest request) throws BussinessException {
        log.info("会员用户认证开始: identifier={}", request.getUsername());
        
        PubMemberAccount account = null;
        switch (request.getAuthenticationType()) {
            case WECHAT_CODE:
                account = authenticateByWechatCode(request);
                break;
            case PHONE_CODE:
                account = authenticateByPhoneCode(request);
                break;
            default:
                throw new BussinessException("会员用户不支持的认证类型: " + request.getAuthenticationType());
        }

        // 检查账户状态
        checkAccountHealth(account);

        // 设置会员用户租户上下文（使用新的方法）
        tenantContextService.setTenantContextForUser(account.getId(), TenantContextService.UserType.MEMBER_USER);
        
        // 获取租户信息
        Tenant tenant = getTenant(account.getTenantCode());

        // 获取关联的会员信息
        Member member = getMember(account);

        // 创建用户主体
        UserPrincipal userPrincipal = createUserPrincipal(account, tenant, member);

        // 生成令牌
        String accessToken = tokenService.generateAccessToken(userPrincipal);
        Long expiresIn = tokenService.getTokenExpiration(accessToken);

        // 更新最后登录信息
        updateLastLoginInfo(account, request.getClientIp());

        log.info("会员用户登录成功: tenantCode={}, memberId={}, accountId={}", 
                account.getTenantCode(), account.getMemberId(), account.getId());

        return AuthResponse.success(accessToken, expiresIn, userPrincipal);
    }

    /**
     * 微信授权码认证
     */
    private PubMemberAccount authenticateByWechatCode(AuthRequest request) throws BussinessException {
        // TODO: 实现微信授权码认证逻辑
        // 1. 通过微信API获取openid
        // 2. 根据openid查询会员账户
        // 3. 如果账户不存在，可以选择自动注册或返回错误
        
        // 临时实现：根据微信openid查询
        String wechatOpenId = request.getWechatCode(); // 这里应该是通过微信API获取的openid
        
        PubMemberAccount account = pubMemberAccountMapper.selectOne(
            new LambdaQueryWrapper<PubMemberAccount>()
                .eq(PubMemberAccount::getWechatOpenId, wechatOpenId)
                .eq(PubMemberAccount::getDeleted, false)
        );
        
        if (account == null) {
            throw new BussinessException("微信账户未绑定，请先注册");
        }

        return account;
    }

    /**
     * 手机号验证码认证
     */
    private PubMemberAccount authenticateByPhoneCode(AuthRequest request) throws BussinessException {
        // TODO: 验证短信验证码
        
        // 根据手机号查询会员账户
        // 注意：这里需要通过租户和手机号查询，因为不同租户可能有相同手机号的会员
        List<PubMemberAccount> accounts = pubMemberAccountMapper.selectList(
            new LambdaQueryWrapper<PubMemberAccount>()
                .eq(PubMemberAccount::getDeleted, false)
        );
        
        // TODO: 这里需要更复杂的逻辑来处理手机号登录
        // 可能需要先让用户选择租户，或者通过其他方式确定租户
        
        throw new BussinessException("手机号登录功能暂未实现");
    }

    /**
     * 获取租户信息
     */
    private Tenant getTenant(String tenantCode) throws BussinessException {
        Tenant tenant = tenantMapper.selectOne(
            new LambdaQueryWrapper<Tenant>()
                .eq(Tenant::getTenantCode, tenantCode)
                .eq(Tenant::getDeleted, false)
        );
        
        if (tenant == null) {
            throw new BussinessException("租户不存在: " + tenantCode);
        }

        if (tenant.getStatus() != 1) {
            throw new BussinessException("租户已被禁用: " + tenantCode);
        }
        
        return tenant;
    }

    /**
     * 获取关联的会员信息
     */
    private Member getMember(PubMemberAccount account) {
        if (account.getMemberId() == null) {
            return null;
        }
        
        return memberMapper.selectById(account.getMemberId());
    }

    /**
     * 检查账户健康状态
     */
    private void checkAccountHealth(PubMemberAccount account) throws BussinessException {
        // PubMemberAccount没有status字段，这里可以根据实际需要添加检查逻辑
        // 比如检查会员是否被禁用等
    }

    /**
     * 更新最后登录信息
     */
    private void updateLastLoginInfo(PubMemberAccount account, String clientIp) {
        account.setLastLoginTime(LocalDateTime.now());
        account.setLastLoginIp(clientIp);
        account.setUpdatedAt(LocalDateTime.now());
        pubMemberAccountMapper.updateById(account);
    }

    /**
     * 创建用户主体
     */
    private UserPrincipal createUserPrincipal(PubMemberAccount account, Tenant tenant, Member member) {
        return UserPrincipal.builder()
                .accountId(account.getId())
                .userId(member != null ? member.getId() : null)
                .userType(UserPrincipal.UserType.MEMBER_USER)
                .username(member != null ? member.getName() : "会员用户")
                .realName(member != null ? member.getName() : null)
                .phone(member != null ? member.getPhone() : null)
                .avatar(member != null && member.getAvatar() != null ? member.getAvatar() : null)
                .tenantCode(tenant.getTenantCode())
                .tenantName(tenant.getTenantName())
                .authorities(buildAuthorities(member))
                .roles(buildRoles(member))
                .status(UserPrincipal.AccountStatus.ACTIVE)
                .lastLoginTime(account.getLastLoginTime())
                .build();
    }

    /**
     * 构建权限列表
     */
    private List<String> buildAuthorities(Member member) {
        List<String> authorities = new ArrayList<>();
        
        if (member != null) {
            // 会员基础权限
            authorities.add("member:basic");
            authorities.add("member:profile:view");
            authorities.add("member:order:view");
            
            // 根据会员状态添加权限
            if (member.getStatus() != null && member.getStatus() == 1) {
                authorities.add("member:consume");
                authorities.add("member:recharge");
            }
        }
        
        return authorities;
    }

    /**
     * 构建角色列表
     */
    private List<String> buildRoles(Member member) {
        List<String> roles = new ArrayList<>();
        
        if (member != null) {
            roles.add("MEMBER");
            
            // 可以根据会员等级添加不同角色
            // if (member.getLevel() == MemberLevel.VIP) {
            //     roles.add("VIP_MEMBER");
            // }
        }
        
        return roles;
    }

    @Override
    public boolean logout(String userId) {
        try {
            // 清除租户上下文
            tenantContextService.clearTenantContext();
            return true;
        } catch (Exception e) {
            log.error("会员用户登出失败: userId={}", userId, e);
            return false;
        }
    }

    @Override
    public AuthResponse refreshToken(String refreshToken) {
        throw new UnsupportedOperationException("会员用户暂不支持刷新令牌");
    }

    @Override
    public UserPrincipal validateToken(String token) {
        return tokenService.validateToken(token);
    }

    @Override
    public UserPrincipal getCurrentUser() {
        return tokenService.getCurrentUser();
    }

    @Override
    public boolean isAuthenticated() {
        return tokenService.isAuthenticated();
    }

    @Override
    public UserPrincipal.UserType getUserType() {
        return UserPrincipal.UserType.MEMBER_USER;
    }
}
