package com.deepaic.core.dto;

import lombok.*;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 用户DTO - 展示Lombok在DTO中的使用
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UserDTO {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    private String username;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 用户状态 - 0:禁用 1:启用
     */
    @Builder.Default
    private Integer status = 1;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 内部静态类 - 展示@Value注解的使用（不可变对象）
     */
    @Value
    @Builder
    public static class UserInfo {
        Long id;
        String username;
        String nickname;
        String email;
    }

    /**
     * 使用@With注解创建不可变对象的副本
     */
    @With
    @Value
    @Builder
    public static class UserProfile {
        Long id;
        String username;
        String nickname;
        String avatarUrl;
        Integer status;
    }
}
