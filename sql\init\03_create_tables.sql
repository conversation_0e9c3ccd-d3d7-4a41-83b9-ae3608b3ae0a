-- =====================================================
-- 美姿姿健康管理系统 - 数据库表创建脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-20
-- 作者: 美姿姿团队
-- 说明: 创建数据库表结构
-- 数据库连接: *****************************************************/mzz
-- =====================================================

-- 确保在正确的数据库中执行
\c mzz

-- 设置客户端编码和时区
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- =====================================================
-- 公共表（pub_前缀）- 存储在public schema中
-- =====================================================

-- public.pub_member_account definition

-- Drop table

-- DROP TABLE pub_member_account;

CREATE TABLE pub_member_account (
	id int8 NOT NULL,
	tenant_code varchar(50) NULL,
	member_id int8 NULL,
	wechat_open_id int4 NULL,
	last_login_time timestamp(6) NULL,
	last_login_ip inet NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	tenant_id int8 NULL,
	CONSTRAINT pub_member_account_pkey PRIMARY KEY (id)
);


-- public.pub_platform_account definition

-- Drop table

-- DROP TABLE pub_platform_account;

CREATE TABLE pub_platform_account (
	id int8 NOT NULL,
	username varchar(50) NOT NULL,
	"password" varchar(100) NOT NULL,
	email varchar(100) NULL,
	phone varchar(20) NULL,
	real_name varchar(50) NULL,
	avatar varchar(500) NULL,
	permission_level int4 DEFAULT 1 NULL,
	status int4 DEFAULT 1 NOT NULL,
	last_login_time timestamp(6) NULL,
	last_login_ip inet NULL,
	login_fail_count int4 DEFAULT 0 NULL,
	lock_time timestamp(6) NULL,
	password_expire_time timestamp(6) NULL,
	remark text NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT pub_platform_account_email_key UNIQUE (email),
	CONSTRAINT pub_platform_account_pkey PRIMARY KEY (id),
	CONSTRAINT pub_platform_account_username_key UNIQUE (username)
);


-- public.pub_tenant definition

-- Drop table

-- DROP TABLE pub_tenant;

CREATE TABLE pub_tenant (
	id int8 NOT NULL,
	tenant_code varchar(50) NOT NULL,
	tenant_name varchar(100) NOT NULL,
	schema_name varchar(100) NOT NULL,
	tenant_type int4 DEFAULT 1 NOT NULL,
	status int4 DEFAULT 1 NOT NULL,
	contact_name varchar(50) NULL,
	contact_phone varchar(20) NULL,
	contact_email varchar(100) NULL,
	address text NULL,
	expire_time timestamp(6) NULL,
	max_users int4 DEFAULT 10 NULL,
	max_storage int8 DEFAULT ********** NULL,
	features jsonb NULL,
	settings jsonb NULL,
	remark text NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT pub_tenant_pkey PRIMARY KEY (id),
	CONSTRAINT pub_tenant_schema_name_key UNIQUE (schema_name),
	CONSTRAINT pub_tenant_tenant_code_key UNIQUE (tenant_code)
);


-- public.pub_user_account definition

-- Drop table

-- DROP TABLE pub_user_account;

CREATE TABLE pub_user_account (
	id int8 NOT NULL,
	tenant_code varchar(50) NOT NULL,
	login_type int4 DEFAULT 1 NOT NULL,
	login_identifier varchar(200) NOT NULL,
	username varchar(50) NULL,
	"password" varchar(100) NULL,
	email varchar(100) NULL,
	phone varchar(20) NULL,
	real_name varchar(50) NULL,
	avatar varchar(500) NULL,
	wechat_openid varchar(100) NULL,
	wechat_unionid varchar(100) NULL,
	account_type int4 DEFAULT 1 NULL,
	status int4 DEFAULT 1 NOT NULL,
	last_login_time timestamp(6) NULL,
	last_login_ip inet NULL,
	login_fail_count int4 DEFAULT 0 NULL,
	lock_time timestamp(6) NULL,
	password_expire_time timestamp(6) NULL,
	bind_time timestamp(6) DEFAULT now() NULL,
	sys_user_id int8 NULL,
	is_primary bool DEFAULT true NULL,
	remark text NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT pub_customer_account_pkey PRIMARY KEY (id)
);

-- =====================================================
-- 租户表（sys_前缀）- 存储在各租户schema中
-- 注意：这些表需要在每个租户的schema中创建
-- 可以使用 create_tenant_tables(schema_name) 函数批量创建
-- =====================================================

-- public.sys_coupon definition

-- Drop table

-- DROP TABLE sys_coupon;

CREATE TABLE sys_coupon (
	id int8 NOT NULL,
	code varchar(20) NULL,
	"name" varchar(50) NULL,
	"type" int2 NULL,
	face_price numeric(10, 2) NULL,
	stock int4 NULL,
	sale_start_date date NULL,
	sale_end_date date NULL,
	valid_start_date date NULL,
	valid_end_date date NULL,
	is_single_use_limit bool NULL,
	single_use_limit int4 NULL,
	is_usage_threshold bool NULL,
	usage_threshold numeric(10, 2) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_coupon_pkey PRIMARY KEY (id)
);


-- public.sys_dictionary definition

-- Drop table

-- DROP TABLE sys_dictionary;

CREATE TABLE sys_dictionary (
	id int8 NOT NULL,
	"name" varchar(50) NULL,
	"type" varchar(20) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_dictionary_pkey PRIMARY KEY (id)
);


-- public.sys_media definition

-- Drop table

-- DROP TABLE sys_media;

CREATE TABLE sys_media (
	id int8 NOT NULL,
	"type" int2 NULL,
	source_id int8 NULL,
	"path" varchar(255) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_media_pkey PRIMARY KEY (id)
);


-- public.sys_member definition

-- Drop table

-- DROP TABLE sys_member;

CREATE TABLE sys_member (
	id int8 NOT NULL,
	code varchar(50) NULL,
	store_id int8 NULL,
	wechat_name varchar(50) NULL,
	wechat_id varchar(50) NULL,
	real_name varchar(50) NULL,
	gender int2 NULL,
	birthday date NULL,
	china_birthday varchar(20) NULL,
	avatar_id int8 NULL,
	phone varchar(20) NULL,
	address varchar(255) DEFAULT '1' NULL,
	status int2 DEFAULT 1 NOT NULL,
	consumption varchar(50) NULL,
	has_children bool NULL,
	marital_status int2 NULL,
	remark varchar(500) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_pkey PRIMARY KEY (id)
);


-- public.sys_member_bussiness definition

-- Drop table

-- DROP TABLE sys_member_bussiness;

CREATE TABLE sys_member_bussiness (
	id int8 NOT NULL,
	member_id int8 NULL,
	point int4 NULL,
	last_consumption_date date NULL,
	consultant_id int8 NULL,
	last_beautician_id int8 NULL,
	tag varchar(255) NULL,
	is_enable_password bool NULL,
	consumption_password varchar(255) NULL,
	is_enable_sms varchar(255) NULL,
	remark varchar(500) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	referrer_user int8 NULL,
	CONSTRAINT sys_member_bussiness_pkey PRIMARY KEY (id)
);


-- public.sys_member_coupon definition

-- Drop table

-- DROP TABLE sys_member_coupon;

CREATE TABLE sys_member_coupon (
	id int8 NOT NULL,
	member_id int4 NULL,
	coupon_id int8 NULL,
	"name" varchar(50) NULL,
	"type" int2 NULL,
	face_price numeric(10, 2) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_coupon_pkey PRIMARY KEY (id)
);


-- public.sys_member_deposit definition

-- Drop table

-- DROP TABLE sys_member_deposit;

CREATE TABLE sys_member_deposit (
	id int8 NOT NULL,
	member_id int8 NULL,
	recharge_id int8 NULL,
	balance numeric(10, 2) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_deposit_pkey PRIMARY KEY (id)
);


-- public.sys_member_follow_notes definition

-- Drop table

-- DROP TABLE sys_member_follow_notes;

CREATE TABLE sys_member_follow_notes (
	id int8 NOT NULL,
	member_id int8 NULL,
	empolyee_id int8 NULL,
	remark varchar(500) NULL,
	"type" int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_follow_notes_pkey PRIMARY KEY (id)
);


-- public.sys_member_package definition

-- Drop table

-- DROP TABLE sys_member_package;

CREATE TABLE sys_member_package (
	id int8 NOT NULL,
	member_id int8 NULL,
	recharge_id int8 NULL,
	package_id int8 NULL,
	package_code varchar(255) NULL,
	package_name varchar(255) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	discount numeric(10, 2) NULL,
	status int2 NULL,
	total_price numeric(10, 2) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_package_pkey PRIMARY KEY (id)
);


-- public.sys_member_package_product definition

-- Drop table

-- DROP TABLE sys_member_package_product;

CREATE TABLE sys_member_package_product (
	id int8 NOT NULL,
	sys_member_package_id varchar(255) NULL,
	product_id int8 NULL,
	product_name numeric(10, 2) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	totle_price numeric(10, 2) NULL,
	discount numeric(10, 2) NULL,
	discount_total_price numeric(10, 2) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_package_product_pkey PRIMARY KEY (id)
);


-- public.sys_member_package_project definition

-- Drop table

-- DROP TABLE sys_member_package_project;

CREATE TABLE sys_member_package_project (
	id int8 NOT NULL,
	sys_member_package_id varchar(255) NULL,
	project_id int8 NULL,
	project_name numeric(10, 2) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	totle_price numeric(10, 2) NULL,
	discount numeric(10, 2) NULL,
	discount_total_price numeric(10, 2) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_package_project_pkey PRIMARY KEY (id)
);


-- public.sys_member_paid_card definition

-- Drop table

-- DROP TABLE sys_member_paid_card;

CREATE TABLE sys_member_paid_card (
	id int8 NOT NULL,
	member_id int8 NULL,
	recharge_id int8 NULL,
	card_id int8 NULL,
	card_code varchar(20) NULL,
	card_name varchar(20) NULL,
	actual_price numeric(10, 2) NULL,
	sale_price numeric(10, 2) NULL,
	project_discount numeric(10, 2) NULL,
	product_discount numeric(10, 2) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_paid_card_pkey PRIMARY KEY (id)
);


-- public.sys_member_project definition

-- Drop table

-- DROP TABLE sys_member_project;

CREATE TABLE sys_member_project (
	id int8 NOT NULL,
	member_id int8 NULL,
	recharge_id int8 NULL,
	project_id int8 NULL,
	project_code varchar(50) NULL,
	project_name varchar(255) NULL,
	project_category varchar(20) NULL,
	service_duration varchar(20) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	gift_quantity int2 NULL,
	total_price numeric(10, 2) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_project_pkey PRIMARY KEY (id)
);


-- public.sys_member_storage definition

-- Drop table

-- DROP TABLE sys_member_storage;

CREATE TABLE sys_member_storage (
	id int8 NOT NULL,
	member_id int8 NULL,
	store_id int8 NULL,
	product_id int8 NULL,
	product_name varchar(255) NULL,
	quantity int2 NULL,
	source_type int2 NULL,
	unit varchar(20) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_member_storage_pkey PRIMARY KEY (id)
);


-- public.sys_menu definition

-- Drop table

-- DROP TABLE sys_menu;

CREATE TABLE sys_menu (
	id int8 NOT NULL,
	menu_name varchar(50) NOT NULL,
	parent_id int8 DEFAULT 0 NULL,
	order_num int4 DEFAULT 0 NULL,
	"path" varchar(200) NULL,
	component varchar(255) NULL,
	query_param varchar(255) NULL,
	is_frame bool DEFAULT false NULL,
	is_cache bool DEFAULT false NULL,
	menu_type bpchar(1) DEFAULT 'M'::bpchar NULL,
	visible bool DEFAULT true NULL,
	status int4 DEFAULT 1 NOT NULL,
	perms varchar(100) NULL,
	icon varchar(100) NULL,
	remark text NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_menu_pkey PRIMARY KEY (id)
);


-- public.sys_package definition

-- Drop table

-- DROP TABLE sys_package;

CREATE TABLE sys_package (
	id int8 NOT NULL,
	code varchar(20) NULL,
	"name" varchar(255) NULL,
	actual_price numeric(10, 2) NULL,
	sale_price numeric(10, 2) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_package_pkey PRIMARY KEY (id)
);


-- public.sys_package_card_product definition

-- Drop table

-- DROP TABLE sys_package_card_product;

CREATE TABLE sys_package_card_product (
	id int8 NOT NULL,
	product_id int8 NULL,
	product_name numeric(10, 2) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	totle_price numeric(10, 2) NULL,
	discount numeric(10, 2) NULL,
	discount_total_price numeric(10, 2) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_package_card_product_pkey PRIMARY KEY (id)
);


-- public.sys_package_project definition

-- Drop table

-- DROP TABLE sys_package_project;

CREATE TABLE sys_package_project (
	id int8 NOT NULL,
	package_id int8 NULL,
	project_id int8 NULL,
	project_name numeric(10, 2) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	totle_price numeric(10, 2) NULL,
	discount numeric(10, 2) NULL,
	discount_total_price numeric(10, 2) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_package_project_pkey PRIMARY KEY (id)
);


-- public.sys_paid_card definition

-- Drop table

-- DROP TABLE sys_paid_card;

CREATE TABLE sys_paid_card (
	id int8 NOT NULL,
	code varchar(20) NULL,
	"name" varchar(255) NULL,
	min_recharge_amount numeric(10, 2) NULL,
	gift_amount numeric(10, 2) NULL,
	project_discount numeric(10, 2) NULL,
	product_discount numeric(10, 2) NULL,
	is_stock_limited bool NULL,
	stock_quantity int4 NULL,
	is_auto_stop_selling bool NULL,
	stop_selling_date date NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_paid_card_pkey PRIMARY KEY (id)
);


-- public.sys_payment definition

-- Drop table

-- DROP TABLE sys_payment;

CREATE TABLE sys_payment (
	id int8 NOT NULL,
	"type" int2 NULL,
	source_id int8 NULL,
	member_id int8 NULL,
	total_amount varchar(20) NULL,
	discount numeric(10, 2) NULL,
	actual_amount numeric(10, 2) NULL,
	alipay numeric(10, 2) NULL,
	wechat numeric(10, 2) NULL,
	cash numeric(10, 2) NULL,
	credit_card numeric(10, 2) NULL,
	other numeric(10, 2) NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_payment_pkey PRIMARY KEY (id)
);


-- public.sys_permission definition

-- Drop table

-- DROP TABLE sys_permission;

CREATE TABLE sys_permission (
	id int8 NOT NULL,
	permission_name varchar(100) NOT NULL,
	permission_code varchar(100) NOT NULL,
	permission_type int4 NOT NULL,
	resource_type varchar(50) NULL,
	scope_type int4 NOT NULL,
	scope_value text NULL,
	status int4 DEFAULT 1 NULL,
	remark text NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_permission_permission_code_key UNIQUE (permission_code),
	CONSTRAINT sys_permission_pkey PRIMARY KEY (id)
);


-- public.sys_point_settings definition

-- Drop table

-- DROP TABLE sys_point_settings;

CREATE TABLE sys_point_settings (
	id int8 NOT NULL,
	is_enabled bool DEFAULT false NULL,
	expiration_type int2 NULL,
	expiration_date date NULL,
	expiry_days int4 NULL,
	billing_enabled bool DEFAULT false NULL,
	billing_scenario varchar(100) NULL,
	billing_earning_method varchar(50) NULL,
	billing_point_value int4 NULL,
	billing_fixed_amount numeric(10, 2) NULL,
	recharge_enabled bool DEFAULT false NULL,
	recharge_scenario varchar(100) NULL,
	recharge_earning_method varchar(50) NULL,
	recharge_point_value int4 NULL,
	recharge_fixed_amount numeric(10, 2) NULL,
	redemption_ratio_points int4 DEFAULT 100 NULL,
	redemption_ratio_cash numeric(10, 2) DEFAULT 1.00 NULL,
	deduct_on_checkout bool DEFAULT false NULL,
	deduct_points int4 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT point_settings_pkey PRIMARY KEY (id)
);


-- public.sys_product definition

-- Drop table

-- DROP TABLE sys_product;

CREATE TABLE sys_product (
	id int8 NOT NULL,
	"name" varchar(50) NULL,
	code varchar(20) NULL,
	dict_category_id int8 NULL,
	dict_brand_id varchar(50) NULL,
	cost_price numeric(10, 2) NULL,
	sale_price numeric(10, 2) NULL,
	purchase_unit numeric(10, 2) NULL,
	standard_unit varchar(10) NULL,
	consumption_unit varchar(10) NULL,
	purchase_to_standard int4 NULL,
	purchase_to_consumption int4 NULL,
	status int2 DEFAULT 1 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_product_pkey PRIMARY KEY (id)
);


-- public.sys_project definition

-- Drop table

-- DROP TABLE sys_project;

CREATE TABLE sys_project (
	id int8 NOT NULL,
	"name" varchar(50) NULL,
	"number" varchar(20) NULL,
	dict_category_id int8 NULL,
	service_duration int2 NULL,
	sale_price numeric(10, 2) NULL,
	commission_score numeric(10, 1) NULL,
	remark varchar(500) NULL,
	status int2 DEFAULT 1 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_project_pkey PRIMARY KEY (id)
);


-- public.sys_project_product definition

-- Drop table

-- DROP TABLE sys_project_product;

CREATE TABLE sys_project_product (
	id int8 NOT NULL,
	product_id int8 NULL,
	product_name varchar(50) NULL,
	consumption numeric(10, 2) DEFAULT 1 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_project_product_pkey PRIMARY KEY (id)
);


-- public.sys_project_store definition

-- Drop table

-- DROP TABLE sys_project_store;

CREATE TABLE sys_project_store (
	id int8 NOT NULL,
	project_id int8 NULL,
	store_id int8 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_project_store_pkey PRIMARY KEY (id)
);


-- public.sys_recharge definition

-- Drop table

-- DROP TABLE sys_recharge;

CREATE TABLE sys_recharge (
	id int8 NOT NULL,
	member_id int8 NULL,
	member_name varchar(50) NULL,
	member_code varchar(20) NULL,
	member_mobile varchar(20) NULL,
	signature_file varchar(255) NULL,
	store_id int8 NULL,
	"type" int2 NULL,
	sub_type varchar(50) NULL,
	amount numeric(10, 2) NULL,
	remark varchar(500) NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	user_id int8 NULL,
	CONSTRAINT sys_recharge_pkey PRIMARY KEY (id)
);


-- public.sys_recharge_deposit definition

-- Drop table

-- DROP TABLE sys_recharge_deposit;

CREATE TABLE sys_recharge_deposit (
	id int8 NOT NULL,
	recharge_id int8 NULL,
	store_id int8 NULL,
	package_code varchar(255) NULL,
	package_name varchar(255) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	discount numeric(10, 2) NULL,
	total_price numeric(10, 2) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_recharge_deposit_pkey PRIMARY KEY (id)
);


-- public.sys_recharge_package definition

-- Drop table

-- DROP TABLE sys_recharge_package;

CREATE TABLE sys_recharge_package (
	id int8 NOT NULL,
	recharge_id int8 NULL,
	package_id int8 NULL,
	package_code varchar(255) NULL,
	package_name varchar(255) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	discount numeric(10, 2) NULL,
	total_price numeric(10, 2) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_recharge_package_pkey PRIMARY KEY (id)
);


-- public.sys_recharge_package_product definition

-- Drop table

-- DROP TABLE sys_recharge_package_product;

CREATE TABLE sys_recharge_package_product (
	id int8 NOT NULL,
	recharge_id int8 NULL,
	recharge_package_id int8 NULL,
	product_id int8 NULL,
	product_name numeric(10, 2) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	totle_price numeric(10, 2) NULL,
	discount numeric(10, 2) NULL,
	discount_total_price numeric(10, 2) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_recharge_package_product_pkey PRIMARY KEY (id)
);


-- public.sys_recharge_package_project definition

-- Drop table

-- DROP TABLE sys_recharge_package_project;

CREATE TABLE sys_recharge_package_project (
	id int8 NOT NULL,
	recharge_id int8 NULL,
	recharge_package_id int8 NULL,
	project_id int8 NULL,
	project_name numeric(10, 2) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	totle_price numeric(10, 2) NULL,
	discount numeric(10, 2) NULL,
	discount_total_price numeric(10, 2) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_recharge_package_project_pkey PRIMARY KEY (id)
);


-- public.sys_recharge_paid_card definition

-- Drop table

-- DROP TABLE sys_recharge_paid_card;

CREATE TABLE sys_recharge_paid_card (
	id int8 NOT NULL,
	recharge_id int8 NULL,
	card_id int8 NULL,
	card_code varchar(20) NULL,
	card_name varchar(20) NULL,
	actual_price numeric(10, 2) NULL,
	sale_price numeric(10, 2) NULL,
	project_discount numeric(10, 2) NULL,
	product_discount numeric(10, 2) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_recharge_paid_card_pkey PRIMARY KEY (id)
);


-- public.sys_recharge_project definition

-- Drop table

-- DROP TABLE sys_recharge_project;

CREATE TABLE sys_recharge_project (
	id int8 NOT NULL,
	recharge_id int8 NULL,
	project_id int8 NULL,
	project_code varchar(50) NULL,
	project_name varchar(255) NULL,
	project_category varchar(20) NULL,
	service_duration varchar(20) NULL,
	unit_price numeric(10, 2) NULL,
	quantity int2 NULL,
	gift_quantity int2 NULL,
	total_price numeric(10, 2) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_recharge_project_pkey PRIMARY KEY (id)
);


-- public.sys_role definition

-- Drop table

-- DROP TABLE sys_role;

CREATE TABLE sys_role (
	id int8 NOT NULL,
	role_name varchar(50) NOT NULL,
	role_code varchar(50) NOT NULL,
	role_sort int4 DEFAULT 0 NULL,
	data_scope int4 DEFAULT 1 NULL,
	status int4 DEFAULT 1 NOT NULL,
	remark text NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_role_pkey PRIMARY KEY (id),
	CONSTRAINT sys_role_role_code_key UNIQUE (role_code),
	CONSTRAINT sys_role_role_name_key UNIQUE (role_name)
);


-- public.sys_role_menu definition

-- Drop table

-- DROP TABLE sys_role_menu;

CREATE TABLE sys_role_menu (
	id int8 NOT NULL,
	role_id int8 NULL,
	menu_id int8 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_role_menu_pkey PRIMARY KEY (id)
);


-- public.sys_role_permission definition

-- Drop table

-- DROP TABLE sys_role_permission;

CREATE TABLE sys_role_permission (
	id int8 NOT NULL,
	role_id int8 NULL,
	permission_id int8 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_role_permission_pkey PRIMARY KEY (id)
);


-- public.sys_room definition

-- Drop table

-- DROP TABLE sys_room;

CREATE TABLE sys_room (
	id int8 NOT NULL,
	store_id int8 NOT NULL,
	"name" varchar(50) NULL,
	code varchar(20) NULL,
	seat_count int2 NULL,
	status int2 DEFAULT 1 NULL,
	remark varchar(500) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_room_pkey PRIMARY KEY (id, store_id)
);


-- public.sys_stock definition

-- Drop table

-- DROP TABLE sys_stock;

CREATE TABLE sys_stock (
	id int8 NOT NULL,
	product_id int8 NOT NULL,
	store_warehouse_id int8 NOT NULL,
	store_warehouse_type int2 NOT NULL,
	receive_quantity int2 NULL,
	receive_unit_name varchar(50) NULL,
	receive_unit_id int8 NULL,
	standard_quantity int2 NULL,
	standart_unit_id int8 NULL,
	standart_unit_name varchar(255) NULL,
	consume_unit_id int8 NULL,
	consume_unit_name varchar(255) NULL,
	consume_quantity int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_stock_pkey PRIMARY KEY (id)
);


-- public.sys_stock_inbound definition

-- Drop table

-- DROP TABLE sys_stock_inbound;

CREATE TABLE sys_stock_inbound (
	id int8 NOT NULL,
	code varchar(50) NOT NULL,
	supplier_id int8 NOT NULL,
	inbound_date date NOT NULL,
	remark varchar(500) NULL,
	signature_file varchar NULL,
	store_warehouse_id int8 NOT NULL,
	store_warehouse_type int2 NOT NULL,
	status int2 NULL,
	created_at timestamp(6) DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamp(6) DEFAULT CURRENT_TIMESTAMP NULL,
	created_by int8 NULL,
	updated_by int8 NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_inventory_inbound_inbound_no_key UNIQUE (code),
	CONSTRAINT sys_inventory_inbound_pkey PRIMARY KEY (id)
);


-- public.sys_stock_inbound_detail definition

-- Drop table

-- DROP TABLE sys_stock_inbound_detail;

CREATE TABLE sys_stock_inbound_detail (
	id int8 NOT NULL,
	stock_inbound_id int8 NOT NULL,
	product_id int8 NOT NULL,
	product_name varchar(255) NOT NULL,
	production_date date NULL,
	expiry_date date NULL,
	receive_quantity int2 NULL,
	receive_unit_name varchar(50) NULL,
	receive_unit_id int8 NULL,
	standard_quantity int2 NULL,
	standart_unit_id int8 NULL,
	standart_unit_name varchar(255) NULL,
	consume_unit_id int8 NULL,
	consume_unit_name varchar(255) NULL,
	consume_quantity int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_inventory_inbound_detail_pkey PRIMARY KEY (id)
);


-- public.sys_stock_inventory definition

-- Drop table

-- DROP TABLE sys_stock_inventory;

CREATE TABLE sys_stock_inventory (
	id int8 NOT NULL,
	code varchar(50) NOT NULL,
	inventory_date date NOT NULL,
	signature_file varchar(255) NULL,
	remark varchar(500) NULL,
	store_warehouse_id int8 NOT NULL,
	store_warehouse_type int2 NOT NULL,
	status int2 NULL,
	created_at timestamp(6) DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamp(6) DEFAULT CURRENT_TIMESTAMP NULL,
	created_by int8 NULL,
	updated_by int8 NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_stock_transfer_copy1_pkey PRIMARY KEY (id)
);


-- public.sys_stock_inventory_detail definition

-- Drop table

-- DROP TABLE sys_stock_inventory_detail;

CREATE TABLE sys_stock_inventory_detail (
	id int8 NOT NULL,
	stock_inventory_id int8 NOT NULL,
	product_id int8 NOT NULL,
	product_name varchar(255) NOT NULL,
	receive_quantity int2 NULL,
	receive_unit_name varchar(50) NULL,
	receive_unit_id int8 NULL,
	standard_quantity int2 NULL,
	standart_unit_id int8 NULL,
	standart_unit_name varchar(255) NULL,
	consume_unit_id int8 NULL,
	consume_unit_name varchar(255) NULL,
	consume_quantity int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_stock_transfer_detail_copy1_pkey PRIMARY KEY (id)
);


-- public.sys_stock_transfer definition

-- Drop table

-- DROP TABLE sys_stock_transfer;

CREATE TABLE sys_stock_transfer (
	id int8 NOT NULL,
	code varchar(50) NOT NULL,
	transfer_date date NOT NULL,
	out_store_warehouse_type int2 NULL,
	out_store_warehouse_id int8 NULL,
	in_store_warehouse_type int2 NULL,
	in_store_warehouse_id int8 NULL,
	signature_file varchar(255) NULL,
	remark varchar(500) NULL,
	status int2 NULL,
	created_at timestamp(6) DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamp(6) DEFAULT CURRENT_TIMESTAMP NULL,
	created_by int8 NULL,
	updated_by int8 NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_inventory_transfer_pkey PRIMARY KEY (id)
);


-- public.sys_stock_transfer_detail definition

-- Drop table

-- DROP TABLE sys_stock_transfer_detail;

CREATE TABLE sys_stock_transfer_detail (
	id int8 NOT NULL,
	stock_transfer_id int8 NOT NULL,
	product_id int8 NOT NULL,
	product_name varchar(255) NOT NULL,
	receive_quantity int2 NULL,
	receive_unit_name varchar(50) NULL,
	receive_unit_id int8 NULL,
	standard_quantity int2 NULL,
	standart_unit_id int8 NULL,
	standart_unit_name varchar(255) NULL,
	consume_unit_id int8 NULL,
	consume_unit_name varchar(255) NULL,
	consume_quantity int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_inventory_transfer_detail_pkey PRIMARY KEY (id)
);


-- public.sys_store definition

-- Drop table

-- DROP TABLE sys_store;

CREATE TABLE sys_store (
	id int8 NOT NULL,
	code varchar(50) NOT NULL,
	"name" varchar(100) NOT NULL,
	"level" int4 DEFAULT 1 NULL,
	phone varchar(20) NULL,
	address varchar(200) NULL,
	status int2 DEFAULT 1 NOT NULL,
	remark varchar(500) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_organization_pkey PRIMARY KEY (id)
);


-- public.sys_supplier definition

-- Drop table

-- DROP TABLE sys_supplier;

CREATE TABLE sys_supplier (
	id int8 NOT NULL,
	"name" varchar NULL,
	contact varchar NULL,
	contract_phone varchar NULL,
	contract_address varchar NULL,
	sort int2 NULL,
	status int2 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_supplier_pk PRIMARY KEY (id)
);


-- public.sys_user definition

-- Drop table

-- DROP TABLE sys_user;

CREATE TABLE sys_user (
	id int8 NOT NULL,
	user_code varchar(50) NOT NULL,
	username varchar(50) NOT NULL,
	"password" varchar(100) NOT NULL,
	email varchar(100) NULL,
	phone varchar(20) NULL,
	real_name varchar(50) NULL,
	avatar varchar(500) NULL,
	gender int4 NULL,
	birthday date NULL,
	employee_no varchar(50) NULL,
	entry_date date NULL,
	account_id int8 NULL,
	status int4 DEFAULT 1 NOT NULL,
	last_login_time timestamp(6) NULL,
	last_login_ip inet NULL,
	remark text NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_user_pkey PRIMARY KEY (id),
	CONSTRAINT sys_user_user_code_key UNIQUE (user_code),
	CONSTRAINT sys_user_username_key UNIQUE (username)
);


-- public.sys_user_profile definition

-- Drop table

-- DROP TABLE sys_user_profile;

CREATE TABLE sys_user_profile (
	id int8 NOT NULL,
	user_id int8 NOT NULL,
	id_card varchar(18) NULL,
	education varchar(50) NULL,
	major varchar(100) NULL,
	work_experience text NULL,
	skills text NULL,
	emergency_contact varchar(50) NULL,
	emergency_phone varchar(20) NULL,
	address text NULL,
	bank_account varchar(50) NULL,
	social_security varchar(50) NULL,
	extra_info jsonb NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	CONSTRAINT sys_user_profile_pkey PRIMARY KEY (id),
	CONSTRAINT sys_user_profile_user_id_key UNIQUE (user_id)
);


-- public.sys_user_role definition

-- Drop table

-- DROP TABLE sys_user_role;

CREATE TABLE sys_user_role (
	id int8 NOT NULL,
	user_id int8 NULL,
	role_id int8 NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_user_role_pkey PRIMARY KEY (id)
);


-- public.sys_user_store definition

-- Drop table

-- DROP TABLE sys_user_store;

CREATE TABLE sys_user_store (
	id int8 NOT NULL,
	user_id int8 NOT NULL,
	store_id int8 NOT NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_user_organization_pkey PRIMARY KEY (id, store_id)
);


-- public.sys_warehouse definition

-- Drop table

-- DROP TABLE sys_warehouse;

CREATE TABLE sys_warehouse (
	id int8 NOT NULL,
	"name" varchar NULL,
	code varchar NULL,
	sort int2 NULL,
	status int2 NULL,
	address varchar(255) NULL,
	created_by int8 NULL,
	created_at timestamp(6) DEFAULT now() NULL,
	updated_by int8 NULL,
	updated_at timestamp(6) DEFAULT now() NULL,
	deleted bool DEFAULT false NULL,
	CONSTRAINT sys_warehouse_pk PRIMARY KEY (id)
);

-- =====================================================
-- 说明和使用指南
-- =====================================================

-- 1. 公共表（pub_前缀）已在public schema中创建
--    这些表用于存储跨租户的公共数据，如租户信息、登录账户等

-- 2. 租户表（sys_前缀）需要在每个租户的schema中创建
--    使用以下函数可以批量创建租户表：
--    SELECT public.create_tenant_tables('tenant_schema_name');

-- 3. 为示例租户创建表（如果需要）
--    SELECT public.create_tenant_tables('tenant_demo');

RAISE NOTICE '==============================================';
RAISE NOTICE '数据库表创建脚本执行完成';
RAISE NOTICE '已创建 4 个公共表（pub_前缀）';
RAISE NOTICE '已定义 51 个租户表模板（sys_前缀）';
RAISE NOTICE '使用 create_tenant_tables() 函数为租户创建表';
RAISE NOTICE '==============================================';