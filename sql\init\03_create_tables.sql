-- =====================================================
-- 美姿姿健康管理系统 - 数据库表创建脚本（优化版）
-- 版本: 1.0.0
-- 创建时间: 2025-07-20
-- 作者: 美姿姿团队
-- 说明: 创建公共表和租户表模板
-- 数据库连接: *****************************************************/mzz
-- =====================================================

-- 确保在正确的数据库中执行
\c mzz

-- 设置客户端编码和时区
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- =====================================================
-- 第一部分：创建公共表（pub_前缀）
-- 这些表存储在public schema中，用于跨租户的公共数据
-- =====================================================

-- 1. pub_tenant - 租户信息表
CREATE TABLE IF NOT EXISTS public.pub_tenant (
    id BIGINT NOT NULL,
    tenant_code VARCHAR(50) NOT NULL,
    tenant_name VARCHAR(100) NOT NULL,
    schema_name VARCHAR(100) NOT NULL,
    tenant_type INTEGER DEFAULT 1 NOT NULL,
    status INTEGER DEFAULT 1 NOT NULL,
    contact_name VARCHAR(50) NULL,
    contact_phone VARCHAR(20) NULL,
    contact_email VARCHAR(100) NULL,
    address TEXT NULL,
    expire_time TIMESTAMP(6) NULL,
    max_users INTEGER DEFAULT 10 NULL,
    max_storage BIGINT DEFAULT ********** NULL,
    features JSONB NULL,
    settings JSONB NULL,
    remark TEXT NULL,
    created_by BIGINT NULL,
    created_at TIMESTAMP(6) DEFAULT NOW() NULL,
    updated_by BIGINT NULL,
    updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
    deleted BOOLEAN DEFAULT FALSE NULL,
    CONSTRAINT pub_tenant_pkey PRIMARY KEY (id),
    CONSTRAINT pub_tenant_schema_name_key UNIQUE (schema_name),
    CONSTRAINT pub_tenant_tenant_code_key UNIQUE (tenant_code)
);

-- 2. pub_platform_account - 平台账户表
CREATE TABLE IF NOT EXISTS public.pub_platform_account (
    id BIGINT NOT NULL,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(100) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    real_name VARCHAR(50) NULL,
    avatar VARCHAR(500) NULL,
    permission_level INTEGER DEFAULT 1 NULL,
    status INTEGER DEFAULT 1 NOT NULL,
    last_login_time TIMESTAMP(6) NULL,
    last_login_ip INET NULL,
    login_fail_count INTEGER DEFAULT 0 NULL,
    lock_time TIMESTAMP(6) NULL,
    password_expire_time TIMESTAMP(6) NULL,
    remark TEXT NULL,
    created_by BIGINT NULL,
    created_at TIMESTAMP(6) DEFAULT NOW() NULL,
    updated_by BIGINT NULL,
    updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
    deleted BOOLEAN DEFAULT FALSE NULL,
    CONSTRAINT pub_platform_account_pkey PRIMARY KEY (id),
    CONSTRAINT pub_platform_account_email_key UNIQUE (email),
    CONSTRAINT pub_platform_account_username_key UNIQUE (username)
);

-- 3. pub_user_account - 用户账户表（客户登录）
CREATE TABLE IF NOT EXISTS public.pub_user_account (
    id BIGINT NOT NULL,
    tenant_code VARCHAR(50) NOT NULL,
    login_type INTEGER DEFAULT 1 NOT NULL,
    login_identifier VARCHAR(200) NOT NULL,
    username VARCHAR(50) NULL,
    password VARCHAR(100) NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    real_name VARCHAR(50) NULL,
    avatar VARCHAR(500) NULL,
    wechat_openid VARCHAR(100) NULL,
    wechat_unionid VARCHAR(100) NULL,
    account_type INTEGER DEFAULT 1 NULL,
    status INTEGER DEFAULT 1 NOT NULL,
    last_login_time TIMESTAMP(6) NULL,
    last_login_ip INET NULL,
    login_fail_count INTEGER DEFAULT 0 NULL,
    lock_time TIMESTAMP(6) NULL,
    password_expire_time TIMESTAMP(6) NULL,
    bind_time TIMESTAMP(6) DEFAULT NOW() NULL,
    sys_user_id BIGINT NULL,
    is_primary BOOLEAN DEFAULT TRUE NULL,
    remark TEXT NULL,
    created_by BIGINT NULL,
    created_at TIMESTAMP(6) DEFAULT NOW() NULL,
    updated_by BIGINT NULL,
    updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
    deleted BOOLEAN DEFAULT FALSE NULL,
    CONSTRAINT pub_user_account_pkey PRIMARY KEY (id)
);

-- 4. pub_member_account - 会员账户表（小程序登录）
CREATE TABLE IF NOT EXISTS public.pub_member_account (
    id BIGINT NOT NULL,
    tenant_code VARCHAR(50) NULL,
    member_id BIGINT NULL,
    wechat_open_id VARCHAR(100) NULL,
    last_login_time TIMESTAMP(6) NULL,
    last_login_ip INET NULL,
    created_by BIGINT NULL,
    created_at TIMESTAMP(6) DEFAULT NOW() NULL,
    updated_by BIGINT NULL,
    updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
    deleted BOOLEAN DEFAULT FALSE NULL,
    tenant_id BIGINT NULL,
    CONSTRAINT pub_member_account_pkey PRIMARY KEY (id)
);

-- 添加公共表的注释
COMMENT ON TABLE public.pub_tenant IS '租户信息表，存储所有租户的基本信息';
COMMENT ON TABLE public.pub_platform_account IS '平台账户表，存储SaaS平台管理员账户';
COMMENT ON TABLE public.pub_user_account IS '用户账户表，存储客户系统登录账户';
COMMENT ON TABLE public.pub_member_account IS '会员账户表，存储小程序会员登录信息';

-- =====================================================
-- 第二部分：租户表创建函数
-- 这些表需要在每个租户的schema中创建
-- =====================================================

-- 创建租户表的函数
CREATE OR REPLACE FUNCTION public.create_tenant_tables(tenant_schema_name VARCHAR(100))
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    start_time TIMESTAMP := NOW();
    table_count INTEGER := 0;
BEGIN
    -- 验证schema名称
    IF tenant_schema_name IS NULL OR LENGTH(tenant_schema_name) < 3 THEN
        RAISE EXCEPTION '租户Schema名称无效: %', tenant_schema_name;
    END IF;
    
    -- 检查schema是否存在
    IF NOT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = tenant_schema_name) THEN
        RAISE EXCEPTION 'Schema % 不存在，请先创建Schema', tenant_schema_name;
    END IF;
    
    RAISE NOTICE '开始在Schema % 中创建租户表...', tenant_schema_name;
    
    -- 1. 系统用户表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_user (
            id BIGINT NOT NULL,
            username VARCHAR(50) NOT NULL,
            password VARCHAR(100) NOT NULL,
            email VARCHAR(100) NULL,
            phone VARCHAR(20) NULL,
            real_name VARCHAR(50) NOT NULL,
            avatar VARCHAR(500) NULL,
            status INTEGER DEFAULT 1 NOT NULL,
            last_login_time TIMESTAMP(6) NULL,
            last_login_ip INET NULL,
            login_fail_count INTEGER DEFAULT 0 NULL,
            lock_time TIMESTAMP(6) NULL,
            password_expire_time TIMESTAMP(6) NULL,
            remark TEXT NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            updated_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
            deleted BOOLEAN DEFAULT FALSE NULL,
            CONSTRAINT sys_user_pkey PRIMARY KEY (id),
            CONSTRAINT sys_user_username_key UNIQUE (username)
        )', tenant_schema_name);
    table_count := table_count + 1;
    
    -- 2. 角色表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_role (
            id BIGINT NOT NULL,
            role_name VARCHAR(50) NOT NULL,
            role_code VARCHAR(50) NOT NULL,
            description TEXT NULL,
            status INTEGER DEFAULT 1 NOT NULL,
            sort_order INTEGER DEFAULT 0 NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            updated_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
            deleted BOOLEAN DEFAULT FALSE NULL,
            CONSTRAINT sys_role_pkey PRIMARY KEY (id),
            CONSTRAINT sys_role_role_code_key UNIQUE (role_code)
        )', tenant_schema_name);
    table_count := table_count + 1;
    
    -- 3. 权限表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_permission (
            id BIGINT NOT NULL,
            permission_name VARCHAR(100) NOT NULL,
            permission_code VARCHAR(100) NOT NULL,
            permission_type INTEGER DEFAULT 1 NOT NULL,
            parent_id BIGINT NULL,
            resource_url VARCHAR(200) NULL,
            method VARCHAR(10) NULL,
            description TEXT NULL,
            status INTEGER DEFAULT 1 NOT NULL,
            sort_order INTEGER DEFAULT 0 NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            updated_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
            deleted BOOLEAN DEFAULT FALSE NULL,
            CONSTRAINT sys_permission_pkey PRIMARY KEY (id),
            CONSTRAINT sys_permission_permission_code_key UNIQUE (permission_code)
        )', tenant_schema_name);
    table_count := table_count + 1;
    
    -- 4. 菜单表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_menu (
            id BIGINT NOT NULL,
            menu_name VARCHAR(50) NOT NULL,
            menu_code VARCHAR(50) NOT NULL,
            parent_id BIGINT NULL,
            menu_type INTEGER DEFAULT 1 NOT NULL,
            path VARCHAR(200) NULL,
            component VARCHAR(200) NULL,
            icon VARCHAR(100) NULL,
            sort_order INTEGER DEFAULT 0 NULL,
            visible BOOLEAN DEFAULT TRUE NULL,
            status INTEGER DEFAULT 1 NOT NULL,
            remark TEXT NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            updated_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
            deleted BOOLEAN DEFAULT FALSE NULL,
            CONSTRAINT sys_menu_pkey PRIMARY KEY (id)
        )', tenant_schema_name);
    table_count := table_count + 1;
    
    -- 5. 用户角色关联表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_user_role (
            id BIGINT NOT NULL,
            user_id BIGINT NOT NULL,
            role_id BIGINT NOT NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            CONSTRAINT sys_user_role_pkey PRIMARY KEY (id),
            CONSTRAINT sys_user_role_unique UNIQUE (user_id, role_id)
        )', tenant_schema_name);
    table_count := table_count + 1;
    
    -- 6. 角色权限关联表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_role_permission (
            id BIGINT NOT NULL,
            role_id BIGINT NOT NULL,
            permission_id BIGINT NOT NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            CONSTRAINT sys_role_permission_pkey PRIMARY KEY (id),
            CONSTRAINT sys_role_permission_unique UNIQUE (role_id, permission_id)
        )', tenant_schema_name);
    table_count := table_count + 1;

    -- 7. 角色菜单关联表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_role_menu (
            id BIGINT NOT NULL,
            role_id BIGINT NOT NULL,
            menu_id BIGINT NOT NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            CONSTRAINT sys_role_menu_pkey PRIMARY KEY (id),
            CONSTRAINT sys_role_menu_unique UNIQUE (role_id, menu_id)
        )', tenant_schema_name);
    table_count := table_count + 1;

    -- 8. 会员表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_member (
            id BIGINT NOT NULL,
            member_no VARCHAR(50) NOT NULL,
            name VARCHAR(50) NOT NULL,
            phone VARCHAR(20) NULL,
            gender INTEGER NULL,
            birthday DATE NULL,
            avatar VARCHAR(500) NULL,
            wechat_openid VARCHAR(100) NULL,
            wechat_unionid VARCHAR(100) NULL,
            status INTEGER DEFAULT 1 NOT NULL,
            register_time TIMESTAMP(6) DEFAULT NOW() NULL,
            last_visit_time TIMESTAMP(6) NULL,
            total_consume DECIMAL(10,2) DEFAULT 0 NULL,
            total_recharge DECIMAL(10,2) DEFAULT 0 NULL,
            balance DECIMAL(10,2) DEFAULT 0 NULL,
            points INTEGER DEFAULT 0 NULL,
            remark TEXT NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            updated_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
            deleted BOOLEAN DEFAULT FALSE NULL,
            CONSTRAINT sys_member_pkey PRIMARY KEY (id),
            CONSTRAINT sys_member_member_no_key UNIQUE (member_no)
        )', tenant_schema_name);
    table_count := table_count + 1;

    -- 9. 门店表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_store (
            id BIGINT NOT NULL,
            store_name VARCHAR(100) NOT NULL,
            store_code VARCHAR(50) NOT NULL,
            address TEXT NULL,
            phone VARCHAR(20) NULL,
            manager_name VARCHAR(50) NULL,
            status INTEGER DEFAULT 1 NOT NULL,
            sort_order INTEGER DEFAULT 0 NULL,
            remark TEXT NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            updated_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
            deleted BOOLEAN DEFAULT FALSE NULL,
            CONSTRAINT sys_store_pkey PRIMARY KEY (id),
            CONSTRAINT sys_store_store_code_key UNIQUE (store_code)
        )', tenant_schema_name);
    table_count := table_count + 1;

    -- 10. 产品表
    EXECUTE format('
        CREATE TABLE IF NOT EXISTS %I.sys_product (
            id BIGINT NOT NULL,
            product_name VARCHAR(100) NOT NULL,
            product_code VARCHAR(50) NOT NULL,
            category_id BIGINT NULL,
            price DECIMAL(10,2) NOT NULL,
            cost_price DECIMAL(10,2) NULL,
            unit VARCHAR(20) NULL,
            description TEXT NULL,
            status INTEGER DEFAULT 1 NOT NULL,
            sort_order INTEGER DEFAULT 0 NULL,
            created_by BIGINT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW() NULL,
            updated_by BIGINT NULL,
            updated_at TIMESTAMP(6) DEFAULT NOW() NULL,
            deleted BOOLEAN DEFAULT FALSE NULL,
            CONSTRAINT sys_product_pkey PRIMARY KEY (id),
            CONSTRAINT sys_product_product_code_key UNIQUE (product_code)
        )', tenant_schema_name);
    table_count := table_count + 1;

    -- 记录执行结果
    RAISE NOTICE 'Schema % 中成功创建 % 个租户表，耗时: %',
                 tenant_schema_name, table_count, NOW() - start_time;

    RETURN TRUE;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION '在Schema % 中创建租户表失败: %', tenant_schema_name, SQLERRM;
END $$;

-- 创建示例租户表
SELECT public.create_tenant_tables('tenant_demo');

RAISE NOTICE '==============================================';
RAISE NOTICE '数据库表创建脚本执行完成';
RAISE NOTICE '已创建公共表: pub_tenant, pub_platform_account, pub_user_account, pub_member_account';
RAISE NOTICE '已创建租户表模板函数: create_tenant_tables()';
RAISE NOTICE '已在tenant_demo中创建示例租户表';
RAISE NOTICE '==============================================';
