package com.deepaic.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.deepaic.core.entity.PlatformAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 平台账户Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface PlatformAccountMapper extends BaseMapper<PlatformAccount> {

    /**
     * 根据用户名查询平台账户
     */
    @Select("""
        SELECT * FROM pub_platform_account 
        WHERE username = #{username} AND deleted = 0
        """)
    PlatformAccount selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询平台账户
     */
    @Select("""
        SELECT * FROM pub_platform_account 
        WHERE email = #{email} AND deleted = 0
        """)
    PlatformAccount selectByEmail(@Param("email") String email);

    /**
     * 根据手机号查询平台账户
     */
    @Select("""
        SELECT * FROM pub_platform_account 
        WHERE phone = #{phone} AND deleted = 0
        """)
    PlatformAccount selectByPhone(@Param("phone") String phone);

    /**
     * 更新最后登录信息
     */
    @Update("""
        UPDATE pub_platform_account
        SET last_login_time = #{loginTime},
            last_login_ip = #{loginIp},
            login_fail_count = 0,
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int updateLastLoginInfo(@Param("accountId") Long accountId,
                           @Param("loginTime") LocalDateTime loginTime,
                           @Param("loginIp") String loginIp);

    /**
     * 增加登录失败次数
     */
    @Update("""
        UPDATE pub_platform_account
        SET login_fail_count = COALESCE(login_fail_count, 0) + 1,
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int incrementLoginFailCount(@Param("accountId") Long accountId);

    /**
     * 锁定账户
     */
    @Update("""
        UPDATE pub_platform_account
        SET status = 2,
            lock_time = #{lockTime},
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int lockAccount(@Param("accountId") Long accountId, @Param("lockTime") LocalDateTime lockTime);

    /**
     * 解锁账户
     */
    @Update("""
        UPDATE pub_platform_account
        SET status = 1,
            lock_time = NULL,
            login_fail_count = 0,
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int unlockAccount(@Param("accountId") Long accountId);

    /**
     * 重置密码
     */
    @Update("""
        UPDATE pub_platform_account
        SET password = #{newPassword},
            password_expire_time = #{expireTime},
            updated_at = NOW()
        WHERE id = #{accountId}
        """)
    int resetPassword(@Param("accountId") Long accountId,
                     @Param("newPassword") String newPassword,
                     @Param("expireTime") LocalDateTime expireTime);

    /**
     * 检查用户名是否存在
     */
    @Select("""
        SELECT COUNT(*) > 0
        FROM pub_platform_account
        WHERE username = #{username} AND deleted = 0
        """)
    boolean existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    @Select("""
        SELECT COUNT(*) > 0
        FROM pub_platform_account
        WHERE email = #{email} AND deleted = 0
        """)
    boolean existsByEmail(@Param("email") String email);

    /**
     * 检查手机号是否存在
     */
    @Select("""
        SELECT COUNT(*) > 0
        FROM pub_platform_account
        WHERE phone = #{phone} AND deleted = 0
        """)
    boolean existsByPhone(@Param("phone") String phone);

    /**
     * 查询所有启用的平台账户
     */
    @Select("""
        SELECT * FROM pub_platform_account 
        WHERE status = 1 AND deleted = 0
        ORDER BY create_time DESC
        """)
    List<PlatformAccount> selectEnabledAccounts();

    /**
     * 根据账户类型查询账户
     */
    @Select("""
        SELECT * FROM pub_platform_account 
        WHERE account_type = #{accountType} AND deleted = 0
        ORDER BY create_time DESC
        """)
    List<PlatformAccount> selectByAccountType(@Param("accountType") Integer accountType);

    /**
     * 统计各状态的账户数量
     */
    @Select("""
        SELECT status, COUNT(*) as count
        FROM pub_platform_account
        WHERE deleted = 0
        GROUP BY status
        """)
    List<Object> countByStatus();

    /**
     * 查询即将过期的密码账户
     */
    @Select("""
        SELECT * FROM pub_platform_account
        WHERE password_expire_time IS NOT NULL
        AND password_expire_time <= #{expireTime}
        AND deleted = 0
        """)
    List<PlatformAccount> selectPasswordExpiringSoon(@Param("expireTime") LocalDateTime expireTime);
}
