package com.deepaic.core.auth.utils;

import cn.dev33.satoken.stp.StpUtil;
import com.deepaic.core.auth.models.UserPrincipal;
import lombok.extern.slf4j.Slf4j;

/**
 * 认证工具类
 * 提供便捷的认证相关操作
 *
 * <AUTHOR>
 */
@Slf4j
public class AuthUtils {

    /**
     * 获取当前登录用户ID
     */
    public static String getCurrentUserId() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }
            
            String loginId = StpUtil.getLoginIdAsString();
            // 解析 userType:userId 格式
            String[] parts = loginId.split(":");
            return parts.length == 2 ? parts[1] : null;
        } catch (Exception e) {
            log.debug("获取当前用户ID失败", e);
            return null;
        }
    }

    /**
     * 获取当前用户类型
     */
    public static UserPrincipal.UserType getCurrentUserType() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }
            
            String loginId = StpUtil.getLoginIdAsString();
            // 解析 userType:userId 格式
            String[] parts = loginId.split(":");
            if (parts.length == 2) {
                return UserPrincipal.UserType.valueOf(parts[0]);
            }
            return null;
        } catch (Exception e) {
            log.debug("获取当前用户类型失败", e);
            return null;
        }
    }

    /**
     * 获取当前用户主体信息
     */
    public static UserPrincipal getCurrentUserPrincipal() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }
            
            return (UserPrincipal) StpUtil.getSession().get("userPrincipal");
        } catch (Exception e) {
            log.debug("获取当前用户主体信息失败", e);
            return null;
        }
    }

    /**
     * 获取当前租户编码
     */
    public static String getCurrentTenantCode() {
        try {
            UserPrincipal userPrincipal = getCurrentUserPrincipal();
            return userPrincipal != null ? userPrincipal.getTenantCode() : null;
        } catch (Exception e) {
            log.debug("获取当前租户编码失败", e);
            return null;
        }
    }

    /**
     * 检查当前用户是否为平台账户
     */
    public static boolean isPlatformAccount() {
        UserPrincipal.UserType userType = getCurrentUserType();
        return UserPrincipal.UserType.PLATFORM_ACCOUNT.equals(userType);
    }

    /**
     * 检查当前用户是否为客户账户
     */
    public static boolean isCustomerAccount() {
        UserPrincipal.UserType userType = getCurrentUserType();
        return UserPrincipal.UserType.SASS_CLIENT_ACCOUNT.equals(userType);
    }

    /**
     * 检查当前用户是否为会员账户
     */
    public static boolean isMemberAccount() {
        UserPrincipal.UserType userType = getCurrentUserType();
        return UserPrincipal.UserType.SASS_MEMBER_ACCOUNT.equals(userType);
    }

    /**
     * 检查当前用户是否有指定权限
     */
    public static boolean hasAuthority(String authority) {
        try {
            UserPrincipal userPrincipal = getCurrentUserPrincipal();
            return userPrincipal != null && userPrincipal.hasAuthority(authority);
        } catch (Exception e) {
            log.debug("检查权限失败: authority={}", authority, e);
            return false;
        }
    }

    /**
     * 检查当前用户是否有指定角色
     */
    public static boolean hasRole(String role) {
        try {
            UserPrincipal userPrincipal = getCurrentUserPrincipal();
            return userPrincipal != null && userPrincipal.hasRole(role);
        } catch (Exception e) {
            log.debug("检查角色失败: role={}", role, e);
            return false;
        }
    }

    /**
     * 检查当前用户是否属于指定租户
     */
    public static boolean belongsToTenant(String tenantCode) {
        try {
            String currentTenantCode = getCurrentTenantCode();
            return tenantCode != null && tenantCode.equals(currentTenantCode);
        } catch (Exception e) {
            log.debug("检查租户归属失败: tenantCode={}", tenantCode, e);
            return false;
        }
    }

    /**
     * 要求当前用户已登录
     */
    public static void requireLogin() {
        if (!StpUtil.isLogin()) {
            throw new RuntimeException("用户未登录");
        }
    }

    /**
     * 要求当前用户为平台账户
     */
    public static void requirePlatformAccount() {
        requireLogin();
        if (!isPlatformAccount()) {
            throw new RuntimeException("需要平台账户权限");
        }
    }

    /**
     * 要求当前用户为客户账户
     */
    public static void requireCustomerAccount() {
        requireLogin();
        if (!isCustomerAccount()) {
            throw new RuntimeException("需要客户账户权限");
        }
    }

    /**
     * 要求当前用户为会员账户
     */
    public static void requireMemberAccount() {
        requireLogin();
        if (!isMemberAccount()) {
            throw new RuntimeException("需要会员账户权限");
        }
    }

    /**
     * 要求当前用户有指定权限
     */
    public static void requireAuthority(String authority) {
        requireLogin();
        if (!hasAuthority(authority)) {
            throw new RuntimeException("权限不足: " + authority);
        }
    }

    /**
     * 要求当前用户有指定角色
     */
    public static void requireRole(String role) {
        requireLogin();
        if (!hasRole(role)) {
            throw new RuntimeException("角色不足: " + role);
        }
    }

    /**
     * 要求当前用户属于指定租户
     */
    public static void requireTenant(String tenantCode) {
        requireLogin();
        if (!belongsToTenant(tenantCode)) {
            throw new RuntimeException("租户权限不足: " + tenantCode);
        }
    }
}
