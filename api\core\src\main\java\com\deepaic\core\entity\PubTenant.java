package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@TableName("pub_tenant")
public class PubTenant extends BaseEntity {

    private String tenantCode;

    private String tenantName;

    private String schemaName;

    private Integer tenantType;

    private Integer status;

    private String contactName;

    private String contactPhone;

    private String contactEmail;

    private String address;

    private LocalDateTime expireTime;

    private Integer maxUsers;

    private Long maxStorage;

    private Object features;

    private Object settings;

    private String remark;
}
