package com.deepaic.core.tenant;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

/**
 * 租户Schema管理器
 * 负责创建和管理租户专属的数据库Schema
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TenantSchemaManager {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 创建租户Schema
     *
     * @param tenantCode 租户编码
     */
    public void createTenantSchema(String tenantCode) {
        try {
            String schemaName = "tenant_" + tenantCode;
            
            // 创建Schema
            String createSchemaSql = "CREATE SCHEMA IF NOT EXISTS " + schemaName;
            jdbcTemplate.execute(createSchemaSql);
            
            log.info("租户Schema创建成功: {}", schemaName);
        } catch (Exception e) {
            log.error("创建租户Schema失败: tenantCode={}", tenantCode, e);
            throw new RuntimeException("创建租户Schema失败", e);
        }
    }

    /**
     * 初始化租户表结构
     *
     * @param tenantCode 租户编码
     */
    public void initializeTenantTables(String tenantCode) {
        try {
            String schemaName = "tenant_" + tenantCode;
            
            // 创建sys_user表
            createSysUserTable(schemaName);
            
            // 创建sys_member表
            createSysMemberTable(schemaName);
            
            // 创建其他业务表
            createBusinessTables(schemaName);
            
            log.info("租户表结构初始化成功: {}", schemaName);
        } catch (Exception e) {
            log.error("初始化租户表结构失败: tenantCode={}", tenantCode, e);
            throw new RuntimeException("初始化租户表结构失败", e);
        }
    }

    /**
     * 初始化租户基础数据
     *
     * @param tenantCode 租户编码
     */
    public void initializeTenantData(String tenantCode) {
        try {
            String schemaName = "tenant_" + tenantCode;
            
            // 插入基础配置数据
            insertBasicConfigData(schemaName);
            
            log.info("租户基础数据初始化成功: {}", schemaName);
        } catch (Exception e) {
            log.error("初始化租户基础数据失败: tenantCode={}", tenantCode, e);
            throw new RuntimeException("初始化租户基础数据失败", e);
        }
    }

    /**
     * 删除租户Schema
     *
     * @param tenantCode 租户编码
     */
    public void dropTenantSchema(String tenantCode) {
        try {
            String schemaName = "tenant_" + tenantCode;
            
            // 删除Schema（CASCADE会删除所有表）
            String dropSchemaSql = "DROP SCHEMA IF EXISTS " + schemaName + " CASCADE";
            jdbcTemplate.execute(dropSchemaSql);
            
            log.info("租户Schema删除成功: {}", schemaName);
        } catch (Exception e) {
            log.error("删除租户Schema失败: tenantCode={}", tenantCode, e);
            throw new RuntimeException("删除租户Schema失败", e);
        }
    }

    /**
     * 创建sys_user表
     */
    private void createSysUserTable(String schemaName) {
        String sql = String.format("""
            CREATE TABLE IF NOT EXISTS %s.sys_user (
                id BIGSERIAL PRIMARY KEY,
                account_id BIGINT NOT NULL COMMENT '关联的账户ID(pub_customer_account.id)',
                user_code VARCHAR(50) NOT NULL UNIQUE COMMENT '用户编码',
                nickname VARCHAR(50) COMMENT '昵称',
                gender INTEGER DEFAULT 0 COMMENT '性别 0:未知 1:男 2:女',
                birthday DATE COMMENT '生日',
                department_id BIGINT COMMENT '部门ID',
                position VARCHAR(100) COMMENT '职位',
                employee_no VARCHAR(50) COMMENT '员工编号',
                entry_date DATE COMMENT '入职日期',
                status INTEGER DEFAULT 1 COMMENT '用户状态 0:禁用 1:启用',
                sort_order INTEGER DEFAULT 0 COMMENT '排序',
                remark VARCHAR(500) COMMENT '备注',
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                deleted INTEGER DEFAULT 0 COMMENT '删除标记 0:未删除 1:已删除',
                version INTEGER DEFAULT 1 COMMENT '版本号'
            )
            """, schemaName);
        
        jdbcTemplate.execute(sql);
        
        // 创建索引
        jdbcTemplate.execute(String.format("CREATE INDEX IF NOT EXISTS idx_%s_sys_user_account_id ON %s.sys_user(account_id)", 
                schemaName.replace("tenant_", ""), schemaName));
        jdbcTemplate.execute(String.format("CREATE INDEX IF NOT EXISTS idx_%s_sys_user_code ON %s.sys_user(user_code)", 
                schemaName.replace("tenant_", ""), schemaName));
    }

    /**
     * 创建sys_member表
     */
    private void createSysMemberTable(String schemaName) {
        String sql = String.format("""
            CREATE TABLE IF NOT EXISTS %s.sys_member (
                id BIGSERIAL PRIMARY KEY,
                member_no VARCHAR(50) NOT NULL UNIQUE COMMENT '会员编号',
                nickname VARCHAR(50) COMMENT '昵称',
                real_name VARCHAR(50) COMMENT '真实姓名',
                avatar VARCHAR(500) COMMENT '头像URL',
                gender INTEGER DEFAULT 0 COMMENT '性别 0:未知 1:男 2:女',
                birthday DATE COMMENT '生日',
                phone VARCHAR(20) COMMENT '手机号',
                email VARCHAR(100) COMMENT '邮箱',
                level INTEGER DEFAULT 1 COMMENT '会员等级',
                points INTEGER DEFAULT 0 COMMENT '积分',
                balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额',
                status INTEGER DEFAULT 1 COMMENT '状态 0:禁用 1:正常 2:冻结',
                register_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
                last_login_time TIMESTAMP COMMENT '最后登录时间',
                remark VARCHAR(500) COMMENT '备注',
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                deleted INTEGER DEFAULT 0 COMMENT '删除标记 0:未删除 1:已删除',
                version INTEGER DEFAULT 1 COMMENT '版本号'
            )
            """, schemaName);
        
        jdbcTemplate.execute(sql);
        
        // 创建索引
        jdbcTemplate.execute(String.format("CREATE INDEX IF NOT EXISTS idx_%s_sys_member_no ON %s.sys_member(member_no)", 
                schemaName.replace("tenant_", ""), schemaName));
        jdbcTemplate.execute(String.format("CREATE INDEX IF NOT EXISTS idx_%s_sys_member_phone ON %s.sys_member(phone)", 
                schemaName.replace("tenant_", ""), schemaName));
    }

    /**
     * 创建业务表
     */
    private void createBusinessTables(String schemaName) {
        // 创建示例业务表
        String sql = String.format("""
            CREATE TABLE IF NOT EXISTS %s.business_data (
                id BIGSERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL COMMENT '名称',
                description TEXT COMMENT '描述',
                status INTEGER DEFAULT 1 COMMENT '状态',
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                deleted INTEGER DEFAULT 0 COMMENT '删除标记',
                version INTEGER DEFAULT 1 COMMENT '版本号'
            )
            """, schemaName);
        
        jdbcTemplate.execute(sql);
    }

    /**
     * 插入基础配置数据
     */
    private void insertBasicConfigData(String schemaName) {
        // 插入示例数据
        String sql = String.format("""
            INSERT INTO %s.business_data (name, description, status) VALUES 
            ('示例数据1', '这是租户的第一条业务数据', 1),
            ('示例数据2', '这是租户的第二条业务数据', 1)
            ON CONFLICT DO NOTHING
            """, schemaName);
        
        jdbcTemplate.execute(sql);
    }

    /**
     * 检查Schema是否存在
     */
    public boolean schemaExists(String tenantCode) {
        try {
            String schemaName = "tenant_" + tenantCode;
            String sql = "SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, schemaName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查Schema是否存在失败: tenantCode={}", tenantCode, e);
            return false;
        }
    }
}
