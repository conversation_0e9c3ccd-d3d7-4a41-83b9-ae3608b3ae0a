package com.deepaic.core.service;

/**
 * 短信服务接口
 * 提供短信发送和验证功能
 *
 * <AUTHOR>
 */
public interface ISmsService {

    /**
     * 发送注册短信验证码
     *
     * @param phone 手机号
     * @param clientIp 客户端IP
     * @return 是否发送成功
     */
    boolean sendRegistrationSmsCode(String phone, String clientIp);

    /**
     * 验证短信验证码
     *
     * @param phone 手机号
     * @param code 验证码
     * @return 是否验证通过
     */
    boolean validateSmsCode(String phone, String code);

    /**
     * 发送注册成功短信
     *
     * @param phone 手机号
     * @param tenantName 租户名称
     * @param username 用户名
     * @return 是否发送成功
     */
    boolean sendRegistrationSuccessSms(String phone, String tenantName, String username);

    /**
     * 发送登录短信验证码
     *
     * @param phone 手机号
     * @param clientIp 客户端IP
     * @return 是否发送成功
     */
    boolean sendLoginSmsCode(String phone, String clientIp);

    /**
     * 发送密码重置短信验证码
     *
     * @param phone 手机号
     * @param clientIp 客户端IP
     * @return 是否发送成功
     */
    boolean sendPasswordResetSmsCode(String phone, String clientIp);
}
