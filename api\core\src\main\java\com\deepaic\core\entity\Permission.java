package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_permission")
public class Permission extends BaseEntity {

    private String permissionName;

    private String permissionCode;

    private Short permissionType;

    private String resourceType;

    private Short scopeType;

    private String scopeValue;

    private Short status;

    private String remark;


    // 权限类型常量
    public static final short TYPE_ORGANIZATION = 1;  // 组织权限
    public static final short TYPE_DATA = 2;          // 数据权限
    public static final short TYPE_FIELD = 3;         // 字段权限
    public static final short TYPE_FUNCTION = 4;      // 功能权限

    // 权限范围类型常量
    public static final short SCOPE_ALL = 1;              // 全部
    public static final short SCOPE_CURRENT_ORG = 2;      // 本组织
    public static final short SCOPE_CURRENT_AND_SUB = 3;  // 本组织及下级
    public static final short SCOPE_CUSTOM = 4;           // 自定义
    public static final short SCOPE_SELF_ONLY = 5;        // 仅本人

    // 状态常量
    public static final short STATUS_DISABLED = 0;   // 禁用
    public static final short STATUS_ENABLED = 1;    // 启用

    /**
     * 检查权限是否启用
     */
    public boolean isEnabled() {
        return Short.valueOf(STATUS_ENABLED).equals(this.status);
    }

    /**
     * 检查是否为组织权限
     */
    public boolean isOrganizationPermission() {
        return Short.valueOf(TYPE_ORGANIZATION).equals(this.permissionType);
    }

    /**
     * 检查是否为数据权限
     */
    public boolean isDataPermission() {
        return Short.valueOf(TYPE_DATA).equals(this.permissionType);
    }

    /**
     * 检查是否为字段权限
     */
    public boolean isFieldPermission() {
        return Short.valueOf(TYPE_FIELD).equals(this.permissionType);
    }

    /**
     * 检查是否为功能权限
     */
    public boolean isFunctionPermission() {
        return Short.valueOf(TYPE_FUNCTION).equals(this.permissionType);
    }

    /**
     * 检查是否为全部权限范围
     */
    public boolean isAllScope() {
        return Short.valueOf(SCOPE_ALL).equals(this.scopeType);
    }

    /**
     * 检查是否为自定义权限范围
     */
    public boolean isCustomScope() {
        return Short.valueOf(SCOPE_CUSTOM).equals(this.scopeType);
    }
}
