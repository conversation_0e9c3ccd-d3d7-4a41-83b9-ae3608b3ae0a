package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_stock_inventory_detail")
public class StockInventoryDetail extends BaseEntity {

    /**
     * 主表主键
     */
    private Long stockInventoryId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 进货单位数量
     */
    private Short receiveQuantity;

    /**
     * 进货单位名称
     */
    private String receiveUnitName;

    /**
     * 进货单位id
     */
    private Long receiveUnitId;

    /**
     * 标准单位数量
     */
    private Short standardQuantity;

    /**
     * 标准单位id
     */
    private Long standartUnitId;

    /**
     * 标准单位名称
     */
    private String standartUnitName;

    /**
     * 消费单位id
     */
    private Long consumeUnitId;

    /**
     * 消费单位名称
     */
    private String consumeUnitName;

    /**
     * 消费单位数量
     */
    private Short consumeQuantity;
}
