[versions]
# Spring Boot 相关版本
spring-boot = "3.5.2"
spring-dependency-management = "1.1.7"

# Java 版本
java = "21"

# 测试相关版本
junit = "5.10.1"

# 数据库相关版本
mysql = "8.0.33"
postgresql = "42.7.1"
mybatis-spring-boot = "3.0.3"
mybatis-plus = "3.5.12"
druid-spring-boot = "1.2.20"

# 工具类版本
lombok = "1.18.38"
mapstruct = "1.5.5.Final"
hutool = "5.8.22"
fastjson2 = "2.0.43"

# 文档相关版本
springdoc-openapi = "2.8.5"

# 安全相关版本
spring-security = "6.2.0"
jjwt = "0.12.3"

# 缓存相关版本
redisson = "3.24.3"

# 权限认证版本
sa-token = "1.37.0"

# 消息队列版本
spring-kafka = "3.1.0"
rabbitmq = "3.1.0"

[libraries]
# Spring Boot 核心依赖
spring-boot-starter = { module = "org.springframework.boot:spring-boot-starter", version.ref = "spring-boot" }
spring-boot-starter-web = { module = "org.springframework.boot:spring-boot-starter-web", version.ref = "spring-boot" }
spring-boot-starter-data-jpa = { module = "org.springframework.boot:spring-boot-starter-data-jpa", version.ref = "spring-boot" }
spring-boot-starter-security = { module = "org.springframework.boot:spring-boot-starter-security", version.ref = "spring-boot" }
spring-boot-starter-validation = { module = "org.springframework.boot:spring-boot-starter-validation", version.ref = "spring-boot" }
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator", version.ref = "spring-boot" }
spring-boot-starter-cache = { module = "org.springframework.boot:spring-boot-starter-cache", version.ref = "spring-boot" }
spring-boot-starter-test = { module = "org.springframework.boot:spring-boot-starter-test", version.ref = "spring-boot" }
spring-boot-starter-freemarker = { module = "org.springframework.boot:spring-boot-starter-freemarker", version.ref = "spring-boot" }


# 数据库相关
mysql-connector = { module = "mysql:mysql-connector-java", version.ref = "mysql" }
postgresql-connector = { module = "org.postgresql:postgresql", version.ref = "postgresql" }
mybatis-spring-boot-starter = { module = "org.mybatis.spring.boot:mybatis-spring-boot-starter", version.ref = "mybatis-spring-boot" }
mybatis-plus-spring-boot3-starter = { module = "com.baomidou:mybatis-plus-spring-boot3-starter", version.ref = "mybatis-plus" }
mybatis-plus-generator = { module = "com.baomidou:mybatis-plus-generator", version.ref = "mybatis-plus" }
druid-spring-boot-starter = { module = "com.alibaba:druid-spring-boot-starter", version.ref = "druid-spring-boot" }

# 工具类
lombok = { module = "org.projectlombok:lombok", version.ref = "lombok" }
mapstruct = { module = "org.mapstruct:mapstruct", version.ref = "mapstruct" }
mapstruct-processor = { module = "org.mapstruct:mapstruct-processor", version.ref = "mapstruct" }
hutool-all = { module = "cn.hutool:hutool-all", version.ref = "hutool" }
fastjson2 = { module = "com.alibaba.fastjson2:fastjson2", version.ref = "fastjson2" }

# 文档
springdoc-openapi-starter-webmvc-ui = { module = "org.springdoc:springdoc-openapi-starter-webmvc-ui", version.ref = "springdoc-openapi" }

# JWT
jjwt-api = { module = "io.jsonwebtoken:jjwt-api", version.ref = "jjwt" }
jjwt-impl = { module = "io.jsonwebtoken:jjwt-impl", version.ref = "jjwt" }
jjwt-jackson = { module = "io.jsonwebtoken:jjwt-jackson", version.ref = "jjwt" }

# 缓存
redisson-spring-boot-starter = { module = "org.redisson:redisson-spring-boot-starter", version.ref = "redisson" }

# 权限认证相关
sa-token-spring-boot3-starter = { module = "cn.dev33:sa-token-spring-boot3-starter", version.ref = "sa-token" }
sa-token-redis-jackson = { module = "cn.dev33:sa-token-redis-jackson", version.ref = "sa-token" }

# Redis相关
spring-boot-starter-data-redis = { module = "org.springframework.boot:spring-boot-starter-data-redis" }

# 消息队列
spring-kafka = { module = "org.springframework.kafka:spring-kafka", version.ref = "spring-kafka" }
spring-boot-starter-amqp = { module = "org.springframework.boot:spring-boot-starter-amqp", version.ref = "spring-boot" }

# 测试
junit-platform-launcher = { module = "org.junit.platform:junit-platform-launcher" }

[bundles]
# 常用依赖组合
spring-web = ["spring-boot-starter-web", "spring-boot-starter-validation", "spring-boot-starter-actuator"]
spring-data = ["spring-boot-starter-data-jpa", "mysql-connector", "druid-spring-boot-starter"]
spring-data-mybatis-plus = ["mybatis-plus-spring-boot3-starter", "mysql-connector", "druid-spring-boot-starter"]
spring-data-mybatis-plus-postgresql = ["mybatis-plus-spring-boot3-starter", "postgresql-connector", "druid-spring-boot-starter"]
spring-security-jwt = ["spring-boot-starter-security", "jjwt-api", "jjwt-impl", "jjwt-jackson"]
tools = ["lombok", "mapstruct", "hutool-all", "fastjson2"]
test = ["spring-boot-starter-test", "junit-platform-launcher"]

[plugins]
spring-boot = { id = "org.springframework.boot", version.ref = "spring-boot" }
spring-dependency-management = { id = "io.spring.dependency-management", version.ref = "spring-dependency-management" }
