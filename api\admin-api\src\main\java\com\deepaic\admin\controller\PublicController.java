package com.deepaic.admin.controller;

import com.deepaic.admin.request.LoginRequest;
import com.deepaic.admin.controller.AuthController.PhoneLoginRequest;
import com.deepaic.admin.controller.AuthController.WechatLoginRequest;
import com.deepaic.core.auth.AuthenticationManager;
import com.deepaic.core.auth.models.AuthRequest;
import com.deepaic.core.auth.models.AuthResponse;
import com.deepaic.core.auth.models.UserPrincipal;
import com.deepaic.core.exception.BussinessException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/public")
public class PublicController {

    private final AuthenticationManager authenticationManager;



    /**
     * 租户用户用户名密码登录
     * 使用用户名和密码进行登录认证
     * @throws BussinessException 
     */
    @PostMapping("/auth/password")
    public ResponseEntity<AuthResponse> loginByUsernamePassword(@RequestBody @Valid LoginRequest loginRequest,
                                     HttpServletRequest httpRequest) throws BussinessException {

        // 创建认证请求
        AuthRequest authRequest = AuthRequest.builder()
                                .userType(UserPrincipal.UserType.SASS_CLIENT_ACCOUNT)
                                .authenticationType(AuthRequest.AuthenticationType.USERNAME_PASSWORD)
                                .username(loginRequest.getUsername())
                                .password(loginRequest.getPassword())
                                .build()
                                .fillHttpRequestParams(httpRequest);

        // 执行认证
        AuthResponse authResponse = authenticationManager.authenticate(authRequest);
        return ResponseEntity.ok(authResponse);
    }

  
    /**
     * 租户用户手机号密码登录
     * 使用手机号和密码进行登录认证
     * @throws BussinessException 
     */
    @PostMapping("/auth/phone-password")
    public ResponseEntity<AuthResponse> loginByPhonePassword(@RequestBody @Valid LoginRequest loginRequest,
                                     HttpServletRequest httpRequest) throws BussinessException {

        // 创建认证请求
        AuthRequest authRequest = AuthRequest.builder()
                                .userType(UserPrincipal.UserType.SASS_CLIENT_ACCOUNT)
                                .authenticationType(AuthRequest.AuthenticationType.PHONE_PASSWORD)
                                .username(loginRequest.getUsername())
                                .password(loginRequest.getPassword())
                                .build()
                                .fillHttpRequestParams(httpRequest);

        // 执行认证
        AuthResponse authResponse = authenticationManager.authenticate(authRequest);
        return ResponseEntity.ok(authResponse);
    }

    /**
     * 发送短信验证码   
     */
    @PostMapping("/sms-send")
    public ResponseEntity<AuthResponse> sendSmsCode(@RequestParam("phone") String phone) {
       
        return ResponseEntity.ok(null);
    }

    /**
     * 手机号登录
     * 使用手机号和短信验证码进行登录
     */
    @PostMapping("/auth/phone-code")
    public ResponseEntity<AuthResponse> phoneLogin(@RequestBody @Valid PhoneLoginRequest request, 
                                        HttpServletRequest httpRequest) {
       
        return ResponseEntity.ok(null);
    }

    /**
     * 微信授权登录
     * 使用微信授权码进行登录
     */
    @PostMapping("/auth/wechat")
    public ResponseEntity<AuthResponse> wechatLogin(@RequestBody @Valid WechatLoginRequest request, 
                                         HttpServletRequest httpRequest) {
       
        return ResponseEntity.ok(null);
    }


}
