# 美姿姿 - Admin API 服务配置
# 管理后台API服务，用于系统管理和配置
# 适用于部署到专门的管理服务器
version: '3.8'

services:
  # Admin API服务
  admin-api:
    build:
      context: ..
      dockerfile: docker/Dockerfile.admin-api
    image: beautiful-posture/admin-api:${VERSION:-latest}
    container_name: beautiful-posture-admin-api
    ports:
      - "${ADMIN_API_PORT:-8080}:8080"
    environment:
      # Spring配置
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES:-production}
      SERVER_PORT: 8080
      
      # 数据库配置 - 连接到远程数据库服务器
      SPRING_DATASOURCE_URL: ${DATABASE_URL:-********************************************************}
      SPRING_DATASOURCE_USERNAME: ${DATABASE_USERNAME:-postgres}
      SPRING_DATASOURCE_PASSWORD: ${DATABASE_PASSWORD:-postgres123}
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: org.postgresql.Driver
      
      # 连接池配置
      SPRING_DATASOURCE_HIKARI_MAXIMUM_POOL_SIZE: ${DB_POOL_SIZE:-20}
      SPRING_DATASOURCE_HIKARI_MINIMUM_IDLE: ${DB_MIN_IDLE:-5}
      SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT: 30000
      SPRING_DATASOURCE_HIKARI_IDLE_TIMEOUT: 600000
      SPRING_DATASOURCE_HIKARI_MAX_LIFETIME: 1800000
      
      # Redis配置 - 连接到远程Redis服务器
      SPRING_REDIS_HOST: ${REDIS_HOST:-redis-server}
      SPRING_REDIS_PORT: ${REDIS_PORT:-6379}
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      SPRING_REDIS_DATABASE: ${REDIS_DATABASE:-0}
      SPRING_REDIS_TIMEOUT: 5000ms
      
      # Redis连接池配置
      SPRING_REDIS_LETTUCE_POOL_MAX_ACTIVE: 20
      SPRING_REDIS_LETTUCE_POOL_MAX_IDLE: 10
      SPRING_REDIS_LETTUCE_POOL_MIN_IDLE: 5
      SPRING_REDIS_LETTUCE_POOL_MAX_WAIT: -1ms
      
      # JVM配置
      JAVA_OPTS: ${JAVA_OPTS:--Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication}
      
      # 应用配置
      APP_NAME: beautiful-posture-admin-api
      APP_VERSION: ${VERSION:-1.0.0}
      
      # 日志配置
      LOGGING_LEVEL_ROOT: ${LOG_LEVEL:-INFO}
      LOGGING_LEVEL_COM_DEEPAIC: ${APP_LOG_LEVEL:-DEBUG}
      LOGGING_FILE_NAME: /app/logs/admin-api.log
      
      # 监控配置
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,info,metrics,prometheus
      MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: when_authorized
      MANAGEMENT_METRICS_EXPORT_PROMETHEUS_ENABLED: true
      
      # 安全配置
      SECURITY_JWT_SECRET: ${JWT_SECRET:-your-secret-key-change-in-production}
      SECURITY_JWT_EXPIRATION: ${JWT_EXPIRATION:-86400}
      
      # 多租户配置
      TENANT_DEFAULT_SCHEMA: ${DEFAULT_TENANT_SCHEMA:-public}
      TENANT_RESOLVER_TYPE: ${TENANT_RESOLVER:-header}
      
      # 文件上传配置
      SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE: ${MAX_FILE_SIZE:-10MB}
      SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE: ${MAX_REQUEST_SIZE:-50MB}
      
    volumes:
      # 日志目录
      - admin_api_logs:/app/logs
      # 配置文件目录（可选）
      - ./config/admin-api:/app/config
      # 临时文件目录
      - admin_api_temp:/tmp
      
    networks:
      - beautiful-posture-network
      
    restart: unless-stopped
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
      
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1.5G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
          
    # 依赖外部服务（通过网络连接）
    external_links:
      - postgres-server:postgres
      - redis-server:redis
      
    # 标签
    labels:
      - "com.beautiful-posture.service=admin-api"
      - "com.beautiful-posture.version=${VERSION:-latest}"
      - "com.beautiful-posture.environment=${ENVIRONMENT:-production}"

volumes:
  admin_api_logs:
    driver: local
  admin_api_temp:
    driver: local

networks:
  beautiful-posture-network:
    external: true  # 使用外部网络，与基础设施共享
