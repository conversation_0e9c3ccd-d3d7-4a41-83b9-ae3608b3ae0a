# 美姿姿 - 统一账户架构设计

## 📋 问题分析

### 用户类型分析
美姿姿系统中存在三类用户：
1. **平台用户** - SaaS平台管理员，管理所有租户
2. **租户用户** - 租户内的管理员和员工，管理租户业务  
3. **会员用户** - 通过小程序等渠道注册的C端用户

### 存储策略对比

#### 方案一：单表存储
```sql
CREATE TABLE unified_account (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(100) NOT NULL,
    user_type ENUM('PLATFORM', 'CUSTOMER', 'MEMBER'),
    tenant_code VARCHAR(50),
    -- 其他字段...
);
```

#### 方案二：多表分离（推荐）
```sql
-- 平台用户表
CREATE TABLE pub_platform_account (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(100) NOT NULL,
    permission_level INT,
    -- 平台特有字段...
);

-- 租户用户表  
CREATE TABLE pub_customer_account (
    id BIGINT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(100) NOT NULL,
    tenant_code VARCHAR(50) NOT NULL,
    -- 租户特有字段...
    UNIQUE KEY uk_tenant_username (tenant_code, username)
);

-- 会员用户表
CREATE TABLE pub_member_account (
    id BIGINT PRIMARY KEY,
    login_type INT NOT NULL, -- 1:微信 2:手机 3:邮箱
    login_identifier VARCHAR(200) NOT NULL,
    tenant_code VARCHAR(50) NOT NULL,
    member_id BIGINT NOT NULL,
    -- 会员特有字段...
    UNIQUE KEY uk_tenant_login (tenant_code, login_type, login_identifier)
);
```

## ✅ 推荐方案：多表分离

### 优势分析

#### 1. **数据隔离和安全性**
- **平台用户**: 完全独立存储，最高安全级别
- **租户用户**: 按租户隔离，防止跨租户访问
- **会员用户**: 支持多种登录方式，灵活映射

#### 2. **性能优化**
```sql
-- 不同表可以有针对性的索引策略
-- 平台用户表
CREATE INDEX idx_platform_username ON pub_platform_account(username);
CREATE INDEX idx_platform_email ON pub_platform_account(email);

-- 租户用户表  
CREATE INDEX idx_customer_tenant_username ON pub_customer_account(tenant_code, username);
CREATE INDEX idx_customer_email ON pub_customer_account(email);

-- 会员用户表
CREATE INDEX idx_member_tenant_login ON pub_member_account(tenant_code, login_type, login_identifier);
CREATE INDEX idx_member_tenant_member ON pub_member_account(tenant_code, member_id);
```

#### 3. **扩展性强**
- 每种用户类型可以有独特的字段和约束
- 便于后续功能扩展和优化
- 支持不同的业务逻辑

#### 4. **权限控制清晰**
```java
// 不同用户类型有不同的权限验证逻辑
@SaCheckRole("platform-admin")     // 平台用户
@SaCheckRole("tenant-admin")       // 租户用户  
@SaCheckRole("member")             // 会员用户
```

### 架构设计

#### 1. **统一认证服务**
```java
@Service
public class UnifiedAuthService {
    
    @Autowired
    private PlatformAuthService platformAuthService;
    
    @Autowired  
    private CustomerAuthService customerAuthService;
    
    @Autowired
    private MemberAuthService memberAuthService;
    
    public AuthResponse authenticate(AuthRequest request) {
        switch (request.getUserType()) {
            case PLATFORM:
                return platformAuthService.authenticate(request);
            case CUSTOMER:
                return customerAuthService.authenticate(request);
            case MEMBER:
                return memberAuthService.authenticate(request);
            default:
                throw new IllegalArgumentException("不支持的用户类型");
        }
    }
}
```

#### 2. **统一DTO设计**
```java
@Data
public class UnifiedAccountDTO {
    private Long id;
    private AccountType accountType; // PLATFORM, CUSTOMER, MEMBER
    private String username;
    private String email;
    private String phone;
    private String realName;
    private String avatar;
    private Integer status;
    
    // 租户相关（仅租户用户和会员用户）
    private String tenantCode;
    private String tenantName;
    
    // 会员相关（仅会员用户）
    private Long memberId;
    private String memberNo;
    private Integer loginType;
    
    // 平台相关（仅平台用户）
    private Integer permissionLevel;
    
    // 通用字段
    private LocalDateTime lastLoginTime;
    private String lastLoginIp;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    public enum AccountType {
        PLATFORM("平台用户"),
        CUSTOMER("租户用户"),
        MEMBER("会员用户");
        
        private final String description;
        
        AccountType(String description) {
            this.description = description;
        }
    }
}
```

#### 3. **统一查询接口**
```java
@RestController
@RequestMapping("/api/unified/account")
public class UnifiedAccountController {
    
    @Autowired
    private UnifiedAccountService unifiedAccountService;
    
    /**
     * 根据用户类型和标识查询账户
     */
    @GetMapping("/search")
    public ResponseEntity<UnifiedAccountDTO> searchAccount(
            @RequestParam AccountType accountType,
            @RequestParam String identifier,
            @RequestParam(required = false) String tenantCode) {
        
        UnifiedAccountDTO account = unifiedAccountService.searchAccount(
            accountType, identifier, tenantCode);
        return ResponseEntity.ok(account);
    }
    
    /**
     * 统一登录接口
     */
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@RequestBody UnifiedLoginRequest request) {
        AuthResponse response = unifiedAccountService.login(request);
        return ResponseEntity.ok(response);
    }
}
```

### 实现建议

#### 1. **保持现有设计**
您当前的多表分离设计是正确的，建议保持：
- `pub_platform_account` - 平台用户
- `pub_customer_account` - 租户用户
- `pub_member_account` - 会员用户

#### 2. **增强统一服务层**
```java
@Service
public class UnifiedAccountService {
    
    public UnifiedAccountDTO convertToUnified(Object account, AccountType type) {
        UnifiedAccountDTO dto = new UnifiedAccountDTO();
        dto.setAccountType(type);
        
        switch (type) {
            case PLATFORM:
                PlatformAccount platform = (PlatformAccount) account;
                dto.setId(platform.getId());
                dto.setUsername(platform.getUsername());
                dto.setPermissionLevel(platform.getPermissionLevel());
                // ... 其他字段映射
                break;
                
            case CUSTOMER:
                CustomerAccount customer = (CustomerAccount) account;
                dto.setId(customer.getId());
                dto.setUsername(customer.getUsername());
                dto.setTenantCode(customer.getTenantCode());
                // ... 其他字段映射
                break;
                
            case MEMBER:
                MemberAccount member = (MemberAccount) account;
                dto.setId(member.getId());
                dto.setUsername(member.getLoginIdentifier());
                dto.setTenantCode(member.getTenantCode());
                dto.setMemberId(member.getMemberId());
                dto.setLoginType(member.getLoginType());
                // ... 其他字段映射
                break;
        }
        
        return dto;
    }
}
```

#### 3. **优化查询性能**
```java
// 使用缓存提升查询性能
@Cacheable(value = "account", key = "#type + ':' + #identifier + ':' + #tenantCode")
public UnifiedAccountDTO getAccount(AccountType type, String identifier, String tenantCode) {
    // 查询逻辑
}

// 批量查询优化
public List<UnifiedAccountDTO> batchGetAccounts(List<AccountQuery> queries) {
    // 按类型分组批量查询，减少数据库访问
}
```

## 🎯 总结

**强烈推荐继续使用多表分离的设计**，因为：

1. **✅ 安全性更高** - 不同类型用户完全隔离
2. **✅ 性能更好** - 针对性索引和查询优化  
3. **✅ 扩展性强** - 每种用户类型可独立演进
4. **✅ 维护性好** - 职责清晰，便于维护
5. **✅ 符合多租户架构** - 与现有架构完美契合

通过统一的服务层和DTO设计，可以在保持数据分离的同时，提供统一的业务接口，既保证了架构的清晰性，又提供了良好的开发体验。
