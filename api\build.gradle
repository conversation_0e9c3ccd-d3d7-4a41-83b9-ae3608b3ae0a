plugins {
    id 'java'
    alias(libs.plugins.spring.boot) apply false
    alias(libs.plugins.spring.dependency.management) apply false
}

// 全局配置
allprojects {
    group = 'com.deepaic'
    version = '0.0.1-SNAPSHOT'

    repositories {
        // 使用阿里云镜像，提升国内下载速度
        maven {
            name 'AliYunMaven'
            url 'https://maven.aliyun.com/repository/public/'
        }
        maven {
            name 'AliYunSpring'
            url 'https://maven.aliyun.com/repository/spring/'
        }
        maven {
            name 'AliYunSpringPlugin'
            url 'https://maven.aliyun.com/repository/spring-plugin/'
        }
        maven {
            name 'AliYunGradle'
            url 'https://maven.aliyun.com/repository/gradle-plugin/'
        }
        // 腾讯云镜像作为备用
        maven {
            name 'TencentMaven'
            url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/'
        }
        // 华为云镜像作为备用
        maven {
            name 'HuaweiMaven'
            url 'https://repo.huaweicloud.com/repository/maven/'
        }
        // 官方仓库作为最后备用
        mavenCentral()
        gradlePluginPortal()
    }
}

// 子项目通用配置
subprojects {
    apply plugin: 'java'

    // 只对API模块应用Spring Boot插件
    if (project.name.endsWith('-api')) {
        apply plugin: 'org.springframework.boot'
        apply plugin: 'io.spring.dependency-management'
    } else {
        // 对于core和service模块，只应用依赖管理
        apply plugin: 'io.spring.dependency-management'
    }

    java {
        // 使用Java 21版本
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    dependencies {
        // 工具类依赖
        compileOnly libs.lombok
        annotationProcessor libs.lombok
        annotationProcessor libs.mapstruct.processor

        // 测试依赖
        testImplementation libs.bundles.test
        testRuntimeOnly libs.junit.platform.launcher
    }

    tasks.named('test') {
        useJUnitPlatform()
    }

    // 编译配置
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
        options.compilerArgs += ['-parameters']
    }

    // 测试任务编码配置
    tasks.withType(Test) {
        systemProperty 'file.encoding', 'UTF-8'
        systemProperty 'user.timezone', 'Asia/Shanghai'
    }

    // Javadoc编码配置
    tasks.withType(Javadoc) {
        options.encoding = 'UTF-8'
        options.charSet = 'UTF-8'
    }
}

// 为API模块添加额外的通用依赖和打包配置
configure(subprojects.findAll { it.name.endsWith('-api') }) {
    dependencies {
        implementation libs.spring.boot.starter
    }

    // Spring Boot打包配置
    tasks.named('bootJar') {
        enabled = true
        archiveClassifier = ''

        // 设置JAR文件名
        archiveBaseName = project.name
        archiveVersion = project.version

        // 包含依赖
        duplicatesStrategy = DuplicatesStrategy.EXCLUDE

        // 设置主类（如果需要的话）
        // mainClass = 'com.deepaic.Application'
    }

    // 禁用普通JAR任务，只使用bootJar
    tasks.named('jar') {
        enabled = false
    }
}

// 为库模块配置JAR打包
configure(subprojects.findAll { !it.name.endsWith('-api') }) {
    tasks.named('jar') {
        enabled = true
        archiveClassifier = ''

        // 设置JAR文件名
        archiveBaseName = project.name
        archiveVersion = project.version

        // 包含源码
        from sourceSets.main.allSource
    }
}

// 根项目打包任务
task buildAll {
    group = 'build'
    description = '构建所有模块'
    dependsOn subprojects.collect { it.tasks.matching { task ->
        task.name == 'bootJar' || task.name == 'jar'
    }}
}

task cleanAll {
    group = 'build'
    description = '清理所有模块'
    dependsOn subprojects.collect { it.tasks.clean }
}