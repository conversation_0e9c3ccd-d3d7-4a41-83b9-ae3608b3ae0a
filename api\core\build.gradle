// Core模块通常不需要Spring Boot启动器，只需要基础依赖
plugins {
    id 'java-library'  // 使用java-library插件而不是Spring Boot插件
}

// Core 模块特定配置 - 公共模块，不需要Spring Boot启动器
description = 'Beautiful Posture Core Module - Common utilities and entities'

dependencies {
    // Web相关依赖 - 提供Servlet API和Spring Web MVC
    api libs.spring.boot.starter.web

    // 数据访问相关 - MyBatis-Plus会自动引入Spring相关依赖
    api libs.mybatis.plus.spring.boot3.starter
    api libs.mysql.connector
    api libs.postgresql.connector
    // 使用Spring Boot默认的HikariCP连接池，无需额外依赖

    // MyBatis-Plus代码生成器（可选，用于开发时生成代码）
    implementation libs.mybatis.plus.generator
    implementation libs.spring.boot.starter.freemarker

    // Lombok - 使用api暴露给其他模块，这样依赖core的模块也能使用lombok
    api libs.lombok
    annotationProcessor libs.lombok

    // 其他工具类 - 使用api暴露给其他模块
    api libs.mapstruct
    annotationProcessor libs.mapstruct.processor
    api libs.hutool.all
    api libs.fastjson2

    // 验证相关
    api libs.spring.boot.starter.validation

    // Sa-Token 权限认证框架
    api 'cn.dev33:sa-token-spring-boot3-starter:1.44.0'
    api 'cn.dev33:sa-token-redis-jackson:1.44.0'
}

// JAR打包配置
jar {
    archiveFileName = 'beautiful-posture-core.jar'

    // 设置重复文件处理策略
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE

    // 设置MANIFEST
    manifest {
        attributes(
            'Implementation-Title': project.name,
            'Implementation-Version': project.version,
            'Implementation-Vendor': 'Beautiful Posture Team'
        )
    }
}
