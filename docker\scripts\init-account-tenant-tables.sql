-- 美姿姿项目 - 公共账户和租户表初始化脚本
-- 创建pub_account和pub_tenant表，存储在public schema中
-- pub_前缀表示公共表，用于跨租户的用户登录和租户管理

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建租户表（公共表）
CREATE TABLE IF NOT EXISTS public.pub_tenant (
    id BIGSERIAL PRIMARY KEY,
    tenant_code VARCHAR(50) NOT NULL UNIQUE COMMENT '租户代码',
    tenant_name VARCHAR(100) NOT NULL COMMENT '租户名称',
    tenant_short_name VARCHAR(50) COMMENT '租户简称',
    schema_name VARCHAR(63) NOT NULL UNIQUE COMMENT '数据库Schema名称',
    tenant_type INTEGER DEFAULT 2 COMMENT '租户类型 1:企业版 2:标准版 3:基础版 4:试用版',
    status INTEGER DEFAULT 1 COMMENT '租户状态 0:禁用 1:启用 2:暂停 3:过期',
    contact_name VARCHAR(50) COMMENT '联系人姓名',
    contact_phone VARCHAR(20) COMMENT '联系人电话',
    contact_email VARCHAR(100) COMMENT '联系人邮箱',
    address VARCHAR(200) COMMENT '公司地址',
    website VARCHAR(200) COMMENT '公司网站',
    logo_url VARCHAR(500) COMMENT '租户Logo URL',
    max_users INTEGER DEFAULT 100 COMMENT '最大用户数限制',
    current_users INTEGER DEFAULT 0 COMMENT '当前用户数',
    storage_limit BIGINT DEFAULT 10000 COMMENT '存储空间限制(MB)',
    storage_used BIGINT DEFAULT 0 COMMENT '已使用存储空间(MB)',
    service_start_time TIMESTAMP COMMENT '服务开始时间',
    service_end_time TIMESTAMP COMMENT '服务结束时间',
    trial_start_time TIMESTAMP COMMENT '试用期开始时间',
    trial_end_time TIMESTAMP COMMENT '试用期结束时间',
    custom_domain VARCHAR(100) COMMENT '自定义域名',
    config_json TEXT COMMENT '配置信息(JSON格式)',
    remark VARCHAR(500) COMMENT '备注',
    company_name VARCHAR(200) COMMENT '企业全称',
    credit_code VARCHAR(50) COMMENT '统一社会信用代码',
    industry_type INTEGER COMMENT '行业类型',
    company_size INTEGER COMMENT '企业规模',
    description TEXT COMMENT '企业描述',
    contact_position VARCHAR(50) COMMENT '联系人职位',
    expected_user_count INTEGER COMMENT '预期用户数量',
    package_type VARCHAR(50) COMMENT '套餐类型',
    registration_source VARCHAR(50) COMMENT '注册来源',
    referral_code VARCHAR(50) COMMENT '推荐人编码',
    audit_status INTEGER DEFAULT 0 COMMENT '审核状态(0:待审核 1:已通过 2:已拒绝 3:自动通过)',
    audit_time TIMESTAMP COMMENT '审核时间',
    audit_by BIGINT COMMENT '审核人',
    audit_remark VARCHAR(500) COMMENT '审核备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标记 0:未删除 1:已删除',
    version INTEGER DEFAULT 1 COMMENT '版本号'
);

-- 创建账户表（公共表，用于登录认证）
CREATE TABLE IF NOT EXISTS public.pub_customer_account (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '账户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(500) COMMENT '头像URL',
    tenant_code VARCHAR(50) NOT NULL COMMENT '所属租户代码',
    status INTEGER DEFAULT 1 COMMENT '账户状态 0:禁用 1:启用 2:锁定',
    account_type INTEGER DEFAULT 3 COMMENT '账户类型 1:超级管理员 2:租户管理员 3:普通用户',
    last_login_time TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    login_fail_count INTEGER DEFAULT 0 COMMENT '登录失败次数',
    lock_time TIMESTAMP COMMENT '账户锁定时间',
    password_expire_time TIMESTAMP COMMENT '密码过期时间',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标记 0:未删除 1:已删除',
    version INTEGER DEFAULT 1 COMMENT '版本号'
);

-- 创建索引
-- 租户表索引
CREATE INDEX IF NOT EXISTS idx_pub_tenant_code ON public.pub_tenant(tenant_code);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_name ON public.pub_tenant(tenant_name);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_schema ON public.pub_tenant(schema_name);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_status ON public.pub_tenant(status);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_type ON public.pub_tenant(tenant_type);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_create_time ON public.pub_tenant(create_time);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_service_end ON public.pub_tenant(service_end_time);
CREATE INDEX IF NOT EXISTS idx_pub_tenant_trial_end ON public.pub_tenant(trial_end_time);

-- 客户账户表索引
CREATE INDEX IF NOT EXISTS idx_pub_customer_account_username ON public.pub_customer_account(username);
CREATE INDEX IF NOT EXISTS idx_pub_customer_account_email ON public.pub_customer_account(email);
CREATE INDEX IF NOT EXISTS idx_pub_customer_account_phone ON public.pub_customer_account(phone);
CREATE INDEX IF NOT EXISTS idx_pub_customer_account_tenant_code ON public.pub_customer_account(tenant_code);
CREATE INDEX IF NOT EXISTS idx_pub_customer_account_status ON public.pub_customer_account(status);
CREATE INDEX IF NOT EXISTS idx_pub_customer_account_type ON public.pub_customer_account(account_type);
CREATE INDEX IF NOT EXISTS idx_pub_customer_account_create_time ON public.pub_customer_account(create_time);
CREATE INDEX IF NOT EXISTS idx_pub_customer_account_last_login ON public.pub_customer_account(last_login_time);

-- 添加外键约束
ALTER TABLE public.pub_customer_account
ADD CONSTRAINT fk_pub_customer_account_tenant_code
FOREIGN KEY (tenant_code) REFERENCES public.pub_tenant(tenant_code)
ON DELETE RESTRICT ON UPDATE CASCADE;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为租户表创建更新时间触发器
DROP TRIGGER IF EXISTS update_pub_tenant_updated_time ON public.pub_tenant;
CREATE TRIGGER update_pub_tenant_updated_time
    BEFORE UPDATE ON public.pub_tenant
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 为客户账户表创建更新时间触发器
DROP TRIGGER IF EXISTS update_pub_customer_account_updated_time ON public.pub_customer_account;
CREATE TRIGGER update_pub_customer_account_updated_time
    BEFORE UPDATE ON public.pub_customer_account
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 插入默认租户数据
INSERT INTO public.pub_tenant (
    tenant_code, tenant_name, tenant_short_name, schema_name, tenant_type, status,
    contact_name, contact_email, max_users, storage_limit,
    service_start_time, service_end_time, remark
) VALUES
(
    'default', '默认租户', '默认', 'public', 1, 1,
    '系统管理员', '<EMAIL>', 1000, 100000,
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '10 years', '系统默认租户'
),
(
    'demo', '演示租户', '演示', 'tenant_demo', 4, 1,
    '演示用户', '<EMAIL>', 10, 1000,
    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days', '演示租户，用于产品展示'
) ON CONFLICT (tenant_code) DO NOTHING;

-- 插入默认账户数据
INSERT INTO public.pub_customer_account (
    username, password, email, real_name, tenant_code, status, account_type,
    password_expire_time, remark
) VALUES
(
    'admin', 'YWRtaW4xMjM=', -- 使用简单编码的密码: admin123
    '<EMAIL>', '系统管理员', 'default', 1, 1,
    CURRENT_TIMESTAMP + INTERVAL '1 year', '系统超级管理员账户'
),
(
    'demo', 'YWRtaW4xMjM=', -- 使用简单编码的密码: admin123
    '<EMAIL>', '演示用户', 'demo', 1, 2,
    CURRENT_TIMESTAMP + INTERVAL '30 days', '演示租户管理员账户'
) ON CONFLICT (username) DO NOTHING;

-- 创建演示租户的schema
CREATE SCHEMA IF NOT EXISTS tenant_demo;

-- 在演示租户schema中创建示例业务表
CREATE TABLE IF NOT EXISTS tenant_demo.business_data (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '名称',
    description TEXT COMMENT '描述',
    status INTEGER DEFAULT 1 COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标记',
    version INTEGER DEFAULT 1 COMMENT '版本号'
);

-- 为演示业务表创建索引
CREATE INDEX IF NOT EXISTS idx_tenant_demo_business_data_status ON tenant_demo.business_data(status);
CREATE INDEX IF NOT EXISTS idx_tenant_demo_business_data_create_time ON tenant_demo.business_data(create_time);

-- 为演示业务表创建更新时间触发器
DROP TRIGGER IF EXISTS update_tenant_demo_business_data_updated_time ON tenant_demo.business_data;
CREATE TRIGGER update_tenant_demo_business_data_updated_time
    BEFORE UPDATE ON tenant_demo.business_data
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 插入演示数据
INSERT INTO tenant_demo.business_data (name, description, status) VALUES 
('演示数据1', '这是演示租户的第一条业务数据', 1),
('演示数据2', '这是演示租户的第二条业务数据', 1),
('演示数据3', '这是演示租户的第三条业务数据', 0)
ON CONFLICT DO NOTHING;

-- 更新租户用户数量
UPDATE public.pub_tenant SET current_users = (
    SELECT COUNT(*) FROM public.pub_customer_account
    WHERE pub_customer_account.tenant_code = pub_tenant.tenant_code
    AND pub_customer_account.deleted = 0
);

-- 在演示租户schema中创建sys_user表（租户用户表）
CREATE TABLE IF NOT EXISTS tenant_demo.sys_user (
    id BIGSERIAL PRIMARY KEY,
    account_id BIGINT NOT NULL COMMENT '关联的账户ID(pub_customer_account.id)',
    user_code VARCHAR(50) NOT NULL UNIQUE COMMENT '用户编码',
    nickname VARCHAR(50) COMMENT '昵称',
    gender INTEGER DEFAULT 0 COMMENT '性别 0:未知 1:男 2:女',
    birthday DATE COMMENT '生日',
    department_id BIGINT COMMENT '部门ID',
    position VARCHAR(100) COMMENT '职位',
    employee_no VARCHAR(50) COMMENT '员工编号',
    entry_date DATE COMMENT '入职日期',
    status INTEGER DEFAULT 1 COMMENT '用户状态 0:禁用 1:启用',
    sort_order INTEGER DEFAULT 0 COMMENT '排序',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标记 0:未删除 1:已删除',
    version INTEGER DEFAULT 1 COMMENT '版本号'
);

-- 为sys_user表创建索引
CREATE INDEX IF NOT EXISTS idx_tenant_demo_sys_user_account_id ON tenant_demo.sys_user(account_id);
CREATE INDEX IF NOT EXISTS idx_tenant_demo_sys_user_code ON tenant_demo.sys_user(user_code);
CREATE INDEX IF NOT EXISTS idx_tenant_demo_sys_user_department ON tenant_demo.sys_user(department_id);
CREATE INDEX IF NOT EXISTS idx_tenant_demo_sys_user_status ON tenant_demo.sys_user(status);
CREATE INDEX IF NOT EXISTS idx_tenant_demo_sys_user_create_time ON tenant_demo.sys_user(create_time);

-- 为sys_user表创建更新时间触发器
DROP TRIGGER IF EXISTS update_tenant_demo_sys_user_updated_time ON tenant_demo.sys_user;
CREATE TRIGGER update_tenant_demo_sys_user_updated_time
    BEFORE UPDATE ON tenant_demo.sys_user
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 插入演示用户数据
INSERT INTO tenant_demo.sys_user (account_id, user_code, nickname, gender, position, status, remark) VALUES
(2, 'demo_user', '演示用户', 1, '产品经理', 1, '演示租户的用户信息')
ON CONFLICT (user_code) DO NOTHING;

-- 会员账户表 - 存储在公共schema中
-- 用于解决多租户环境下的会员登录问题
CREATE TABLE IF NOT EXISTS public.pub_member_account (
    id BIGSERIAL PRIMARY KEY,
    login_type INTEGER NOT NULL COMMENT '登录类型(1:微信OpenID 2:手机号 3:邮箱)',
    login_identifier VARCHAR(200) NOT NULL COMMENT '登录标识(OpenID/手机号/邮箱)',
    tenant_code VARCHAR(50) NOT NULL COMMENT '租户编码',
    member_id BIGINT NOT NULL COMMENT '租户内会员ID',
    member_no VARCHAR(50) NOT NULL COMMENT '会员编号',
    status INTEGER DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 创建会员账户表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_pub_member_account ON public.pub_member_account(login_type, login_identifier) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_pub_member_account_tenant_code ON public.pub_member_account(tenant_code);
CREATE INDEX IF NOT EXISTS idx_pub_member_account_member_id ON public.pub_member_account(member_id);
CREATE INDEX IF NOT EXISTS idx_pub_member_account_status ON public.pub_member_account(status);
CREATE INDEX IF NOT EXISTS idx_pub_member_account_deleted ON public.pub_member_account(deleted);

-- 为会员账户表创建更新时间触发器
DROP TRIGGER IF EXISTS update_pub_member_account_updated_time ON public.pub_member_account;
CREATE TRIGGER update_pub_member_account_updated_time
    BEFORE UPDATE ON public.pub_member_account
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 平台账户表 - SaaS平台管理账户
CREATE TABLE IF NOT EXISTS public.pub_platform_account (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(500) COMMENT '头像URL',
    status INTEGER DEFAULT 1 COMMENT '账户状态(0:禁用 1:启用 2:锁定)',
    account_type INTEGER DEFAULT 2 COMMENT '账户类型(1:超级管理员 2:平台管理员 3:运营人员 4:技术支持)',
    permission_level INTEGER DEFAULT 1 COMMENT '权限级别',
    last_login_time TIMESTAMP COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    login_fail_count INTEGER DEFAULT 0 COMMENT '登录失败次数',
    lock_time TIMESTAMP COMMENT '账户锁定时间',
    password_expire_time TIMESTAMP COMMENT '密码过期时间',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by BIGINT COMMENT '创建人',
    update_by BIGINT COMMENT '更新人',
    version INTEGER DEFAULT 1 COMMENT '版本号',
    deleted INTEGER DEFAULT 0 COMMENT '删除标志(0:未删除 1:已删除)'
);

-- 平台账户表索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_pub_platform_account_username ON public.pub_platform_account(username) WHERE deleted = 0;
CREATE INDEX IF NOT EXISTS idx_pub_platform_account_email ON public.pub_platform_account(email);
CREATE INDEX IF NOT EXISTS idx_pub_platform_account_phone ON public.pub_platform_account(phone);
CREATE INDEX IF NOT EXISTS idx_pub_platform_account_status ON public.pub_platform_account(status);
CREATE INDEX IF NOT EXISTS idx_pub_platform_account_type ON public.pub_platform_account(account_type);
CREATE INDEX IF NOT EXISTS idx_pub_platform_account_create_time ON public.pub_platform_account(create_time);
CREATE INDEX IF NOT EXISTS idx_pub_platform_account_deleted ON public.pub_platform_account(deleted);

-- 为平台账户表创建更新时间触发器
DROP TRIGGER IF EXISTS update_pub_platform_account_updated_time ON public.pub_platform_account;
CREATE TRIGGER update_pub_platform_account_updated_time
    BEFORE UPDATE ON public.pub_platform_account
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_time_column();

-- 插入默认平台账户数据
INSERT INTO public.pub_platform_account (
    username, password, email, real_name, status, account_type, permission_level, remark
) VALUES
(
    'superadmin',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR8PqPHDOjKpF8QK6pzINIy', -- 密码: admin123
    '<EMAIL>',
    '超级管理员',
    1,
    1,
    10,
    '系统超级管理员账户'
),
(
    'admin',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR8PqPHDOjKpF8QK6pzINIy', -- 密码: admin123
    '<EMAIL>',
    '平台管理员',
    1,
    2,
    5,
    '平台管理员账户'
),
(
    'operator',
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR8PqPHDOjKpF8QK6pzINIy', -- 密码: admin123
    '<EMAIL>',
    '运营人员',
    1,
    3,
    3,
    '平台运营人员账户'
)
ON CONFLICT (username) DO NOTHING;

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '=== 美姿姿项目账户和租户表初始化完成 ===';
    RAISE NOTICE '公共表: pub_platform_account (平台账户), pub_customer_account (客户账户), pub_tenant (租户管理)';
    RAISE NOTICE '公共表: pub_member_account (会员账户)';
    RAISE NOTICE '租户表: sys_user (租户用户信息), sys_member (会员信息)';
    RAISE NOTICE '默认平台账户: superadmin / admin123 (超级管理员)';
    RAISE NOTICE '默认平台账户: admin / admin123 (平台管理员)';
    RAISE NOTICE '默认客户账户: admin / admin123 (租户管理员)';
    RAISE NOTICE '演示客户账户: demo / admin123 (演示账户)';
    RAISE NOTICE '默认租户: default (使用public schema)';
    RAISE NOTICE '演示租户: demo (使用tenant_demo schema)';
    RAISE NOTICE '============================================';
END $$;
