# 美姿姿 - 通用配置文件
# 所有API模块共享的基础配置

spring:
  # 数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:*************}:${DB_PORT:5432}/${DB_NAME:mzz}?currentSchema=${DB_SCHEMA:public}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:deepaic!2025}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      pool-name: HikariCP-PostgreSQL
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      # password: ${REDIS_PASSWORD:}  # 本地开发环境不设置密码
      database: ${REDIS_DATABASE:0}
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_id
      logic-delete-field: deleted
      logic-delete-value: true
      logic-not-delete-value: false
  mapper-locations: classpath*:mapper/**/*.xml

# Sa-Token配置
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: satoken
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true

# 日志配置
logging:
  level:
    com.beautifulposture: debug
    com.deepaic: debug
    org.springframework.web: info
    com.baomidou.mybatisplus: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  info:
    java:
      enabled: true
    os:
      enabled: true

# 应用信息
info:
  app:
    name: 美姿姿健康管理系统
    description: Beautiful Posture Health Management System
    version: 1.0.0
    java-version: 21
    team: 美姿姿团队
