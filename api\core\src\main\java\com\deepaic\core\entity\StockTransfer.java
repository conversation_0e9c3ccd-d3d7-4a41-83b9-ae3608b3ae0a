package com.deepaic.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

/**
 * <p>
 * 调拨表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("sys_stock_transfer")
public class StockTransfer extends BaseEntity {

    /**
     * 调拨单号
     */
    private String code;

    /**
     * 调拨日期
     */
    private LocalDate transferDate;

    /**
     * 调出类型，门店或仓库
     */
    private Short outStoreWarehouseType;

    /**
     * 调出仓库或门店id
     */
    private Long outStoreWarehouseId;

    /**
     * 调入类型，门店或仓库
     */
    private Short inStoreWarehouseType;

    /**
     * 调入仓库或门店id
     */
    private Long inStoreWarehouseId;

    /**
     * 签字
     */
    private String signatureFile;

    private String remark;

    private Short status;
}
