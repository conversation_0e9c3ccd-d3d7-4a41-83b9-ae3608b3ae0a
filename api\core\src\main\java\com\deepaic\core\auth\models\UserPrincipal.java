package com.deepaic.core.auth.models;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户主体信息
 * 统一的用户身份信息模型
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPrincipal {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户类型
     */
    private UserType userType;

    /**
     * 用户名
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;


    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 租户编码（客户账户和会员账户）
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 权限列表
     */
    private List<String> authorities;

    /**
     * 角色列表
     */
    private List<String> roles;

    /**
     * 账户状态
     */
    private AccountStatus status;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;




    /**
     * 用户类型枚举
     */
    public enum UserType {
        PLATFORM_ACCOUNT,
        SASS_CLIENT_ACCOUNT,
        SASS_MEMBER_ACCOUNT;
    }

    /**
     * 账户状态枚举
     */
    public enum AccountStatus {
        ENABLED("enabled", "启用"),
        DISABLED("disabled", "禁用"),
        LOCKED("locked", "锁定"),
        EXPIRED("expired", "过期");

        private final String code;
        private final String description;

        AccountStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
    }

    /**
     * 创建平台账户主体
     */
    public static UserPrincipal platformAccount(String userId, String username, String realName) {
        return UserPrincipal.builder()
                .userId(userId)
                .userType(UserType.PLATFORM_ACCOUNT)
                .username(username)
                .realName(realName)
                .status(AccountStatus.ENABLED)
                .build();
    }

    /**
     * 创建客户账户主体
     */
    public static UserPrincipal customerAccount(String userId, String username, String realName, 
                                              String tenantCode, String tenantName) {
        return UserPrincipal.builder()
                .userId(userId)
                .userType(UserType.SASS_CLIENT_ACCOUNT)
                .username(username)
                .realName(realName)
                .tenantCode(tenantCode)
                .tenantName(tenantName)
                .status(AccountStatus.ENABLED)
                .build();
    }

    /**
     * 创建会员账户主体
     */
    public static UserPrincipal memberAccount(String userId, String username, String realName,
                                            String tenantCode, String tenantName) {
        return UserPrincipal.builder()
                .userId(userId)
                .userType(UserType.SASS_MEMBER_ACCOUNT)
                .username(username)
                .realName(realName)
                .tenantCode(tenantCode)
                .tenantName(tenantName)
                .status(AccountStatus.ENABLED)
                .build();
    }

    /**
     * 检查是否为平台账户
     */
    public boolean isPlatformAccount() {
        return UserType.PLATFORM_ACCOUNT.equals(userType);
    }

    /**
     * 检查是否为客户账户
     */
    public boolean isCustomerAccount() {
        return UserType.SASS_CLIENT_ACCOUNT.equals(userType);
    }

    /**
     * 检查是否为会员账户
     */
    public boolean isMemberAccount() {
        return UserType.SASS_MEMBER_ACCOUNT.equals(userType);
    }

    /**
     * 检查账户是否启用
     */
    public boolean isEnabled() {
        return AccountStatus.ENABLED.equals(status);
    }

    /**
     * 检查账户是否锁定
     */
    public boolean isLocked() {
        return AccountStatus.LOCKED.equals(status);
    }

    /**
     * 检查是否有指定权限
     */
    public boolean hasAuthority(String authority) {
        return authorities != null && authorities.contains(authority);
    }

    /**
     * 检查是否有指定角色
     */
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }
}
