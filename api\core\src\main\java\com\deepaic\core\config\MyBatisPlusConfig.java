package com.deepaic.core.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.deepaic.core.tenant.TenantSchemaInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * MyBatis Plus 配置类
 * 配置分页插件和多租户拦截器
 *
 * <AUTHOR>
 */
@Configuration
// @RequiredArgsConstructor  // 暂时注释掉，避免循环依赖
public class MyBatisPlusConfig {

    // private final @Lazy TenantSchemaInterceptor tenantSchemaInterceptor;

    /**
     * MyBatis Plus 拦截器配置
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // TODO: 暂时注释掉多租户拦截器，避免循环依赖
        // 多租户拦截器（优先级最高）
        // interceptor.addInnerInterceptor(tenantSchemaInterceptor);

        return interceptor;
    }
}
